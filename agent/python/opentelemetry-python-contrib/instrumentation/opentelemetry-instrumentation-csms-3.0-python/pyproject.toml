[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "zoom-opentelemetry-instrumentation-csms-3.0-python"
dynamic = ["version"]
description = "OpenTelemetry Csms-Python-3.0 instrumentation"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13"
]
dependencies = [
  "opentelemetry-api ~= 1.14",
  "opentelemetry-instrumentation == 0.57b0",
  "opentelemetry-semantic-conventions == 0.57b0",
]

[project.optional-dependencies]
instruments = [
  "zoom-python-csms >= 3.0.0",
]

[project.entry-points.opentelemetry_instrumentor]
csms_v3_0 = "opentelemetry.instrumentation.csms.v3_0:CsmsInstrumentorV3"

[tool.hatch.version]
path = "src/opentelemetry/instrumentation/csms/v3_0/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
