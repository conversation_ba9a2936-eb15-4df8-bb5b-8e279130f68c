import base64
import json
import re
from logging import getLogger
from typing import List, Optional

from opentelemetry import propagate
from opentelemetry.propagators import textmap
from opentelemetry.semconv.trace import (
    MessagingDestinationKindValues,
    MessagingOperationValues,
    SpanAttributes,
)
from opentelemetry.trace import Link, SpanKind
from zoom_asyncmq_gateway.asyncmq.task import Task, PayloadType

_LOG = getLogger(__name__)


class AsyncMQContextGetter(textmap.Getter):
    def get(self, carrier: textmap.CarrierT, key: str) -> Optional[List[str]]:
        if carrier is None:
            return None

        carrier_items = carrier
        if isinstance(carrier, dict):
            carrier_items = carrier.items()

        for item_key, value in carrier_items:
            if item_key == key:
                if value is not None:
                    if isinstance(value, bytes):
                        return [value.decode()]
                    else:
                        return [value]

        return None

    def keys(self, carrier: textmap.CarrierT) -> List[str]:
        if carrier is None:
            return []

        carrier_items = carrier
        if isinstance(carrier, dict):
            carrier_items = carrier.items()
        return [key for (key, value) in carrier_items]


class AsyncMQContextSetter(textmap.Setter):
    def set(self, carrier: textmap.CarrierT, key: str, value: str) -> None:
        if carrier is None or key is None:
            return

        if value:
            value = value.encode()

        if isinstance(value, bytes):
            value = value.decode("utf-8")

        if isinstance(carrier, list):
            carrier.append((key, value))

        if isinstance(carrier, dict):
            if not carrier.get(key):
                carrier.update({key: value})


_asyncmq_getter = AsyncMQContextGetter()


def _enrich_span(
        span,
        topic,
        partition: Optional[int] = None,
        offset: Optional[int] = None,
        operation: Optional[str] = None,
):
    if not span.is_recording():
        return

    span.set_attribute(SpanAttributes.MESSAGING_SYSTEM, "asyncmq")
    span.set_attribute(SpanAttributes.MESSAGING_DESTINATION, topic)

    if partition is not None:
        span.set_attribute("messaging.asyncmq.partition", partition)

    span.set_attribute(
        SpanAttributes.MESSAGING_DESTINATION_KIND,
        MessagingDestinationKindValues.QUEUE.value,
    )

    if operation:
        span.set_attribute(SpanAttributes.MESSAGING_OPERATION, operation)
    else:
        span.set_attribute(SpanAttributes.MESSAGING_DESTINATION_TEMPORARY, True)

    if partition is not None and offset is not None and topic:
        span.set_attribute(
            SpanAttributes.MESSAGING_MESSAGE_ID,
            f"{topic}.{partition}.{offset}",
        )


_asyncmq_setter = AsyncMQContextSetter()


def _get_span_name(operation: str, topic: str):
    return f"{topic} {operation}"


def convert_task(b_data: bytes) -> Task:
    udata = json.loads(b_data)
    # remove invalid context
    if "context" in udata:
        del udata["context"]

    snake_dict = {}
    for k, v in udata.items():
        snake_dict[camel_to_snake(k)] = v

    task: Task = Task(**snake_dict)
    # new protocol compatibility
    if task.payload_type == "":
        task.payload_type = None

    if task.payload_type is not None:
        task.payload_type = PayloadType(task.payload_type)
        task.payload = base64.b64decode(task.payload)
        if task.payload_type == PayloadType.String:
            task.payload = task.payload.decode("utf-8")
        elif task.payload_type == PayloadType.Object:
            task.payload = json.loads(task.payload.decode("utf-8"))
    return task


def camel_to_snake(name):
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
