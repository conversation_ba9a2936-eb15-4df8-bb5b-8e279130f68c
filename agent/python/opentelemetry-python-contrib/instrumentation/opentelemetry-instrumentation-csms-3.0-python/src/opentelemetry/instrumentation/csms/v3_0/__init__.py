# Copyright The OpenTelemetry Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import inspect
from typing import Collection, Optional, Callable

import wrapt

from opentelemetry.instrumentation.instrumentor import BaseInstrumentor
from opentelemetry.instrumentation.utils import unwrap

from .package import _instruments


class CsmsInstrumentorV3(BaseInstrumentor):
    """An instrumentor for csms module
    See `BaseInstrumentor`
    """

    _get_jwt_client_func: Optional[Callable] = None
    jwt_client = None

    def instrumentation_dependencies(self) -> Collection[str]:
        return _instruments

    def _instrument(self, **kwargs):

        def _inner_wrap_init(func, instance, args, kwargs):
            if not CsmsInstrumentorV3._get_jwt_client_func:
                CsmsInstrumentorV3._get_jwt_client_func = getattr(instance, "jwt_client")
            return func(*args, **kwargs)

        try:
            from zoom_python_csms.csms_sdk_client import CsmsSdkClient
            wrapt.wrap_function_wrapper(
                CsmsSdkClient,
                "__init__",
                _inner_wrap_init,
            )
        except Exception:
            pass

    def _uninstrument(self, **kwargs):
        try:
            from zoom_python_csms.csms_sdk_client import CsmsSdkClient
            unwrap(CsmsSdkClient, "__init__")
        except Exception:
            pass

    @staticmethod
    def get_jwt_client() -> Optional:
        if CsmsInstrumentorV3._get_jwt_client_func and not CsmsInstrumentorV3.jwt_client:
            CsmsInstrumentorV3.jwt_client = CsmsInstrumentorV3._get_jwt_client_func()
        return CsmsInstrumentorV3.jwt_client
