# Copyright The OpenTelemetry Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import dataclasses
import json
import threading
import time
from logging import getLogger
from typing import Collection, Any, Dict, Optional

import wrapt
from opentelemetry import context, propagate, trace
from opentelemetry.context import Context, get_current
from opentelemetry.instrumentation.instrumentor import BaseInstrumentor
from opentelemetry.instrumentation.utils import unwrap
from opentelemetry.semconv.trace import MessagingOperationValues, SpanAttributes

from .package import _instruments
from .utils import (
    _enrich_span,
    _asyncmq_getter, _asyncmq_setter,
)
from .version import __version__

can_instrument: bool = True
_logger = getLogger("cube-agent-log")
try:
    from zoom_asyncmq_gateway.apis.api import API
    from zoom_asyncmq_gateway.apis.api_abc import Ack_ABC
    from zoom_asyncmq_gateway.asyncmq.task import convert_task_entity, TaskEntity, TaskMetadata, TaskContext
    from zoom_asyncmq_gateway.protocol.event.publish import PublishData, PublishHeader, PublishResponse, PublishAckData
    from zoom_asyncmq_gateway.protocol.event.receive import ReceiveData
except Exception as e:
    can_instrument = False
    _logger.exception("import dependency for asyncmq failed")


@dataclasses.dataclass
class AsyncMQContext:
    ctx: Context
    create_time: int


class AsyncMQInstrumentor(BaseInstrumentor):
    """An instrumentor for asyncmq module
    See `BaseInstrumentor`
    """

    def __init__(self):
        self._ack_set = set()
        self._lock = threading.RLock()
        self._tracer = None

    def instrumentation_dependencies(self) -> Collection[str]:
        return _instruments

    def _instrument(self, **kwargs):
        tracer_provider = kwargs.get("tracer_provider")
        self._tracer = trace.get_tracer(
            __name__,
            __version__,
            tracer_provider=tracer_provider,
            schema_url="https://opentelemetry.io/schemas/1.11.0",
        )
        if not can_instrument:
            return

        def _inner_wrap_publish(func, instance, args, kwargs):
            return AsyncMQInstrumentor.wrap_publish(
                func, instance, self._tracer, args, kwargs
            )

        def _inner_wrap_consume(func, instance, args, kwargs):
            return AsyncMQInstrumentor.wrap_consume(
                func, instance, self._tracer, args, kwargs
            )

        def _inner_wrap_publish_ack(func, instance, args, kwargs):
            return AsyncMQInstrumentor.wrap_publish_ack(
                func, instance, self._tracer, args, kwargs
            )

        def _do_wrap_ack(ack: Ack_ABC):
            cls = ack.__class__
            if not cls:
                return
            wrapped_flag = "_opentelemetry_asyncmq_wrapped_" + __version__.replace(".", "")
            if hasattr(cls, wrapped_flag):
                return
            with self._lock:
                if hasattr(cls, wrapped_flag):
                    return
                wrapt.wrap_function_wrapper(
                    cls,
                    "publish",
                    _inner_wrap_publish_ack,
                )
                wrapt.wrap_function_wrapper(
                    cls,
                    "receive",
                    _inner_wrap_consume,
                )
                setattr(cls, wrapped_flag, True)
                self._ack_set.update([cls])

        def _wrapped_init(func, instance, args, kwargs):
            try:
                ack: Optional[Ack_ABC] = None
                for arg in args:
                    if hasattr(arg, "ack") and arg.ack:
                        ack = arg.ack
                        break
                    elif isinstance(arg, Ack_ABC) and arg:
                        ack = arg
                        break
                if not ack and kwargs.get("config"):
                    config = kwargs.get("config")
                    tmp_ack = kwargs.get("ack")
                    if tmp_ack:
                        ack = tmp_ack
                    elif hasattr(config, "ack") and config.ack:
                        ack = config.ack
                if ack:
                    _do_wrap_ack(ack)
            except Exception:
                _logger.exception("wrapp asyncmq API init failed")
            return func(*args, **kwargs)

        wrapt.wrap_function_wrapper(
            API,
            "__init__",
            _wrapped_init,
        )

        wrapt.wrap_function_wrapper(
            API,
            "publish",
            _inner_wrap_publish,
        )

    def _uninstrument(self, **kwargs):
        for ack_impl in self._ack_set:
            unwrap(ack_impl, "receive")
            unwrap(ack_impl, "publish")
        unwrap(API, "publish")

    @staticmethod
    async def wrap_publish(func, instance, tracer, args, kwargs):
        publish_data: PublishData = args[0] if len(args) >= 1 else kwargs.get("req")
        if not publish_data:
            return func(*args, **kwargs)
        publish_header: PublishHeader = publish_data.header

        ack = instance.ack
        if not hasattr(ack, "current_context_map"):
            ack.current_context_map = {}
        attributes = {}
        fill_attributes(attributes, publish_header.topic, None)
        with tracer.start_as_current_span(
                f"{publish_header.topic} publish", context=get_current(), end_on_exit=True, attributes=attributes,
                kind=trace.SpanKind.PRODUCER
        ) as span:
            asyncmq_context: AsyncMQContext = AsyncMQContext(ctx=context.get_current(),
                                                             create_time=int(time.time() * 1000))
            ack.current_context_map.update({publish_header.request_id: asyncmq_context})
            try:
                task_dict = json.loads(publish_data.data)
                if isinstance(task_dict, dict):
                    tracking_info: Dict[str, Any]
                    if task_dict.get("protocolStrategy") == "v1.0":
                        if not task_dict.get("extraInfo"):
                            task_dict.update({"extraInfo": {}})
                        tracking_info = task_dict.get("extraInfo")
                    else:
                        if not task_dict.get("trackingInfo"):
                            task_dict.update({"trackingInfo": {}})
                        tracking_info = task_dict.get("trackingInfo")
                    _enrich_span(
                        span,
                        operation=MessagingOperationValues.PUBLISH.value
                    )  # Replace
                    propagate.inject(
                        tracking_info,
                        setter=_asyncmq_setter
                    )
                    publish_data.data = json.dumps(task_dict).encode("utf-8")
            except Exception as err:
                pass
            await func(*args, **kwargs)

    @staticmethod
    def wrap_consume(func, instance, tracer, args, kwargs):
        receive_data: ReceiveData
        if len(args) < 2:
            receive_data = kwargs.get("response")
        else:
            receive_data = args[1]

        if not receive_data:
            return func(*args, **kwargs)
        task_entity: TaskEntity = convert_task_entity(receive_data)
        task_meta_data: TaskMetadata = task_entity.metadata
        task_context: TaskContext = task_entity.taskContext
        tracking_info: Dict[str, Any] = task_context.extraInfo \
            if task_entity.protocolVersion == "v1.0" else task_context.trackingInfo
        ctx: Context = propagate.extract(tracking_info, getter=_asyncmq_getter)
        attributes = {}
        fill_attributes(attributes, task_context.topicName, task_meta_data.partition)
        with tracer.start_as_current_span(
                f"{task_context.topicName} process", context=ctx, attributes=attributes, end_on_exit=True,
                kind=trace.SpanKind.CONSUMER
        ) as span:
            _enrich_span(span,
                         task_meta_data.partition, task_meta_data.offset,
                         operation=MessagingOperationValues.PROCESS.value,
                         task_id=task_context.taskId,
                         message_key=task_meta_data.key)
            return func(*args, **kwargs)

    @staticmethod
    def wrap_publish_ack(func, instance, tracer, args, kwargs):
        publish_response: PublishResponse
        if len(args) < 2:
            publish_response = kwargs.get("response")
        else:
            publish_response = args[1]

        if not publish_response:
            return func(*args, **kwargs)
        ack_data: PublishAckData = publish_response.data
        request_id = ack_data.request_id

        if not hasattr(instance, "current_context_map"):
            instance.current_context_map = {}

        asyncmq_context: AsyncMQContext = instance.current_context_map.get(request_id)
        if not asyncmq_context:
            return func(*args, **kwargs)
        attributes = {}
        fill_attributes(attributes, ack_data.topic, None)
        with tracer.start_as_current_span(
                f"{ack_data.topic} publish_ack", context=asyncmq_context.ctx, attributes=attributes, end_on_exit=True,
                kind=trace.SpanKind.INTERNAL
        ) as span:
            try:
                _enrich_span(span, operation=MessagingOperationValues.PUBLISH.value + "_ack")
                instance.current_context_map.pop(request_id, None)
                clear_expired_span(instance.current_context_map)
            except Exception as err:
                pass
            return func(*args, **kwargs)


def clear_expired_span(current_context_token_map: dict[str, AsyncMQContext]):
    if not current_context_token_map or len(current_context_token_map) == 0:
        return
    now = int(time.time() * 1000)
    max_delay_time = 1000 * 60 * 5
    current_context_token_map_copy = current_context_token_map.copy()
    for request_id, asyncmq_context in current_context_token_map_copy.items():
        delay = now - asyncmq_context.create_time
        if delay < max_delay_time:
            continue
        current_context_token_map.pop(request_id, None)


def fill_attributes(attributes, topic_name, partition):
    if topic_name:
        attributes[SpanAttributes.MESSAGING_DESTINATION_NAME] = topic_name
    if partition:
        attributes["messaging.asyncmq.partition"] = partition
