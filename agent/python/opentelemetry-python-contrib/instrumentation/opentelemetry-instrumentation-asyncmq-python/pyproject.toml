[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "zoom-opentelemetry-instrumentation-asyncmq-python"
dynamic = ["version"]
description = "OpenTelemetry Asyncmq-Python instrumentation"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13"
]
dependencies = [
  "opentelemetry-api ~= 1.14",
  "opentelemetry-instrumentation == 0.57b0",
  "opentelemetry-semantic-conventions == 0.57b0",
]

[project.optional-dependencies]
instruments = [
  "zoom-asyncmq-gateway >= 1.2.0",
]

[project.entry-points.opentelemetry_instrumentor]
asyncmq = "opentelemetry.instrumentation.asyncmq:AsyncMQInstrumentor"

[tool.hatch.version]
path = "src/opentelemetry/instrumentation/asyncmq/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src",
  "/tests",
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
