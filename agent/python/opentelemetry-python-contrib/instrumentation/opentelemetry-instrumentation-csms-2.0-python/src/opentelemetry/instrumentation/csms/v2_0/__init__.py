# Copyright The OpenTelemetry Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
from opentelemetry.instrumentation.utils import unwrap
from typing import Collection, Optional, Callable

import wrapt

from opentelemetry.instrumentation.instrumentor import BaseInstrumentor

from .package import _instruments


class CsmsInstrumentorV2(BaseInstrumentor):
    """An instrumentor for csms module
    See `BaseInstrumentor`
    """

    _get_secret_func: Optional[Callable[[list], dict]] = None

    def instrumentation_dependencies(self) -> Collection[str]:
        return _instruments

    def _instrument(self, **kwargs):

        def _inner_wrap_get_secret(func, instance, args, kwargs):
            if not CsmsInstrumentorV2._get_secret_func:
                CsmsInstrumentorV2._get_secret_func = getattr(instance, "get_secret_no_cahce")
            return func(*args, **kwargs)

        try:
            from zoom_python_csms.CSMSRotateSecret import CSMSSecretService
            wrapt.wrap_function_wrapper(
                CSMSSecretService,
                "__init__",
                _inner_wrap_get_secret,
            )
        except Exception:
            pass
    def _uninstrument(self, **kwargs):
        try:
            from zoom_python_csms.CSMSRotateSecret import CSMSSecretService
            unwrap(CSMSSecretService, "__init__")
        except Exception:
            pass

    @staticmethod
    def get_secret() -> Optional[dict]:
        if CsmsInstrumentorV2._get_secret_func:
            return CsmsInstrumentorV2._get_secret_func(["jwtSigningPrivate"])
        return None
