[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "zoom-opentelemetry-distro"
dynamic = ["version"]
description = "Zoom OpenTelemetry Python Distro"
readme = "README.rst"
license = "Apache-2.0"
requires-python = ">=3.8"
authors = [
  { name = "OpenTelemetry Authors", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: Apache Software License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Typing :: Typed",
]
dependencies = [
  "opentelemetry-api ~= 1.14",
  "opentelemetry-instrumentation == 0.57b0",
  "opentelemetry-sdk ~= 1.12",
  "pydantic >= 2.8.2",
  "opentelemetry-processor-baggage == 0.57b0",
  "zoom_jwt_python ~= 0.1.0",
  "cryptography > 44.0.0",
  "zoom-opentelemetry-instrumentation-csms-3.0-python == 0.57b0",
  "zoom-opentelemetry-instrumentation-csms-2.0-python == 0.57b0",
  "concurrent-log-handler == 0.9.25"
]

[project.entry-points.opentelemetry_traces_exporter]
logging-file = "opentelemetry.distro.exporter.file_exporter:LoggingFileSpanExporter"

[project.entry-points.opentelemetry_logs_exporter]
logging-file = "opentelemetry.distro.exporter.file_exporter:LoggingFileLogRecordExporter"

[project.entry-points.opentelemetry_configurator]
configurator = "opentelemetry.distro:OpenTelemetryConfigurator"

[project.entry-points.opentelemetry_distro]
distro = "opentelemetry.distro:OpenTelemetryDistro"


[tool.hatch.version]
path = "src/opentelemetry/distro/version.py"

[tool.hatch.build.targets.sdist]
include = [
  "/src"
]

[tool.hatch.build.targets.wheel]
packages = ["src/opentelemetry"]
