import os

APPLICATION_LOG_PATH = os.environ.get("APPLICATION_LOG_PATH", os.environ.get("OTEL_APPLICATION_LOG_PATH", "/tmp/logs"))
OTEL_FILE_SIZE = os.environ.get("OTEL_FILE_SIZE", 300 * 1024 * 1024)
OTEL_FILE_COUNT = os.environ.get("OTEL_FILE_COUNT", 5)
OTEL_CONFIG_SERVICE_URL = os.environ.get("OTEL_CONFIG_SERVICE_URL", None)
REGION = os.environ.get("REGION", os.environ.get("OTEL_REGION", "va"))
CLUSTER = os.environ.get("CLUSTER", os.environ.get("OTEL_CLUSTER", "unknown"))
APP_NAME = os.environ.get("CMDB_APP_NAME", os.environ.get("OTEL_SERVICE_NAME", "unknown"))
ZM_TRACKING_ID = "x-zm-trackingid"
SHORT_TRACKING_ID = "trackingId"
POST_SAMPLED_FLAG = "post_sampled.flag"
TRACE_LOG_PATH = os.path.join(APPLICATION_LOG_PATH, "zoom_middleware/trace", APP_NAME)
# LOG_RECORD_PATH = os.path.join(APPLICATION_LOG_PATH, "zoom_middleware/log", APP_NAME)
if not os.path.exists(TRACE_LOG_PATH):
    os.makedirs(TRACE_LOG_PATH)
# if not os.path.exists(LOG_RECORD_PATH):
#     os.makedirs(LOG_RECORD_PATH)



