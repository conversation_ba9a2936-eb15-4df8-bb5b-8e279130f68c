# Copyright The OpenTelemetry Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import json
import logging

from concurrent_log_handler import ConcurrentRotatingFileHandler
from google.protobuf.json_format import MessageToDict
from opentelemetry.exporter.otlp.proto.common.trace_encoder import (
    encode_spans,
)
from opentelemetry.sdk.trace.export import SpanExporter, SpanExportResult

from ..constants import *
from ..logging.agent_logger import get_agent_logger

_logger = get_agent_logger()

RESOURCE_SPAN_KEY = "resourceSpans"
RESOURCE_LOG_KEY = "resourceLogs"


class LoggingFileSpanExporter(SpanExporter):
    _instance = None
    _trace_logger = logging.getLogger("cube-trace-exporter")
    _trace_logger.propagate = False
    _trace_logger.setLevel(logging.INFO)
    _trace_logger.addHandler(ConcurrentRotatingFileHandler(
        os.path.join(TRACE_LOG_PATH, "traces.log"),  # Log file name
        maxBytes=OTEL_FILE_SIZE,  # default 300 MB
        backupCount=OTEL_FILE_COUNT  # Keep 5 backup files
    ))
    del _trace_logger.manager.loggerDict["cube-trace-exporter"]
    _shutdown = False

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def _export(self, serialized_data: MessageToDict):
        LoggingFileSpanExporter._trace_logger.info(json.dumps(serialized_data))
        return True

    def _serialize_spans(self, spans):
        return MessageToDict(encode_spans(spans), use_integers_for_enums=True)

    def _export_serialized_spans(self, serialized_data):
        if RESOURCE_SPAN_KEY in serialized_data:
            data_list = serialized_data.get(RESOURCE_SPAN_KEY, [])
            for data in data_list:
                self._export(data)
        return SpanExportResult.SUCCESS

    def export(self, spans) -> SpanExportResult:
        # After the call to Shutdown subsequent calls to Export are
        # not allowed and should return a Failure result.
        if LoggingFileSpanExporter._shutdown:
            _logger.warning("Exporter already shutdown, ignoring batch")
            return SpanExportResult.FAILURE

        serialized_data = self._serialize_spans(spans)

        return self._export_serialized_spans(serialized_data)

    def shutdown(self):
        if LoggingFileSpanExporter._shutdown:
            _logger.warning("Exporter already shutdown, ignoring call")
            return
        LoggingFileSpanExporter._shutdown = True

    def force_flush(self, timeout_millis: int = 30000) -> bool:
        """Nothing is buffered in this exporter, so this method does nothing."""
        return True
