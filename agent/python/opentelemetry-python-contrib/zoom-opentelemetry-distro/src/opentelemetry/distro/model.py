import os
import re
import threading
from re import Pattern
from typing import Optional, Mapping, Callable

from opentelemetry.sdk.environment_variables import OTEL_TRACES_SAMPLER_ARG
from pydantic import BaseModel


class Rule(BaseModel):
    exclude: list[Mapping[str, str]] = []
    include: list[Mapping[str, str]] = []


class AgentConfiguration(BaseModel):
    sampler: str = "parentbased_traceidratio"
    sampleRatio: float = float(os.environ.get(OTEL_TRACES_SAMPLER_ARG, "0.01"))
    rules: list[Rule] = []
    recordKeys: list[str] = []
    timestamp: int = 0
    zoomPropagatorEnable: bool = False
    recordDroppedSpanEnabled: bool = os.environ.get("RECORD_DROPPED_SPAN", "false") == "true"
    captureErrorSpanEnabled: bool = os.environ.get("CAPTURE_ERROR_SPAN", "false") == "true"
    captureErrorLogEnabled: bool = os.environ.get("CAPTURE_ERROR_LOG", "false") == "true"
    collectLogLevelNum: int = 17

    def get_converted_rules(self) -> Mapping[str, list[Mapping[str, Pattern]]]:
        rule_sets: dict[str, list[Mapping[str, Pattern]]] = {}
        if len(self.rules) == 0:
            return rule_sets
        exclude_set: list[Mapping[str, Pattern]] = []
        include_set: list[Mapping[str, Pattern]] = []
        for rule in self.rules:
            self._compile_rules(exclude_set, rule.exclude)
            self._compile_rules(include_set, rule.include)

        rule_sets.update({"exclude": exclude_set})

        rule_sets.update({"include": include_set})

        return rule_sets

    @staticmethod
    def _compile_rules(result: list[Mapping[str, Pattern]], spec: list[Mapping[str, str]]):
        if len(spec) != 0:
            for dic in spec:
                rule_group: dict[str, Pattern] = {}
                for string in dic.keys():
                    if type(dic.get(string)) == str:
                        rule_group.update({string: re.compile(dic.get(string))})
                result.append(rule_group)


class ResponseObject(BaseModel):
    status: Optional[str]
    operId: Optional[str]
    message: Optional[str]
    operCode: Optional[str]
    data: Optional[AgentConfiguration]


DEFAULT_CONFIG = AgentConfiguration()

GLOBAL_CALLBACK: list[Callable[[AgentConfiguration], None]] = []
lock = threading.RLock()


def get_default() -> AgentConfiguration:
    return DEFAULT_CONFIG


def add_callback(callback: Callable[[AgentConfiguration], None]):
    with lock:
        GLOBAL_CALLBACK.append(callback)
