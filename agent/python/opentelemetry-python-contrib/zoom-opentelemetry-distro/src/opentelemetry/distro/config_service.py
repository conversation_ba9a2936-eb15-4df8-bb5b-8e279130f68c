import os
import socket
import threading
import time

import requests

from .jwt.jwt_service_proxy import JWTServiceProxyManager
from .logging.agent_logger import get_agent_logger
from .model import ResponseObject, GLOBAL_CALLBACK
from . import constants

_logger = get_agent_logger()


class ConfigService:

    def __init__(self):
        self._hostname = os.environ.get("HOSTNAME", socket.gethostname())
        self._agent_version = "20250616"
        self._agent_language = "python"
        self._secret_service_proxy = JWTServiceProxyManager()
        self._config_version = 0
        self._pid = 0

    def start(self):
        self._pid = os.getpid()
        if constants.OTEL_CONFIG_SERVICE_URL:
            get_remote_config_thread = threading.Timer(10, self._refresh_config_timed_task)
            get_remote_config_thread.daemon = True
            get_remote_config_thread.start()
        else:
            _logger.warning("OTEL_CONFIG_SERVICE_URL is not configured, will skip get confit from remote server")

    def _refresh_config_timed_task(self):
        while True:
            _logger.info("start refresh config from remote server %s", constants.OTEL_CONFIG_SERVICE_URL)
            self._do_refresh_config()
            time.sleep(120)

    def _do_refresh_config(self):
        token = self._secret_service_proxy.get_token()
        if not token:
            return
        try:
            param = {
                "serviceName": constants.APP_NAME,
                "cluster": constants.CLUSTER,
                "region": constants.REGION,
                "hostname": self._hostname,
                "agentVersion": self._agent_version,
                "agentLanguage": self._agent_language,
                "timestamp": self._config_version,
                "processId": self._pid
            }
            headers = {"Authentication": token}
            response = requests.post(constants.OTEL_CONFIG_SERVICE_URL + "/cfg/trace/agent/agentConfiguration",
                                     json=param, headers=headers)
            if response.status_code != 200:
                _logger.error("get configuration unknown error, message: %s, code: %s",
                              response.content, response.status_code)
                return
            response_object = ResponseObject.parse_raw(response.content)
            if response_object.status != "success":
                _logger.error("get configuration inner error, message: %s, code: %s, status: %s",
                              response_object.message, response_object.operCode, response_object.status)
                return
            tmp_config = response_object.data
            if tmp_config is None:
                _logger.info("get configuration empty, message: %s, code: %s, status: %s",
                             response_object.message, response_object.operCode, response_object.status)
                return
            _logger.info("get new configuration, lastModifiedTime: %s", tmp_config.timestamp)
            self._config_version = tmp_config.timestamp
            for callback in GLOBAL_CALLBACK:
                _ = callback(tmp_config)
        except Exception:
            _logger.error("refresh configuration error", exc_info=True)
