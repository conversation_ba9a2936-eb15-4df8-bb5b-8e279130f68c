import logging
from logging import getLogger
from concurrent_log_handler import ConcurrentRotatingFileHandler
from ..constants import *

_logger = getLogger("cube-agent-log")
_logger.setLevel(logging.INFO)
_logger.propagate = False
handler = ConcurrentRotatingFileHandler(
    os.path.join(TRACE_LOG_PATH, "agent.log"),  # Log file name
    maxBytes=OTEL_FILE_SIZE,  # default 300 MB
    backupCount=OTEL_FILE_COUNT  # Keep 5 backup files
)
formatter = logging.Formatter('%(asctime)s %(filename)s:%(lineno)d %(funcName)s[%(levelname)s] [%(process)d]: %('
                              'message)s')
handler.setFormatter(formatter)
_logger.addHandler(handler)
del _logger.manager.loggerDict["cube-agent-log"]


def get_agent_logger() -> logging.Logger:
    return _logger
