from typing import Optional

from opentelemetry.context import Context
from opentelemetry.sdk.trace import SpanProcessor, ReadableSpan
from opentelemetry.trace import Span, StatusCode
from ..model import AgentConfiguration, add_callback
from ..exporter.file_exporter import LoggingFileSpanExporter
from ..constants import ZM_TRACKING_ID, SHORT_TRACKING_ID, POST_SAMPLED_FLAG


class ZoomSpanProcessor(SpanProcessor):

    def __init__(self) -> None:
        self._capture_error_span_enabled = False
        self._exporter = LoggingFileSpanExporter()
        add_callback(self.update_config)

    def update_config(self, config: AgentConfiguration) -> None:
        self._capture_error_span_enabled = config.captureErrorSpanEnabled

    def on_start(
            self, span: "Span", parent_context: Optional[Context] = None
    ) -> None:
        if not parent_context:
            return
        tracking_id_list = parent_context.get(ZM_TRACKING_ID)
        if not tracking_id_list or not isinstance(tracking_id_list, list):
            return
        tracking_id = tracking_id_list[1] if len(tracking_id_list) == 2 else tracking_id_list[0]
        span.set_attribute(SHORT_TRACKING_ID, tracking_id)

    def on_end(self, span: "ReadableSpan") -> None:
        if not self._capture_error_span_enabled or span.get_span_context().trace_flags.sampled:
            return
        flag = span.attributes.get(POST_SAMPLED_FLAG)
        if flag or span.status.status_code == StatusCode.ERROR:
            self._exporter.export([span])





