from typing import Mu<PERSON>Mapping

from opentelemetry import trace
from opentelemetry._logs import Severity<PERSON><PERSON>ber
from opentelemetry.sdk._logs import LogRecordProcessor, LogData
from opentelemetry.sdk.trace import ReadableSpan
from opentelemetry.semconv.attributes.exception_attributes import EXCEPTION_STACKTRACE
from opentelemetry.trace import Span
from opentelemetry.util.types import Attributes
from ..model import AgentConfiguration, add_callback


class ZoomLogRecordProcessor(LogRecordProcessor):

    def __init__(self):
        self._collect_log_level_num = SeverityNumber.ERROR.value
        add_callback(self._update_config)

    def _update_config(self, config: AgentConfiguration) -> None:
        self._collect_log_level_num = config.collectLogLevelNum

    def emit(self, log_data: LogData):
        if log_data.log_record.severity_number.value < self._collect_log_level_num:
            return
        attrs: Attributes = log_data.log_record.attributes
        if isinstance(attrs, MutableMapping):
            attrs.pop(EXCEPTION_STACKTRACE)
            current_span = trace.get_current_span()
            context = current_span.get_span_context()
            if context.is_valid and isinstance(current_span, ReadableSpan):
                attrs.update({"span.kind": current_span.kind.name})
                attrs.update({"span.name": current_span.name})
                attrs.update({"component.name": current_span.instrumentation_scope.name})
                if isinstance(current_span, Span) and not context.trace_flags.sampled:
                    current_span.set_attribute("post_sampled.flag", True)

    def shutdown(self):
        pass

    def force_flush(self, timeout_millis: int = 30000):
        pass
