import typing
from collections.abc import Mu<PERSON><PERSON>apping

from opentelemetry.context import Context, get_current
from opentelemetry.propagators.textmap import Text<PERSON>apPropagator, CarrierT, Setter, Getter, default_getter, \
    default_setter
from opentelemetry.trace import get_current_span, Span
from ..model import AgentConfiguration, add_callback
from ..logging.agent_logger import get_agent_logger
from ..constants import ZM_TRACKING_ID, SHORT_TRACKING_ID

_logger = get_agent_logger()


class ZoomPropagator(TextMapPropagator):

    def __init__(self):
        self._inject_enable = False
        add_callback(self.callback)

    def extract(self, carrier: CarrierT, context: typing.Optional[Context] = None,
                getter: Getter[CarrierT] = default_getter) -> Context:
        try:
            if context is None:
                context = get_current()
            header = _extract_first_element(getter.get(carrier, ZM_TRACKING_ID))
            if not header:
                return context
            tracking_id = parse_field(header)
            context.update({ZM_TRACKING_ID: [header, tracking_id]})
        except Exception:
            _logger.error("Failed to extract x-zm-trackingid", exc_info=True)
        return context

    def inject(self, carrier: CarrierT, context: typing.Optional[Context] = None,
               setter: Setter[CarrierT] = default_setter) -> None:
        try:
            if context is None:
                context = get_current()
            if self._inject_enable:
                tracking_id_list = context.get(ZM_TRACKING_ID)
                if not tracking_id_list or not isinstance(tracking_id_list, list):
                    return
                setter.set(carrier, ZM_TRACKING_ID, tracking_id_list[0])
            span: Span = get_current_span(context)
            if span and isinstance(carrier, MutableMapping) and ZM_TRACKING_ID in carrier:
                tracking_id = parse_field(carrier[ZM_TRACKING_ID])
                if tracking_id:
                    span.set_attribute(SHORT_TRACKING_ID, tracking_id)
        except Exception:
            _logger.error("Failed to inject x-zm-trackingid", exc_info=True)

    def callback(self, config: AgentConfiguration) -> None:
        self._inject_enable = config.zoomPropagatorEnable

    def fields(self) -> typing.Set[str]:
        fields = set()
        fields.add(ZM_TRACKING_ID)
        return fields


def parse_field(field):
    if not field:
        return None

    split = field.split(";")

    if len(split) == 1 and split[0]:
        return split[0]

    for pair in split:
        if not pair:
            continue
        kv = pair.strip().split("=")
        if len(kv) != 2:
            continue
        key = kv[0].strip()
        value = kv[1].strip()
        if not value:
            continue
        if key == "rid":
            return value
    return None


def _extract_first_element(
        items: typing.Iterable[CarrierT],
) -> typing.Optional[CarrierT]:
    if items is None:
        return None
    return next(iter(items), None)
