from io import <PERSON><PERSON>
from re import <PERSON><PERSON>
from typing import Optional, Sequence, Mapping

from opentelemetry.context import Context
from opentelemetry.sdk.trace.sampling import Sam<PERSON>, ALWAYS_ON, ALWAYS_OFF, TraceIdRatioBased, ParentBased, \
    Decision, Sampling<PERSON><PERSON>ult, _get_parent_trace_state
from opentelemetry.trace import Link, SpanKind, get_current_span
from opentelemetry.trace.span import TraceState
from opentelemetry.util.types import Attributes
from opentelemetry.baggage import get_all

from ..logging.agent_logger import get_agent_logger
from ..model import AgentConfiguration, add_callback

_logger = get_agent_logger()

OTEL_APPLICATION_LOG_PATH = "OTEL_APPLICATION_LOG_PATH"
APPLICATION_LOG_PATH = "APPLICATION_LOG_PATH"
OTEL_TRACES_FILE_SIZE = "OTEL_TRACES_FILE_SIZE"
OTEL_TRACES_FILE_COUNT = "OTEL_TRACES_FILE_COUNT"

OTEL_CONFIG_SERVICE_URL = "OTEL_CONFIG_SERVICE_URL"
REGION = "REGION"
CLUSTER = "CLUSTER"
OTEL_SERVICE_NAME = "OTEL_SERVICE_NAME"
CMDB_APP_NAME = "CMDB_APP_NAME"

SAMPLE_KEYS = "sample_keys"


class DynamicSampler(Sampler):
    """
    Sampler that respects its parent span's sampling decision, but otherwise
    samples probabalistically based on `rate`.
    """

    def __init__(self, config: AgentConfiguration):
        # init
        self._rules = None
        self._root = None
        self._record_dropped_span_enabled = False
        self.update_sampler(config)
        add_callback(self.update_sampler)

    def update_sampler(self, config: AgentConfiguration) -> None:
        self._rules = config.get_converted_rules()
        self._record_dropped_span_enabled = config.recordDroppedSpanEnabled
        match config.sampler:
            case "always_on":
                self._root = ALWAYS_ON
            case "always_off":
                self._root = ALWAYS_OFF
            case "traceidratio":
                self._root = TraceIdRatioBased(config.sampleRatio)
            case "parentbased_always_on":
                self._root = ParentBased(ALWAYS_ON)
            case "parentbased_always_off":
                self._root = ParentBased(ALWAYS_OFF)
            case _:
                sampler = TraceIdRatioBased(config.sampleRatio)
                self._root = ParentBased(root=sampler, remote_parent_not_sampled=sampler,
                                         local_parent_not_sampled=sampler)

    def should_sample(self, parent_context: Optional["Context"], trace_id: int, name: str,
                      kind: Optional[SpanKind] = None, attributes: Attributes = None,
                      links: Optional[Sequence["Link"]] = None,
                      trace_state: Optional["TraceState"] = None) -> "SamplingResult":
        result: SamplingResult = self._root.should_sample(parent_context, trace_id, name, kind,
                                                          attributes, links, trace_state)
        if result.decision == result.decision.RECORD_AND_SAMPLE:
            try:
                baggage = get_all(parent_context)
                excludes = self._rules.get("exclude")
                if excludes and len(excludes) != 0:
                    for exclude_group in excludes:
                        exclude = self._match_pattern(attributes, baggage, exclude_group)
                        if exclude[0]:
                            attributes = None
                            return SamplingResult(Decision.DROP)
            except Exception:
                _logger.error("Failed to sample exclude span", exc_info=True)
        elif result.decision == result.decision.DROP:
            try:
                baggage = get_all(parent_context)
                includes = self._rules.get("include")
                if includes and len(includes) != 0:
                    for include_group in includes:
                        include = self._match_pattern(attributes, baggage, include_group)
                        if include[0]:
                            if isinstance(attributes, dict):
                                attributes.update({SAMPLE_KEYS: include[1]})
                            return SamplingResult(Decision.RECORD_AND_SAMPLE, attributes,
                                                  _get_parent_trace_state(parent_context))
            except Exception:
                _logger.error("Failed to sample include span", exc_info=True)
        if result.decision == Decision.DROP and self._record_dropped_span_enabled:
            return SamplingResult(Decision.RECORD_ONLY, attributes, _get_parent_trace_state(parent_context))
        return result

    @staticmethod
    def _update_attributes(key, value, attributes: Attributes = None):
        if isinstance(attributes, dict):
            attributes.update({key: value})
        return attributes

    def get_description(self) -> str:
        return "DynamicSampler"

    @staticmethod
    def _match_pattern(attributes: Attributes, baggage: Mapping[str, object],
                       pattern_map: Mapping[str, Pattern]):
        if not attributes or not pattern_map:
            return [False, ""]
        raw_pattern = []
        for key, pattern in pattern_map.items():
            value = attributes.get(key)
            if not value and baggage:
                value = baggage.get(key)
            if not value or not pattern.search(value):
                return [False, ""]
            raw_pattern.append(key + ":" + pattern.pattern)
        return [True, ",".join(raw_pattern)]
