import base64
import json
import os
import time
from importlib.metadata import version, PackageNotFoundError
from packaging.version import Version
from abc import ABC, abstractmethod
from typing import Optional
from . import key_util

import requests
from opentelemetry.instrumentation.csms.v3_0 import CsmsInstrumentorV3
from zoom_jwt_python.AsymmJWTService import AsymmJWTService

from ..logging.agent_logger import get_agent_logger

from opentelemetry.instrumentation.csms.v2_0 import CsmsInstrumentorV2

_logger = get_agent_logger()


def get_issuer_and_path_from_response(sdkResponse) -> [str]:
    if sdkResponse["success"] and "secret" in sdkResponse and sdkResponse["secret"]:
        for item in sdkResponse["secret"]:
            if item["properties"].get("signerName") and item["properties"].get("signerName") != "" and \
                    item["alias"] == "jwtSigningPrivate":
                version_detail = [
                    it for it in item['versionList'] if it['stage'] == 'CURRENT'][0]
                fingerprint = key_util.get_public_key_fingerprint_from_private_key(version_detail["secret"])
                version_detail["fingerprint"] = fingerprint
                return [item["properties"]["signerName"], item["prefixPath"]]
    return None


def get_jwt_csms_response(csms_resp, payload: dict, jwt_service: AsymmJWTService):
    if csms_resp:
        try:
            issuer_and_path: Optional[list] = get_issuer_and_path_from_response(csms_resp)
            jwt_service.update_private_key(csms_resp)
            if issuer_and_path:
                payload.update({"path": issuer_and_path[1]})
                return jwt_service.generate_jwt_token(issuer=issuer_and_path[0], payload=payload)
        except Exception:
            _logger.error("generate jwt failed", exc_info=True)
            return None
    return None


class BaseJWTServiceProxy(ABC):

    def __init__(self):
        self._expire_time_seconds = 30 * 60
        self._last_refresh_time = 0
        self._jwt = None

    def _get_basic_payload(self) -> dict:
        return {
            "aud": "cube",
            "iat": int(time.time()),
            "exp": int(time.time()) + self._expire_time_seconds,
        }

    def get_token(self):
        if (int(time.time()) - self._last_refresh_time) < (self._expire_time_seconds / 2):
            return self._jwt
        token = self._generate_token()
        if token:
            self._jwt = token
            self._last_refresh_time = int(time.time())
        return self._jwt

    @abstractmethod
    def _generate_token(self) -> Optional[str]:
        """generate token"""


class DefaultCsmsJWTService(BaseJWTServiceProxy):
    def __init__(self):
        super().__init__()
        self._csms_endpoint = os.environ.get("CSMS_ENDPOINTS", None)
        self._csms_app_path = os.environ.get("CSMS_APP_PATH", None)
        self._aws_role_arn = os.environ.get("AWS_ROLE_ARN", None)
        self._aws_token_file = os.environ.get("AWS_WEB_IDENTITY_TOKEN_FILE", None)
        self._jwt_service = AsymmJWTService()

    def _generate_token(self) -> Optional[str]:
        payload = self._get_basic_payload()
        csms_resp = self._get_csms_keys_by_aws_token_file()
        return get_jwt_csms_response(csms_resp, payload, self._jwt_service)

    def _get_csms_keys_by_aws_token_file(self):
        try:
            if not self._aws_token_file or not os.path.isfile(self._aws_token_file):
                _logger.error("aws token file not exist")
                return None
            with open(self._aws_token_file, mode="r", encoding='utf-8') as f:
                token = f.read()
            if not token:
                _logger.error("aws token file is empty")
                return None
            encoded_aws_token = base64.b64encode(token.encode()).decode().strip()
            encoded_role_arn = base64.b64encode(self._aws_role_arn.encode()).decode().strip()
            ticket = f"{encoded_role_arn}.{encoded_aws_token}"
            headers = {"Ticket": ticket, "AuthType": "token"}
            response = requests.get(self._csms_endpoint + "/api/1.0/getSecret?prefixPath="
                                    + self._csms_app_path + "&pageSize=1&alias=jwtSigningPrivate&strict=false",
                                    headers=headers)

            if response.status_code != 200:
                _logger.error("get csms keys unknown error, message: %s, code: %s",
                              response.content, response.status_code)
                return None
            return json.loads(response.content)
        except Exception:
            _logger.error("get csms keys by local aws token error", exc_info=True)
            return None


class CsmsJWTServiceV2(BaseJWTServiceProxy):

    def __init__(self):
        super().__init__()
        self._jwt_service = AsymmJWTService()

    def _generate_token(self) -> Optional[str]:
        csms_resp = None
        payload = self._get_basic_payload()
        try:
            csms_resp = CsmsInstrumentorV2.get_secret()
        except Exception:
            _logger.error("get csms keys v2 error", exc_info=True)
        return get_jwt_csms_response(csms_resp, payload, self._jwt_service)


class CsmsJWTServiceV3(BaseJWTServiceProxy):
    def __init__(self):
        super().__init__()

    def _generate_token(self) -> Optional[str]:
        try:
            jwt_client = CsmsInstrumentorV3.get_jwt_client()
            if jwt_client:
                return jwt_client.generate_jwt_token(payload=self._get_basic_payload())
        except Exception:
            _logger.error("get csms keys v3 error", exc_info=True)
            return None


class JWTServiceProxyManager(BaseJWTServiceProxy):
    def __init__(self):
        super().__init__()
        self._jwt_service_list = find_jwt_service_proxy()

    def _generate_token(self) -> Optional[str]:
        pass

    def get_token(self):
        for service in self._jwt_service_list:
            token = service.get_token()
            if token:
                return token
        return None


def find_jwt_service_proxy() -> []:
    service_list = []
    try:
        package_version_str = version("zoom-python-csms")
        _logger.info("zoom-python-csms version: %s", package_version_str)
        package_version = Version(package_version_str)
        high_version = Version("3.0.0")
        if package_version >= high_version:
            service_list.append(CsmsJWTServiceV3())
        else:
            service_list.append(CsmsJWTServiceV2())
        service_list.append(DefaultCsmsJWTService())
    except PackageNotFoundError:
        _logger.error("zoom-python-csms is not installed, will use default")
        service_list.append(DefaultCsmsJWTService())
    return service_list
