import codecs
import hashlib

from cryptography.hazmat.primitives import serialization


def _generate_public_key_from_private_key(private_key: str):
    """
    Generate public key from private key.
    """
    key = serialization.load_pem_private_key(private_key.encode(), password=None)
    public_key = (
        key.public_key()
        .public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )
        .decode()
    )
    return public_key


def calculate_fingerprint(public_key: str) -> str:
    key = serialization.load_pem_public_key(public_key.encode())
    public_key = key.public_bytes(
        encoding=serialization.Encoding.DER,
        format=serialization.PublicFormat.SubjectPublicKeyInfo,
    )
    hex_sha256 = hashlib.sha256(public_key).hexdigest()
    key_fingerprint = (
        codecs.encode(codecs.decode(hex_sha256[:12], "hex"), "base64").decode().strip()
    )
    return key_fingerprint


def get_public_key_fingerprint_from_private_key(private_key: str):
    """
    Generate public key fingerprint from private key.
    """
    public_key = _generate_public_key_from_private_key(private_key=private_key)
    key_fingerprint = calculate_fingerprint(public_key=public_key)
    return key_fingerprint
