from opentelemetry import propagate
from opentelemetry.environment_variables import OTEL_TRACES_EXPORTER, OTEL_METRICS_EXPORTER
from opentelemetry.instrumentation.distro import BaseDistro
from opentelemetry.processor.baggage import BaggageSpanProcessor
from opentelemetry.propagators import composite
from opentelemetry.sdk._configuration import _OTelSDKConfigurator
from opentelemetry.sdk.environment_variables import OTEL_TRACES_SAMPLER, OTEL_TRACES_SAMPLER_ARG, \
    OTEL_BSP_MAX_EXPORT_BATCH_SIZE
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.trace import get_tracer_provider

from .config_service import ConfigService
from .constants import *
from .model import get_default, add_callback, AgentConfiguration
from .processor.zoom_processor import ZoomSpanProcessor
from .propagator.zoom_propagator import ZoomPropagator
from .sampler.dynamic_sampler import DynamicSampler


class OpenTelemetryConfigurator(_OTelSDKConfigurator):
    def __init__(self):
        self._config_service = ConfigService()
        self._config_service.start()

    def _configure(self, **kwargs):
        config = get_default()
        kwargs["sampler"] = DynamicSampler(config)
        os.register_at_fork(after_in_child=self._config_service.start)
        if kwargs.get("resource_attributes"):
            kwargs["resource_attributes"].update({"service.name": APP_NAME})
        else:
            kwargs["resource_attributes"] = {"service.name": APP_NAME}
        super()._configure(**kwargs)
        tracer_provider: TracerProvider = get_tracer_provider()
        predicate = lambda key: key in config.recordKeys if config.recordKeys else False

        baggage_span_processor = BaggageSpanProcessor(predicate)

        def update_baggage_predicate(new_config: AgentConfiguration):
            baggage_span_processor._baggage_key_predicate = lambda key: key in new_config.recordKeys \
                if new_config.recordKeys else False

        add_callback(update_baggage_predicate)
        tracer_provider.add_span_processor(baggage_span_processor)
        tracer_provider.add_span_processor(ZoomSpanProcessor())

        propagators = propagate.propagators
        propagators.append(ZoomPropagator())
        propagate.set_global_textmap(composite.CompositePropagator(propagators))


class OpenTelemetryDistro(BaseDistro):
    """
    The OpenTelemetry provided Distro configures a default set of
    configuration out of the box.
    """

    # pylint: disable=no-self-use
    def _configure(self, **kwargs):
        os.environ.setdefault(OTEL_TRACES_EXPORTER, "logging-file")
        os.environ.setdefault(OTEL_METRICS_EXPORTER, "none")
        os.environ.setdefault(OTEL_TRACES_SAMPLER, "parentbased_traceidratio")
        os.environ.setdefault(OTEL_TRACES_SAMPLER_ARG, "0.01")
        os.environ.setdefault(OTEL_BSP_MAX_EXPORT_BATCH_SIZE, "4")
