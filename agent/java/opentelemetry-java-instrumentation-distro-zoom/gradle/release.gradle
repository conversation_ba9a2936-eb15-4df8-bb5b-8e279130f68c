//java {
//    withSourcesJar()
//}

publishing {
    publications {
        mavenJava(MavenPublication) {
            groupId = GROUP_ID
            artifactId = "opentelemetry-$project.name"
            version = VERSION
            from components.java
        }
    }
}


// Variables will be loaded from gradle.properties
artifactory {
    contextUrl = "${zoom_rt_context_url}"
    publish {
        repository {
            repoKey = VERSION.endsWith('-SNAPSHOT') ? "${zoom_rt_snapshot_repo}" : "${zoom_rt_release_repo}"
            username = "${zoom_rt_user}"
            password = "${zoom_rt_password}"
            maven = true
        }
        defaults {
            publications(publishing.publications.mavenJava)
            publishArtifacts = true
            publishBuildInfo = true
        }
    }
    clientConfig.setIncludeEnvVars(true)
    clientConfig.timeout = 600
}

