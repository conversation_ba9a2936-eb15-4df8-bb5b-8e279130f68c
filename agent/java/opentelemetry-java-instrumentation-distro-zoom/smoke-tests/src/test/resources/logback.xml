<?xml version="1.0" encoding="UTF-8"?>
<configuration>

  <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>

  <root level="INFO">
    <appender-ref ref="console"/>
  </root>

  <logger name="io.opentelemetry" level="debug"/>
  <logger name="com.example.javaagent" level="debug"/>
  <logger name="org.testcontainers" level="debug"/>
  <logger name="org.testcontainers.shaded" level="warn"/>
  <logger name="org.testcontainers.containers.output.WaitingConsumer" level="warn"/>
  <logger name="Collector" level="debug"/>
  <logger name="Backend" level="debug"/>

</configuration>
