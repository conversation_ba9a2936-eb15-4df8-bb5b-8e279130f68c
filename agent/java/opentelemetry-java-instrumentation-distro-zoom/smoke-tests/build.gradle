plugins {
  id "java"
}

dependencies {
  testImplementation("org.testcontainers:testcontainers:1.20.1")
  testImplementation("com.fasterxml.jackson.core:jackson-databind:2.17.2")
  testImplementation("com.google.protobuf:protobuf-java-util:3.25.4")
  testImplementation("com.squareup.okhttp3:okhttp:4.12.0")
  testImplementation("io.opentelemetry.proto:opentelemetry-proto:1.3.2-alpha")
  testImplementation("io.opentelemetry:opentelemetry-api")

  testImplementation("ch.qos.logback:logback-classic:1.5.8")
}

tasks.test {
  useJUnitPlatform()

  testLogging.showStandardStreams = true

  def shadowTask = project(":agent").tasks.shadowJar
  dependsOn(shadowTask)
  inputs.files(layout.files(shadowTask))

  doFirst {
    jvmArgs("-Dio.opentelemetry.smoketest.agent.shadowJar.path=${shadowTask.archiveFile.get()}")
  }
}
