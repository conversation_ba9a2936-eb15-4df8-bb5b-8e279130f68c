/*
 * Copyright 2023 Domstoladministrasjonen, Norway
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package us.zoom.trace.javaagent.bootstrap;

@SuppressWarnings({"overrides"})
public class AgentConfigurationRefreshResult {

   private AgentConfiguration agentConfiguration;

   private boolean success;

    public AgentConfigurationRefreshResult(AgentConfiguration agentConfiguration, boolean success) {
        this.agentConfiguration = agentConfiguration;
        this.success = success;
    }

    public AgentConfigurationRefreshResult(boolean success) {
        this.success = success;
    }

    public AgentConfiguration getAgentConfiguration() {
        return agentConfiguration;
    }

    public void setAgentConfiguration(AgentConfiguration agentConfiguration) {
        this.agentConfiguration = agentConfiguration;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
