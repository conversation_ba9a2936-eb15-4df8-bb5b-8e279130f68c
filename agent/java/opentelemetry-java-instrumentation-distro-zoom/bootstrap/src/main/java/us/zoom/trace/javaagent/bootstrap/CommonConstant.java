package us.zoom.trace.javaagent.bootstrap;

/**
 * <AUTHOR>
 */
public interface CommonConstant {
    char COMMA = ',';
    char COLON = ':';
    char SPACE = ' ';
    String TOPIC_NAME = "topic";
    String UPSTREAM = "upstream";
    String DOWNSTREAM = "downstream";
    char UNDERSCORE = '_';
    String SDK_NAME = "trace";
    String TRACE_TOPIC_PREFIX = "mw_trace_";
    String TRACE_TOPIC_SUFFIX = "_traces";
    String TRACE_REPORT_API = "/api/metrics";
    String TRACE_REPORT_CONTENT_TYPE = "application/x-protobuf";
    int TRACE_REPORT_PORT = 51105;
    int MAX_STACK_TRACE_LENGTH = 2048;
    String CUSTOM_ERROR_EVENT_NAME = "biz_error";
}
