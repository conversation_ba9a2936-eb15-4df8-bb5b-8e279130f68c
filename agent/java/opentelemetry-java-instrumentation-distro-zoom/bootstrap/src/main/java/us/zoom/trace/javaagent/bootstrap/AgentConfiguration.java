/*
 * Copyright 2023 Domstoladministrasjonen, Norway
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package us.zoom.trace.javaagent.bootstrap;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Represents a sampler configuration as serviced by the Agent Configuration
 * Service or read from a json-file.
 *
 * @since 1.0
 */
@SuppressWarnings({"overrides"})
public class AgentConfiguration {

    private static AgentConfiguration globalConfiguration = new AgentConfiguration();

    public enum SamplerType {
        always_on, always_off, traceidratio, parentbased_always_on, parentbased_always_off, parentbased_traceidratio,
    }

    private SamplerType sampler = SamplerType.parentbased_traceidratio;

    private Double sampleRatio = 0.01;

    private long timestamp = 0;

    private List<Rules> rules;

    private Set<String> recordKeys;
    private Set<String> propagableKeys;

    private Boolean captureErrorSpanEnabled = EnvironmentConfig.CAPTURE_ERROR_SPAN.getBooleanValue();

    private Boolean captureErrorLogEnabled = EnvironmentConfig.CAPTURE_ERROR_LOG.getBooleanValue();

    private Boolean replaceTrackingIdEnabled = EnvironmentConfig.REPLACE_TRACKING_ID.getBooleanValue();

    private Boolean reportByHttpProtobufEnabled = EnvironmentConfig.HTTP_PROTOBUF_ENABLED.getBooleanValue();

    private List<String> tracesScheduledClassNames = EnvironmentConfig.TRACES_SCHEDULED_CLASS_NAMES.getListValue();

    private Integer maxExportBatchSize = EnvironmentConfig.OTEL_BSP_MAX_EXPORT_BATCH_SIZE.getIntValue();

    private Map<String, Set<Integer>> skipErrorLogStackFrameMapping = new HashMap<>();

    private boolean enableMybatisEnhancement = false;
    private boolean mybatisEnhancementAsClient = false;

    public boolean captureErrorSpanEnabled() {
        return Boolean.TRUE.equals(captureErrorSpanEnabled);
    }

    public boolean captureErrorLogEnabled() {
        return Boolean.TRUE.equals(captureErrorLogEnabled);
    }

    public boolean replaceTrackingIdEnabled() {
        return Boolean.TRUE.equals(replaceTrackingIdEnabled);
    }


    public boolean reportByHttpProtobufEnabled() {
        return Boolean.TRUE.equals(reportByHttpProtobufEnabled);
    }

    public void setReportByHttpProtobufEnabled(Boolean reportByHttpProtobufEnabled) {
        this.reportByHttpProtobufEnabled = reportByHttpProtobufEnabled;
    }

    public Integer getMaxExportBatchSize() {
        return maxExportBatchSize;
    }

    public void setMaxExportBatchSize(Integer maxExportBatchSize) {
        this.maxExportBatchSize = maxExportBatchSize;
    }

    public List<String> getTracesScheduledClassNames() {
        return tracesScheduledClassNames;
    }

    public void setTracesScheduledClassNames(List<String> tracesScheduledClassNames) {
        this.tracesScheduledClassNames = tracesScheduledClassNames;
    }

    public void setSampleRatio(Double sampleRatio) {
        this.sampleRatio = sampleRatio;
    }

    public void setCaptureErrorSpanEnabled(Boolean captureErrorSpanEnabled) {
        if (captureErrorSpanEnabled != null) {
            this.captureErrorSpanEnabled = captureErrorSpanEnabled;
        }
    }

    public void setCaptureErrorLogEnabled(Boolean captureErrorLogEnabled) {
        if (captureErrorLogEnabled != null) {
            this.captureErrorLogEnabled = captureErrorLogEnabled;
        }
    }

    public void setReplaceTrackingIdEnabled(Boolean replaceTrackingIdEnabled) {
        if (replaceTrackingIdEnabled != null) {
            this.replaceTrackingIdEnabled = replaceTrackingIdEnabled;
        }
    }

    public Map<String, Set<Integer>> getSkipErrorLogStackFrameMapping() {
        return skipErrorLogStackFrameMapping;
    }

    public void setSkipErrorLogStackFrameMapping(Map<String, Set<Integer>> skipErrorLogStackFrameMapping) {
        this.skipErrorLogStackFrameMapping = skipErrorLogStackFrameMapping;
    }

    public Set<String> getRecordKeys() {
        return recordKeys;
    }

    public void setRecordKeys(Set<String> recordKeys) {
        this.recordKeys = recordKeys;
    }

    public Set<String> getPropagableKeys() {
        return propagableKeys;
    }

    public void setPropagableKeys(Set<String> propagableKeys) {
        this.propagableKeys = propagableKeys;
    }

    public boolean isEnableMybatisEnhancement() {
        return enableMybatisEnhancement;
    }

    public void setEnableMybatisEnhancement(boolean enableMybatisEnhancement) {
        this.enableMybatisEnhancement = enableMybatisEnhancement;
    }

    public boolean isMybatisEnhancementAsClient() {
        return mybatisEnhancementAsClient;
    }

    public void setMybatisEnhancementAsClient(boolean mybatisEnhancementAsClient) {
        this.mybatisEnhancementAsClient = mybatisEnhancementAsClient;
    }

    public static class Rules {

        private List<Map<String, String>> exclude;

        private List<Map<String, String>> include;

        public List<Map<String, String>> getExclude() {
            return exclude;
        }

        public void setExclude(List<Map<String, String>> exclude) {
            this.exclude = exclude;
        }

        public List<Map<String, String>> getInclude() {
            return include;
        }

        public void setInclude(List<Map<String, String>> include) {
            this.include = include;
        }
    }

    public double getSampleRatio() {
        return sampleRatio;
    }

    public void setSampleRatio(double sampleRatio) {
        this.sampleRatio = sampleRatio;
    }

    public SamplerType getSampler() {
        return sampler;
    }

    public void setSampler(SamplerType sampler) {
        this.sampler = sampler;
    }

    public List<Rules> getRules() {
        return rules;
    }

    public void setRules(List<Rules> rules) {
        this.rules = rules;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }


    public static AgentConfiguration globalConfiguration() {
        return globalConfiguration;
    }

    public static void setGlobalConfiguration(AgentConfiguration globalConfiguration) {
        AgentConfiguration.globalConfiguration = globalConfiguration;
    }
}
