package us.zoom.trace.javaagent.bootstrap;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public enum EnvironmentConfig {

    OTEL_CONFIG_SERVICE_URL("otel.config.service.url", ""),
    CMDB_APP_NAME("otel.service.name", "unknown"),
    REGION("otel.region", ""),
    APP_NODE_IP("otel.app.node.ip", "127.0.0.1"),
    USE_FILE_EXPORTER("otel.use.file.exporter", "false"),
    CLUSTER("otel.cluster", ""),
    OTEL_TRACES_SAMPLER_ARG("otel.traces.sampler.arg", "0.01"),
    OTEL_TRACES_SAMPLER("otel.traces.sampler", "parentbased_traceidratio"),
    APPLICATION_LOG_PATH("otel.application.log.path", "/tmp/logs"),
    OTEL_TRACE_LOG_FILE_SIZE("otel.traces.file.size", String.valueOf(300 * 1024 * 1024)),
    OTEL_TRACE_LOG_FILE_COUNT("otel.traces.file.count", "5"),
    LINE_SEPARATOR("line.separator", "\n"),
    PATH_SEPARATOR("file.separator", "/"),
    OTEL_AUTH_ENABLED("otel.auth.enabled", "true"),
    CSMS_ENDPOINTS("csms.endpoints", ""),
    CSMS_APP_PATH("csms.app.path", ""),
    AWS_WEB_IDENTITY_TOKEN_FILE("aws.web.identity.token.file", ""),
    AWS_ROLE_ARN("aws.role.arn", ""),
    TRACES_DYNAMIC_CONFIG_FILE_PATH("otel.traces.dynamic.config.file.path", null),
    RECORD_DROPPED_SPAN("otel.traces.record.dropped.span", "false"),
    CAPTURE_ERROR_SPAN("otel.traces.capture.error.span", "false"),
    CAPTURE_ERROR_LOG("otel.logs.capture.error.log", "false"),
    REPLACE_TRACKING_ID("otel.traces.replace.trackingid", "true"),
    HTTP_PROTOBUF_ENABLED("otel.traces.http.protobuf.enabled", "false"),
    TRACES_SCHEDULED_CLASS_NAMES("otel.traces.scheduled.class.names", null),
    OTEL_BSP_MAX_EXPORT_BATCH_SIZE("otel.bsp.max.export.batch.size", "4")
    ;

    private static final Map<EnvironmentConfig, String> cache = new ConcurrentHashMap<>();

    private String propertyName;
    private String defaultValue;

    EnvironmentConfig(String propertyName, String defaultValue) {
        this.propertyName = propertyName;
        this.defaultValue = defaultValue;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public String getValue() {
        return cache.computeIfAbsent(this, t -> {
            String value = System.getenv(name());
            if (value == null) {
                value = System.getProperty(propertyName);
            } else {
                System.setProperty(propertyName, value);
            }
            return value == null ? defaultValue : value;
        });
    }

    public List<String> getListValue() {
        String value = getValue();
        if (value == null) {
            return Collections.emptyList();
        }
        String[] split = value.split(",");
        return Arrays.asList(split);
    }

    public boolean getBooleanValue() {
        String value = getValue();
        if (value == null) {
            return false;
        }
        return Boolean.parseBoolean(value);
    }

    public Integer getIntValue() {
        String value = getValue();
        if (value == null) {
            return 0;
        }
        return Integer.parseInt(value);
    }

}
