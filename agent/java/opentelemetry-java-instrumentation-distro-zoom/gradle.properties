# workaround for https://github.com/diffplug/spotless/issues/834
org.gradle.jvmargs=\
  --add-exports jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED \
  --add-exports jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED \
  --add-exports jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED \
  --add-exports jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED \
  --add-exports jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED

GROUP_ID=us.zoom
#VERSION=2.8.15.2-BETA
# only used for local
#zoom_rt_repo_url=https://artifacts.corp.zoom.us/artifactory/zoom-mvn-virtual
#zoom_rt_context_url=https://artifacts.corp.zoom.us/artifactory
#zoom_rt_release_repo=zoom-mvn-release
#zoom_rt_snapshot_repo=zoom-mvn-snapshot
