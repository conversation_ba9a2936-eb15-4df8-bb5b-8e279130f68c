package us.zoom.trace.javaagent.auth.csms;

import us.zoom.trace.javaagent.auth.JwtPrivateKeyResponse;
import us.zoom.trace.javaagent.logging.ILog;
import us.zoom.trace.javaagent.logging.LogManager;

import java.lang.reflect.Method;
import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */

public class CompatibleCsmsConfigService implements CsmsConfigService {


    private static final ILog logger = LogManager.getLogger(CompatibleCsmsConfigService.class);
    private static final String JWT_SINGING_PRIVATE = "jwtSigningPrivate";
    private static final String CURRENT_TAG = "CURRENT";
    private static final String INIT_SECRET_GETTER_FAILED = "init secret getter failed, will skip";
    private static final String CANNOT_FIND_USER_CLASSLOADER_ERROR = "get csms sdk classLoader failed";
    private static final String SECRET_NOT_EXIST = "secret not exist";
    private static final String GET_SECRET_KEY_ERROR = "get secret key error";
    private static final Supplier<JwtPrivateKeyResponse> DEFAULT_SECRET_GETTER = () -> JwtPrivateKeyResponse.DEFAULT_RESPONSE;

    private Supplier<JwtPrivateKeyResponse> secretGetter;
    private boolean init = false;

    private void init() {
        if (init) {
            return;
        }
        try {
            Class<?> aClass = Class.forName("us.zoom.trace.javaagent.instrumentation.csms.v0_6_0.CsmsSdkInstrumentation");
            Method method = aClass.getMethod("getUserspaceClassLoader");
            ClassLoader appClassLoader = (ClassLoader) method.invoke(null);
            if (appClassLoader == null) {
                throw new RuntimeException(CANNOT_FIND_USER_CLASSLOADER_ERROR);
            }
            Class<?> providerClazz = appClassLoader.loadClass("us.zoom.cloud.secrets.provider.CSMSServiceProvider");
            Method getInstance = providerClazz.getMethod("getInstance");
            Object configProvider = getInstance.invoke(null);
            Method getCSMSMultiVerService = providerClazz.getMethod("getCSMSMultiVerService");
            Class<?> csmsRestApiClazz = appClassLoader.loadClass("us.zoom.cloud.secrets.service.CSMSMultiVerService");
            Method getConfigMethod = csmsRestApiClazz.getMethod("getSecret", String.class);
            Class<?> kvSecretClazz = appClassLoader.loadClass("us.zoom.cloud.secrets.vo.KVSecret");
            Method getStage = kvSecretClazz.getMethod("getStage");
            Method getValue = kvSecretClazz.getMethod("getValue");
            Method getFingerprint = kvSecretClazz.getMethod("getFingerprint");
            Method getPath = kvSecretClazz.getMethod("getPath");
            Method getProp = kvSecretClazz.getMethod("getProperties");
            Class<?> secretPropClazz = appClassLoader.loadClass("us.zoom.cloud.secrets.vo.CSMSSecretProperties");
            Method getSignerName = secretPropClazz.getMethod("getSignerName");
            secretGetter = () -> {
                try {
                    Object api = getCSMSMultiVerService.invoke(configProvider);
                    List kvSecrets = (List) getConfigMethod.invoke(api, JWT_SINGING_PRIVATE);
                    if (kvSecrets == null || kvSecrets.isEmpty()) {
                        throw new RuntimeException(SECRET_NOT_EXIST);
                    }
                    for (Object kvSecret : kvSecrets) {
                        Enum stage = (Enum) getStage.invoke(kvSecret);
                        if (!CURRENT_TAG.equals(stage.name())) {
                            continue;
                        }
                        return new JwtPrivateKeyResponse(
                                (String) getSignerName.invoke(getProp.invoke(kvSecret)),
                                (String) getValue.invoke(kvSecret),
                                (String) getFingerprint.invoke(kvSecret),
                                (String) getPath.invoke(kvSecret)
                        );
                    }
                    return JwtPrivateKeyResponse.DEFAULT_RESPONSE;
                } catch (Throwable e) {
                    logger.error(e, GET_SECRET_KEY_ERROR);
                    return JwtPrivateKeyResponse.DEFAULT_RESPONSE;
                }
            };
        } catch (Throwable e) {
            logger.error(e, INIT_SECRET_GETTER_FAILED);
            secretGetter = DEFAULT_SECRET_GETTER;
        }
        init = true;
    }

    @Override
    public JwtPrivateKeyResponse getJwtPrivateKey() {
        init();
        return secretGetter.get();
    }

}
