package us.zoom.trace.javaagent;

import com.fasterxml.jackson.core.type.TypeReference;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.trace.propagation.W3CTraceContextPropagator;
import io.opentelemetry.context.propagation.TextMapPropagator;
import io.opentelemetry.exporter.otlp.http.trace.OtlpHttpSpanExporter;
import io.opentelemetry.exporter.otlp.http.trace.OtlpHttpSpanExporterBuilder;
import io.opentelemetry.javaagent.bootstrap.InstrumentationHolder;
import io.opentelemetry.sdk.autoconfigure.spi.AutoConfigurationCustomizer;
import io.opentelemetry.sdk.autoconfigure.spi.AutoConfigurationCustomizerProvider;
import io.opentelemetry.sdk.autoconfigure.spi.ConfigProperties;
import io.opentelemetry.sdk.trace.export.BatchSpanProcessor;
import io.opentelemetry.sdk.trace.export.BatchSpanProcessorBuilder;
import io.opentelemetry.sdk.trace.export.SpanExporter;
import io.opentelemetry.sdk.trace.samplers.Sampler;
import net.bytebuddy.agent.builder.AgentBuilder;
import net.bytebuddy.implementation.FieldAccessor;
import net.bytebuddy.matcher.ElementMatchers;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;
import us.zoom.trace.javaagent.bootstrap.CommonConstant;
import us.zoom.trace.javaagent.bootstrap.EnvironmentConfig;
import us.zoom.trace.javaagent.common.CustomSpan;
import us.zoom.trace.javaagent.exporter.FileJsonLoggingSpanExporter;
import us.zoom.trace.javaagent.logging.ILog;
import us.zoom.trace.javaagent.logging.LogManager;
import us.zoom.trace.javaagent.processor.BatchSpanProcessorWrapper;
import us.zoom.trace.javaagent.processor.LogRecordEnhanceProcessor;
import us.zoom.trace.javaagent.processor.ZoomSpanProcessor;
import us.zoom.trace.javaagent.propagator.ZoomTracePropagator;
import us.zoom.trace.javaagent.sampler.DynamicSamplerWrapper;
import us.zoom.trace.javaagent.sampler.RuleBasedSampler;
import us.zoom.trace.javaagent.util.JsonUtil;
import us.zoom.trace.javaagent.util.RuleUtil;

import java.io.InputStream;
import java.lang.instrument.Instrumentation;
import java.time.Duration;
import java.util.*;


public class ZoomAutoConfigurationCustomizer implements AutoConfigurationCustomizerProvider {


    private static final ILog logger = LogManager.getLogger(ZoomAutoConfigurationCustomizer.class);

    private final ConfigService configService = new ConfigService();

    private static final String SERVICE_NAME_KEY = "service.name";

    @Override
    public void customize(AutoConfigurationCustomizer autoConfigurationCustomizer) {
        init();
        Map<String, String> defaultConfig = loadDefaultConfig();
        autoConfigurationCustomizer.addPropertiesSupplier(() -> defaultConfig);
        configService.start();

        AgentConfiguration agentConfiguration = AgentConfiguration.globalConfiguration();
        agentConfiguration.setSampleRatio(Double.parseDouble(EnvironmentConfig.OTEL_TRACES_SAMPLER_ARG.getValue()));
        autoConfigurationCustomizer.addTracerProviderCustomizer((sdkTracerProviderBuilder, configProperties) -> {
            sdkTracerProviderBuilder.addSpanProcessor(new ZoomSpanProcessor());
//            Set<String> recordKeys = agentConfiguration.getRecordKeys();
//            if (recordKeys == null) {
//                recordKeys = Collections.emptySet();
//            }
//            BaggageSpanProcessor processor = BaggageSpanProcessor.createProcessor(recordKeys);
//            sdkTracerProviderBuilder.addSpanProcessor(processor);
            BatchSpanProcessor batchSpanProcessor = buildBatchSpanProcessor(configProperties, FileJsonLoggingSpanExporter.create(), agentConfiguration.getMaxExportBatchSize());
            BatchSpanProcessorWrapper wrapper = new BatchSpanProcessorWrapper(batchSpanProcessor, agentConfiguration.getMaxExportBatchSize());
            sdkTracerProviderBuilder.addSpanProcessor(wrapper);
            configService.registerListener(newConfig -> {
//                Set<String> keys;
//                if ((keys = newConfig.getRecordKeys()) == null) {
//                    keys = Collections.emptySet();
//                }
//                processor.setBaggageKeyPredicate(keys::contains);
                SpanExporter exporter = wrapper.getBatchSpanProcessor().getSpanExporter();
                SpanExporter newExporter = null;
                boolean batchSizeChanged = newConfig.getMaxExportBatchSize() != wrapper.getMaxBatchSize();
                if (newConfig.reportByHttpProtobufEnabled()) {
                    if (exporter instanceof FileJsonLoggingSpanExporter) {
                        newExporter = buildHttpExporter();
                    }
                } else if (exporter instanceof OtlpHttpSpanExporter) {
                    newExporter = FileJsonLoggingSpanExporter.create();
                }
                if (batchSizeChanged && newExporter == null) {
                    newExporter = exporter instanceof FileJsonLoggingSpanExporter ? FileJsonLoggingSpanExporter.create() : buildHttpExporter();
                }
                if (newExporter != null) {
                    BatchSpanProcessor newBatchSpanProcessor = buildBatchSpanProcessor(configProperties, newExporter, newConfig.getMaxExportBatchSize());
                    wrapper.update(newBatchSpanProcessor, newConfig.getMaxExportBatchSize());
                }
            });
            return sdkTracerProviderBuilder;
        });

        autoConfigurationCustomizer.addPropagatorCustomizer((textMapPropagator, configProperties) -> {
            if (textMapPropagator instanceof W3CTraceContextPropagator) {
                return TextMapPropagator.composite(textMapPropagator, ZoomTracePropagator.getInstance());
            }
            return textMapPropagator;
        });
        autoConfigurationCustomizer.addResourceCustomizer((resource, configProperties) -> {
            if (resource.getAttribute(AttributeKey.stringKey(SERVICE_NAME_KEY)) != null) {
                return resource.toBuilder()
                        .put(SERVICE_NAME_KEY, EnvironmentConfig.CMDB_APP_NAME.getValue())
                        .put(EnvironmentConfig.REGION.name().toLowerCase(), EnvironmentConfig.REGION.getValue())
                        .put(EnvironmentConfig.CLUSTER.name().toLowerCase(), EnvironmentConfig.CLUSTER.getValue())
                        .build();
            }
            return resource;
        });
        //use decorator mode
        autoConfigurationCustomizer.addSamplerCustomizer(
                (sampler, properties) -> {
                    // ignore original sampler
                    DynamicSamplerWrapper dynamicSamplerWrapper = new DynamicSamplerWrapper();
                    updateSampler(agentConfiguration, dynamicSamplerWrapper);
                    configService.registerListener(newConfig -> updateSampler(newConfig, dynamicSamplerWrapper));
                    return dynamicSamplerWrapper;
                });
        autoConfigurationCustomizer.addLoggerProviderCustomizer((sdkLoggerProviderBuilder, configProperties) -> {
            LogRecordEnhanceProcessor logRecordEnhanceProcessor = new LogRecordEnhanceProcessor();
            sdkLoggerProviderBuilder.addLogRecordProcessor(logRecordEnhanceProcessor);
            return sdkLoggerProviderBuilder;
        });
    }

    private void updateSampler(AgentConfiguration config, DynamicSamplerWrapper dynamicSamplerWrapper) {
        RuleBasedSampler.getInstance().setRules(RuleUtil.convertRules(config.getRules()));

        Sampler configuredSampler;
        switch (config.getSampler()) {
            case always_off:
                configuredSampler = Sampler.alwaysOff();
                break;
            case always_on:
                configuredSampler = Sampler.alwaysOn();
                break;
            case traceidratio:
                configuredSampler = Sampler.traceIdRatioBased(config.getSampleRatio());
                break;
            case parentbased_always_off:
                configuredSampler = Sampler.parentBased(Sampler.alwaysOff());
                break;
            case parentbased_always_on:
                configuredSampler = Sampler.parentBased(Sampler.alwaysOn());
                break;
            default:
                Sampler traceIdRatioBased = Sampler.traceIdRatioBased(config.getSampleRatio());
                configuredSampler = Sampler
                        .parentBasedBuilder(traceIdRatioBased)
                        .setLocalParentNotSampled(traceIdRatioBased)
                        .setRemoteParentNotSampled(traceIdRatioBased)
                        .build();
                break;
        }

        dynamicSamplerWrapper.setCurrentSampler(configuredSampler);
    }

    private Map<String, String> loadDefaultConfig() {
        try (InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("default_config.json")) {
            if (inputStream == null) {
                return null;
            }
            // JDK 8 compatible way to read all bytes
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            String content = new String(buffer);
            return JsonUtil.fromJson(content, new TypeReference<Map<String, String>>() {
            });
        } catch (Exception e) {
            logger.error(e, "Failed to load default config");
            return new HashMap<>();
        }
    }

    private BatchSpanProcessor buildBatchSpanProcessor(ConfigProperties config, SpanExporter spanExporter, int maxBatchSize) {
        BatchSpanProcessorBuilder builder = BatchSpanProcessor.builder(spanExporter)
                .setExportUnsampledSpans(true);
        Duration scheduleDelay = config.getDuration("otel.bsp.schedule.delay");
        if (scheduleDelay != null) {
            builder.setScheduleDelay(scheduleDelay);
        }

        Integer maxQueue = config.getInt("otel.bsp.max.queue.size");
        if (maxQueue != null) {
            builder.setMaxQueueSize(maxQueue);
        }

        Duration timeout = config.getDuration("otel.bsp.export.timeout");
        if (timeout != null) {
            builder.setExporterTimeout(timeout);
        }
        builder.setMaxExportBatchSize(maxBatchSize);
        return builder.build();
    }

    private OtlpHttpSpanExporter buildHttpExporter() {
        OtlpHttpSpanExporterBuilder builder = OtlpHttpSpanExporter.builder();
        builder.addHeader("Content-Type", CommonConstant.TRACE_REPORT_CONTENT_TYPE);
        builder.addHeader("appName", EnvironmentConfig.CMDB_APP_NAME.getValue());
        builder.addHeader("sdkName", CommonConstant.SDK_NAME);
        builder.addHeader("topic", CommonConstant.TRACE_TOPIC_PREFIX + EnvironmentConfig.CMDB_APP_NAME.getValue() + CommonConstant.TRACE_TOPIC_SUFFIX);
        builder.setEndpoint("http://" + EnvironmentConfig.APP_NODE_IP.getValue() + CommonConstant.COLON +
                CommonConstant.TRACE_REPORT_PORT + CommonConstant.TRACE_REPORT_API);
        builder.setConnectTimeout(Duration.ofSeconds(1));
        builder.setTimeout(Duration.ofSeconds(5));
        return builder.build();
    }

    private void init() {
        Instrumentation instrumentation = InstrumentationHolder.getInstrumentation();
        if (instrumentation == null) {
            return;
        }
        new AgentBuilder.Default()
                .type(ElementMatchers.named("io.opentelemetry.sdk.trace.SdkSpan"))
                .transform((builder, typeDescription, classLoader, javaModule, protectionDomain) ->
                        builder.implement(CustomSpan.class)
                                .method(ElementMatchers.named("getStatus"))
                                .intercept(FieldAccessor.ofField("status"))
                                .method(ElementMatchers.named("getEvents"))
                                .intercept(FieldAccessor.ofField("events"))
                )
                .installOn(instrumentation);
    }

}
