package us.zoom.trace.javaagent.propagator.extractor;

import us.zoom.trace.javaagent.bootstrap.CommonConstant;
import us.zoom.trace.javaagent.common.MethodUtil;

import java.lang.invoke.MethodHandle;

public class AsyncMQRootSpanNameGetter extends LazyInitializeRootSpanNameGetter {


    private static AsyncMQRootSpanNameGetter instance;


    public static AsyncMQRootSpanNameGetter getInstance() {
        if (instance == null) {
            synchronized (AsyncMQRootSpanNameGetter.class) {
                if (instance == null) {
                    instance = new AsyncMQRootSpanNameGetter();
                }
            }
        }
        return instance;
    }

    private AsyncMQRootSpanNameGetter() {}

    private MethodHandle getTopic;


    @Override
    protected void doInit(ClassLoader classLoader) throws Throwable {
        Class<?> mainClass = classLoader.loadClass(carrierClassName());
        getTopic = MethodUtil.findMethodOrNull(mainClass, "getTopic", String.class);
    }


    @Override
    public String doGetRootSpanName(Object carrier) throws Throwable {
        if (getTopic == null) {
            return null;
        }
        return getTopic.invoke(carrier).toString() + CommonConstant.SPACE + "process";
    }

    @Override
    public String carrierClassName() {
        return "us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.ConsumeRequest";
    }
}
