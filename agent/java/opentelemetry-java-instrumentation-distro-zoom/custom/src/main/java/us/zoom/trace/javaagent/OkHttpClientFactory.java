package us.zoom.trace.javaagent;


import okhttp3.ConnectionPool;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import us.zoom.trace.javaagent.bootstrap.EnvironmentConfig;

import java.io.IOException;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class OkHttpClientFactory {

    private static final long CONNECTION_TIMEOUT = 5;
    private static final long READ_TIMEOUT = 15;
    private static final long WRITE_TIMEOUT = 15;
    private static final String AUTH_HEADER = "Authentication";
    private static final boolean AUTH_ENABLED = Boolean.parseBoolean(EnvironmentConfig.OTEL_AUTH_ENABLED.getValue());

    private static OkHttpClient INSTANCE;

    public static OkHttpClient getOkHttpClient() {
        if (INSTANCE == null) {
            synchronized (OkHttpClientFactory.class) {
                if (INSTANCE != null) {
                    return INSTANCE;
                }
                OkHttpClient.Builder builder = new OkHttpClient.Builder()
                        .connectionPool(new ConnectionPool(5, 2, TimeUnit.MINUTES))
                        .protocols(Collections.singletonList(Protocol.HTTP_1_1))
                        .connectTimeout(CONNECTION_TIMEOUT, TimeUnit.SECONDS)
                        .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                        .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                        .addInterceptor(new Interceptor() {
                            @NotNull
                            @Override
                            public Response intercept(@NotNull Chain chain) throws IOException {
                                Request sourceRequest = chain.request();
                                Request.Builder builder = sourceRequest.newBuilder();
                                builder.addHeader("Accept", "application/json");
                                return chain.proceed(builder.build());
                            }
                        });
                INSTANCE = builder.build();
            }
        }
        return INSTANCE;

    }


}
