/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.exporter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.io.SegmentedStringWriter;
import io.opentelemetry.api.logs.Severity;
import io.opentelemetry.exporter.internal.otlp.logs.ResourceLogsMarshaler;
import io.opentelemetry.sdk.common.CompletableResultCode;
import io.opentelemetry.sdk.logs.data.LogRecordData;
import io.opentelemetry.sdk.logs.export.LogRecordExporter;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;
import us.zoom.trace.javaagent.logging.FileWriter;
import us.zoom.trace.javaagent.logging.ILog;
import us.zoom.trace.javaagent.logging.LogManager;
import us.zoom.trace.javaagent.logging.LogRecordResolver;
import us.zoom.trace.javaagent.util.JsonUtil;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * A {@link LogRecordExporter} which writes {@linkplain LogRecordData logRecords} to a {@link FileWriter} in
 * JSON format. Each log line will include a single {@code ResourceSpans}.
 */
public final class FileJsonLoggingLogRecordExporter implements LogRecordExporter {

    private static final ILog logger =
            LogManager.getLogger(FileJsonLoggingLogRecordExporter.class);

    private static final ILog logRecordLogger = LogManager.getLogger(FileJsonLoggingLogRecordExporter.class, new LogRecordResolver());
    private final AtomicBoolean isShutdown = new AtomicBoolean();

    @Override
    public CompletableResultCode export(Collection<LogRecordData> logs) {
        if (!AgentConfiguration.globalConfiguration().captureErrorLogEnabled()) {
            return CompletableResultCode.ofSuccess();
        }
        if (isShutdown.get()) {
            return CompletableResultCode.ofFailure();
        }
        // only record error logs
        if (logs.isEmpty()) {
            return CompletableResultCode.ofSuccess();
        }
        logs = logs.stream()
                .filter(logRecordData -> logRecordData.getSeverity().getSeverityNumber() >= Severity.ERROR.getSeverityNumber())
                .collect(Collectors.toList());
        ResourceLogsMarshaler[] resourceLogsMarshalers = ResourceLogsMarshaler.create(logs);
        for (ResourceLogsMarshaler resourceLogsMarshaler : resourceLogsMarshalers) {
            SegmentedStringWriter sw = new SegmentedStringWriter(JsonUtil.getJsonFactory()._getBufferRecycler());
            try (JsonGenerator gen = JsonUtil.create(sw)) {
                resourceLogsMarshaler.writeJsonToGenerator(gen);
            } catch (IOException e) {
                continue;
            }
            try {
                logRecordLogger.error(sw.getAndClear());
            } catch (IOException e) {
                logger.error("Unable to read OTLP JSON log records", e);
            }
        }
        return CompletableResultCode.ofSuccess();
    }

    @Override
    public CompletableResultCode flush() {
        return CompletableResultCode.ofSuccess();
    }

    @Override
    public CompletableResultCode shutdown() {
        if (!isShutdown.compareAndSet(false, true)) {
            logger.warn("Calling shutdown() multiple times.");
        }
        return CompletableResultCode.ofSuccess();
    }

}
