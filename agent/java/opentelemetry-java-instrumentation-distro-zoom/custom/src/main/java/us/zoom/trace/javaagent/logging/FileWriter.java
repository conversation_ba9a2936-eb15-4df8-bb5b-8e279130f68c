/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.logging;

import us.zoom.trace.javaagent.bootstrap.EnvironmentConfig;
import us.zoom.trace.javaagent.util.FileUtil;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

@SuppressWarnings({
        "NullAway",
        "FieldCanBeFinal",
        "ConstantField",
        "NonFinalStaticField",
        "MemberName",
        "DefaultCharset",
        ""
})
public class FileWriter {
    private static final Logger logger = Logger.getLogger(FileWriter.class.getName());
    private static final int maxFileSize = Integer.parseInt(EnvironmentConfig.OTEL_TRACE_LOG_FILE_SIZE.getValue());
    private static final int maxFileCount = Integer.parseInt(EnvironmentConfig.OTEL_TRACE_LOG_FILE_COUNT.getValue());
    private static final String lineSeparator = EnvironmentConfig.LINE_SEPARATOR.getValue();
    private BufferedOutputStream fileBOS;
    private volatile boolean started = false;
    private final AtomicInteger fileSize = new AtomicInteger();
    private final AtomicInteger lineNum = new AtomicInteger();
    private String fileName;
    private int bufferSize;
    private File logDir;

    public FileWriter(LogType logType) {
        switch (logType) {
            case COMMON:
                fileName = "agent.log";
                bufferSize = 8192;
                logDir = FileUtil.getTraceLogDirectory().toFile();
                break;
            case TRACE:
                bufferSize = 1024 * 1024;
                fileName = "traces.log";
                logDir = FileUtil.getTraceLogDirectory().toFile();
                break;
            case LOG_RECORD:
                bufferSize = 1024 * 1024;
                fileName = "records.log";
                logDir = FileUtil.getErrorLogDirectory().toFile();
                break;
        }
    }

    public void write(byte[] message, boolean forceFlush) {
        if (hasWriteStream() && fileBOS != null) {
            try {
                lineNum.incrementAndGet();
                fileBOS.write(message);
                fileSize.addAndGet(message.length);
                fileBOS.write(lineSeparator.getBytes());
                fileSize.addAndGet(lineSeparator.length());
                if (forceFlush || lineNum.get() % 15 == 0) {
                    fileBOS.flush();
                }
            } catch (IOException e) {
                logger.log(Level.WARNING, "write message failed", e);
            } finally {
                switchFile();
            }
        }
    }

    public void write(String message, boolean forceFlush) {
        write(message.getBytes(), forceFlush);
    }

    private void switchFile() {
        if (fileSize.get() > maxFileSize) {
            try {
                if (fileBOS != null) {
                    fileBOS.flush();
                    fileBOS.close();
                }
                boolean success =
                        new File(logDir, fileName)
                                .renameTo(
                                        new File(
                                                logDir,
                                                fileName
                                                        + LocalDateTime.now(ZoneId.systemDefault())
                                                        .format(DateTimeFormatter.ofPattern(".yyyy_MM_dd_HH_mm_ss"))));

                if (!success) {
                    logger.log(Level.WARNING, "rename file({0}) failed", fileName);
                    return;
                }

                List<String> historyFileNames = new ArrayList<String>();
                Pattern p = Pattern.compile("^" + fileName + ".\\d{4}_\\d{2}_\\d{2}_\\d{2}_\\d{2}_\\d{2}");
                File[] files = logDir.listFiles();
                if (files == null) {
                    // should not happen
                    return;
                }
                for (File child : files) {
                    if (child.isFile() && p.matcher(child.getName()).find()) {
                        historyFileNames.add(child.getName());
                    }
                }
                Collections.sort(historyFileNames);
                while (historyFileNames.size() > maxFileCount) {
                    String fileName = historyFileNames.remove(0);
                    File f = new File(logDir, fileName);
                    if (f.delete()) {
                        logger.log(Level.WARNING, "delete file failed");
                    }
                }
                fileBOS = null;
                started = false;
            } catch (IOException e) {
                logger.log(Level.WARNING, "switch file failed", e);
            }
        }
    }

    private boolean hasWriteStream() {
        if (fileBOS != null) {
            return true;
        }
        if (!started) {
            if (!logDir.exists()) {
                if (!logDir.mkdirs()) {
                    logger.log(Level.WARNING, "create dir({0}) failed.", logDir.getPath());
                }
            } else if (!logDir.isDirectory()) {
                logger.log(Level.WARNING, "Log dir({0}) is not a directory.", logDir.getPath());
            }
            File file = new File(logDir, fileName);
            try {
                if (!file.exists() && !file.createNewFile()) {
                    logger.log(Level.WARNING, "create file({0}) failed.", file.getPath());
                }
                FileOutputStream fileOutputStream =
                        new FileOutputStream(file, true);
                fileBOS = new BufferedOutputStream(fileOutputStream, this.bufferSize);
                fileSize.set(Long.valueOf(new File(logDir, fileName).length()).intValue());
            } catch (IOException e) {
                logger.log(Level.WARNING, "create file failed or file not found", e);
            }
            started = true;
        }

        return fileBOS != null;
    }
}
