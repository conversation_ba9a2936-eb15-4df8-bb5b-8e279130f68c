/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.logging;

import com.lmax.disruptor.EventFactory;
import com.lmax.disruptor.EventHandler;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.dsl.Disruptor;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

/**
 * The <code>FileWriter</code> support async file output, by using a queue as buffer.
 */
@SuppressWarnings({"NullAway", "FieldCanBeFinal", "ConstantField", "NonFinalStaticField"})
public class FileWriterFactory implements EventHandler<FileWriterFactory.MessageHolder> {
    private static volatile FileWriterFactory INSTANCE;
    private static final Object CREATE_LOCK = new Object();
    private Disruptor<MessageHolder> disruptor;
    private RingBuffer<MessageHolder> buffer;
    private final Map<LogType, FileWriter> proxies;

    public static FileWriterFactory get() {
        if (INSTANCE == null) {
            synchronized (CREATE_LOCK) {
                if (INSTANCE == null) {
                    INSTANCE = new FileWriterFactory();
                }
            }
        }
        return INSTANCE;
    }

    private FileWriterFactory() {
        disruptor =
                new Disruptor<MessageHolder>(
                        new EventFactory<MessageHolder>() {
                            @Override
                            public MessageHolder newInstance() {
                                return new MessageHolder();
                            }
                        },
                        1024,
                        r -> {
                            Thread t = new Thread(r, "TraceLogWriter-" + new Random().nextInt());
                            t.setDaemon(true);
                            return t;
                        });
        disruptor.handleEventsWith(this);
        buffer = disruptor.getRingBuffer();
        disruptor.start();
        this.proxies = new ConcurrentHashMap<LogType, FileWriter>();
        for (LogType logType : LogType.values()) {
            proxies.put(logType, new FileWriter(logType));
        }
    }

    @Override
    public void onEvent(MessageHolder event, long sequence, boolean endOfBatch) {
        try {
            byte[] message = event.getMessage();
            proxies.get(event.getLogType()).write(message, endOfBatch);
        } finally {
            event.setMessage(new byte[0]);
        }
    }

    public void write(String message, LogType logType) {
        long next = buffer.next();
        try {
            MessageHolder holder = buffer.get(next);
            holder.setMessage(message.getBytes(StandardCharsets.UTF_8));
            holder.setLogType(logType);
        } finally {
            buffer.publish(next);
        }
    }

    public void write(byte[] messageByte, LogType logType) {
        long next = buffer.next();
        try {
            MessageHolder holder = buffer.get(next);
            holder.setMessage(messageByte);
            holder.setLogType(logType);
        } finally {
            buffer.publish(next);
        }
    }

    public static class MessageHolder {
        private byte[] message;
        private LogType logType;

        public byte[] getMessage() {
            return message;
        }

        public void setMessage(byte[] message) {
            this.message = message;
        }

        public LogType getLogType() {
            return logType;
        }

        public void setLogType(LogType logType) {
            this.logType = logType;
        }
    }
}
