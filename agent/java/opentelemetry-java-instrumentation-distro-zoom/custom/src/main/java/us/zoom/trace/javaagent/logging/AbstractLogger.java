package us.zoom.trace.javaagent.logging;

import us.zoom.trace.javaagent.bootstrap.EnvironmentConfig;
import us.zoom.trace.javaagent.util.StringUtil;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;

public abstract class AbstractLogger implements ILog {

    private Class<?> targetClass;
    private static final ThreadLocal<SimpleDateFormat> simpleDateFormatThreadLocal = ThreadLocal.withInitial(
            () -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS"));

    private final String serviceName = EnvironmentConfig.CMDB_APP_NAME.getValue();

    public AbstractLogger(Class<?> targetClass) {
        this.targetClass = targetClass;
    }

    protected abstract void logger(LogLevel level, String message, Throwable e);

    private String replaceParam(String message, Object... parameters) {
        int startSize = 0;
        int parametersIndex = 0;
        int index;
        String tmpMessage = message;
        while ((index = message.indexOf("{}", startSize)) != -1) {
            if (parametersIndex >= parameters.length) {
                break;
            }
            /**
             * @Fix the Illegal group reference issue
             */
            tmpMessage = tmpMessage.replaceFirst("\\{\\}",
                    Matcher.quoteReplacement(String.valueOf(parameters[parametersIndex++])));
            startSize = index + 2;
        }
        return tmpMessage;
    }

    private static final String LEFT_SQUARE = "[";
    private static final String RIGHT_SQUARE = "]";
    private static final String EMPTY_STR = "";
    private static final String DOTS_CHAR = ",,,,";
    private static final String DOT_CHAR = ",";

    String format(LogLevel level, String message, Throwable t) {
        return StringUtil.join(EMPTY_STR,
                simpleDateFormatThreadLocal.get().format(new Date()),
                LEFT_SQUARE, DOT_CHAR, serviceName, DOTS_CHAR, RIGHT_SQUARE,
                LEFT_SQUARE, Thread.currentThread().getName(), RIGHT_SQUARE,
                LEFT_SQUARE, level.name(), RIGHT_SQUARE,
                LEFT_SQUARE, targetClass.getSimpleName(), RIGHT_SQUARE,
                message,
                t == null ? EMPTY_STR : format(t)
        );
    }

    String format(Throwable t) {
        ByteArrayOutputStream buf = new ByteArrayOutputStream();
        t.printStackTrace(new java.io.PrintWriter(buf, true));
        String expMessage = buf.toString();
        try {
            buf.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return EnvironmentConfig.LINE_SEPARATOR.getValue() + expMessage;
    }

    @Override
    public void info(String format) {
        if (isInfoEnable()) {
            logger(LogLevel.INFO, format, null);
        }
    }

    @Override
    public void info(String format, Object... arguments) {
        if (isInfoEnable()) {
            logger(LogLevel.INFO, replaceParam(format, arguments), null);
        }
    }

    @Override
    public void warn(String format, Object... arguments) {
        if (isWarnEnable()) {
            logger(LogLevel.WARN, replaceParam(format, arguments), null);
        }
    }

    @Override
    public void warn(Throwable e, String format, Object... arguments) {
        if (isWarnEnable()) {
            logger(LogLevel.WARN, replaceParam(format, arguments), e);
        }
    }

    @Override
    public void error(String format, Throwable e) {
        if (isErrorEnable()) {
            logger(LogLevel.ERROR, format, e);
        }
    }

    @Override
    public void error(Throwable e, String format, Object... arguments) {
        if (isErrorEnable()) {
            logger(LogLevel.ERROR, replaceParam(format, arguments), e);
        }
    }

    @Override
    public boolean isDebugEnable() {
        return false;
    }

    @Override
    public boolean isInfoEnable() {
        return true;
    }

    @Override
    public boolean isWarnEnable() {
        return true;
    }

    @Override
    public boolean isErrorEnable() {
        return true;
    }

    @Override
    public void debug(String format) {
        if (isDebugEnable()) {
            logger(LogLevel.DEBUG, format, null);
        }
    }

    @Override
    public void debug(String format, Object... arguments) {
        if (isDebugEnable()) {
            logger(LogLevel.DEBUG, replaceParam(format, arguments), null);
        }
    }

    @Override
    public void error(String format) {
        if (isErrorEnable()) {
            logger(LogLevel.ERROR, format, null);
        }
    }

}
