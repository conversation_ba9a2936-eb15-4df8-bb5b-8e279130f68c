package us.zoom.trace.javaagent.auth;

/**
 * <AUTHOR>
 */
public class JwtPrivateKeyResponse {

    public static final JwtPrivateKeyResponse DEFAULT_RESPONSE = new JwtPrivateKeyResponse();

    private String issuer;
    private String privateKey;
    private String fingerprint;
    private String path;
    private boolean success;


    public JwtPrivateKeyResponse(String issuer, String privateKey, String fingerprint, String path) {
        if (issuer == null || issuer.isEmpty()) {
            throw new IllegalArgumentException("issuer can't be null or empty");
        }
        if (privateKey == null || privateKey.isEmpty()) {
            throw new IllegalArgumentException("privateKey can't be null or empty");
        }
        if (fingerprint == null || fingerprint.isEmpty()) {
            throw new IllegalArgumentException("fingerprint can't be null or empty");
        }
        if (path == null || path.isEmpty()) {
            throw new IllegalArgumentException("path can't be null or empty");
        }
        this.issuer = issuer;
        this.privateKey = privateKey;
        this.fingerprint = fingerprint;
        this.path = path;
        this.success = true;
    }

    public JwtPrivateKeyResponse() {
        this.success = false;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getFingerprint() {
        return fingerprint;
    }

    public void setFingerprint(String fingerprint) {
        this.fingerprint = fingerprint;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
