package us.zoom.trace.javaagent.processor;

import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.common.AttributesBuilder;
import io.opentelemetry.api.common.Value;
import io.opentelemetry.api.logs.Severity;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.context.Context;
import io.opentelemetry.sdk.logs.LogRecordProcessor;
import io.opentelemetry.sdk.logs.ReadWriteLogRecord;
import io.opentelemetry.sdk.trace.ReadWriteSpan;
import io.opentelemetry.semconv.ExceptionAttributes;
import us.zoom.trace.javaagent.common.AttributeKeys;

import java.util.concurrent.TimeUnit;

public class LogRecordEnhanceProcessor implements LogRecordProcessor {

    public LogRecordEnhanceProcessor() {
    }

    @Override
    public void onEmit(Context context, ReadWriteLogRecord readWriteLogRecord) {
        Span current = Span.current();
        if (readWriteLogRecord.getSeverity().getSeverityNumber() < Severity.ERROR.getSeverityNumber()) {
            return;
        }
        if (current.getSpanContext().isValid() && current instanceof ReadWriteSpan) {
            ReadWriteSpan readWriteSpan = (ReadWriteSpan) current;
            readWriteLogRecord.setAttribute(AttributeKeys.SPAN_KIND_ATTRIBUTE, readWriteSpan.getKind().name());
            readWriteLogRecord.setAttribute(AttributeKeys.SPAN_NAME_ATTRIBUTE, readWriteSpan.getName());
            readWriteLogRecord.setAttribute(AttributeKeys.COMPONENT_ATTRIBUTE, readWriteSpan.getInstrumentationScopeInfo().getName());
            if (readWriteSpan.getAttribute(AttributeKeys.POST_SAMPLED_FLAG_ATTRIBUTE) == null && !current.getSpanContext().isSampled()) {
                readWriteSpan.setAttribute(AttributeKeys.POST_SAMPLED_FLAG_ATTRIBUTE, true);
            }
            String exceptionType = readWriteLogRecord.getAttribute(ExceptionAttributes.EXCEPTION_TYPE);
            String stackTrace = readWriteLogRecord.getAttribute(ExceptionAttributes.EXCEPTION_STACKTRACE);
            String message = readWriteLogRecord.getAttribute(ExceptionAttributes.EXCEPTION_MESSAGE);
            Value<?> bodyValue = readWriteLogRecord.getBodyValue();
            AttributesBuilder builder = Attributes.builder();
            if (exceptionType != null) {
                builder.put(ExceptionAttributes.EXCEPTION_TYPE, exceptionType);
            }
            if (stackTrace != null) {
                builder.put(ExceptionAttributes.EXCEPTION_STACKTRACE, stackTrace);
            }
            if (message != null) {
                builder.put(ExceptionAttributes.EXCEPTION_MESSAGE, message);
            }
            String errorMessage;
            if (bodyValue != null && (errorMessage = bodyValue.asString()) != null) {
                builder.put(AttributeKeys.ERROR_MESSAGE, errorMessage);
            }
            readWriteSpan.addEvent(readWriteLogRecord.getInstrumentationScopeInfo().getName(), builder.build(), readWriteLogRecord.getTimestampEpochNanos(), TimeUnit.NANOSECONDS);
            readWriteSpan.setStatus(StatusCode.ERROR);
        }
    }
}
