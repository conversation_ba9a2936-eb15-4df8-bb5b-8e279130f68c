/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.exporter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.io.SegmentedStringWriter;
import io.opentelemetry.exporter.internal.otlp.traces.ResourceSpansMarshaler;
import io.opentelemetry.sdk.common.CompletableResultCode;
import io.opentelemetry.sdk.trace.data.SpanData;
import io.opentelemetry.sdk.trace.export.SpanExporter;
import us.zoom.trace.javaagent.logging.FileWriter;
import us.zoom.trace.javaagent.logging.ILog;
import us.zoom.trace.javaagent.logging.LogManager;
import us.zoom.trace.javaagent.logging.TraceLogResolver;
import us.zoom.trace.javaagent.util.JsonUtil;

import java.io.IOException;
import java.util.Collection;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * A {@link SpanExporter} which writes {@linkplain SpanData spans} to a {@link FileWriter} in
 * OTLP JSON format. Each log line will include a single {@code ResourceSpans}.
 */
public final class FileJsonLoggingSpanExporter implements SpanExporter {

    private static final ILog logger =
            LogManager.getLogger(FileJsonLoggingSpanExporter.class);

    private static final ILog spanLogger = LogManager.getLogger(FileJsonLoggingSpanExporter.class, new TraceLogResolver());

    private final AtomicBoolean isShutdown = new AtomicBoolean();


    /**
     * Returns a new {@link FileJsonLoggingSpanExporter}.
     */
    public static SpanExporter create() {
        return new FileJsonLoggingSpanExporter();
    }

    private FileJsonLoggingSpanExporter() {
    }

    @Override
    public CompletableResultCode export(Collection<SpanData> spans) {
        if (isShutdown.get()) {
            return CompletableResultCode.ofFailure();
        }

        ResourceSpansMarshaler[] allResourceSpans = ResourceSpansMarshaler.create(spans);
        for (ResourceSpansMarshaler resourceSpans : allResourceSpans) {
            SegmentedStringWriter sw =
                    new SegmentedStringWriter(JsonUtil.getJsonFactory()._getBufferRecycler());
            try (JsonGenerator gen = JsonUtil.create(sw)) {
                resourceSpans.writeJsonToGenerator(gen);
            } catch (IOException e) {
                // Shouldn't happen in practice, just skip it.
                continue;
            }
            try {
                spanLogger.info(sw.getAndClear());
                return CompletableResultCode.ofSuccess();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return CompletableResultCode.ofFailure();
    }

    @Override
    public CompletableResultCode flush() {
        return CompletableResultCode.ofSuccess();
    }

    @Override
    public CompletableResultCode shutdown() {
        if (!isShutdown.compareAndSet(false, true)) {
            logger.warn("Calling shutdown() multiple times.");
        }
        return CompletableResultCode.ofSuccess();
    }
}
