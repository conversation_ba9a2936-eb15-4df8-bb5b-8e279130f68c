package us.zoom.trace.javaagent.auth.csms;


import us.zoom.trace.javaagent.auth.JwtPrivateKeyResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * authenticate service
 */
public class CsmsConfigServiceManager implements CsmsConfigService {

    private static final class InstanceHolder {
        private static final CsmsConfigServiceManager instance = new CsmsConfigServiceManager();
    }

    public static CsmsConfigServiceManager getInstance() {
        return InstanceHolder.instance;
    }

    private List<CsmsConfigService> csmsConfigServices = new ArrayList<>();

    private CsmsConfigServiceManager() {
        csmsConfigServices.add(new CompatibleCsmsConfigService());
        csmsConfigServices.add(new DefaultCsmsConfigService());
    }

    @Override
    public JwtPrivateKeyResponse getJwtPrivateKey() {
        JwtPrivateKeyResponse jwtPrivateKey = null;
        for (CsmsConfigService csmsConfigService : csmsConfigServices) {
            jwtPrivateKey = csmsConfigService.getJwtPrivateKey();
            if (jwtPrivateKey.isSuccess()) {
                return jwtPrivateKey;
            }
        }
        return jwtPrivateKey == null ? JwtPrivateKeyResponse.DEFAULT_RESPONSE : jwtPrivateKey;
    }
}
