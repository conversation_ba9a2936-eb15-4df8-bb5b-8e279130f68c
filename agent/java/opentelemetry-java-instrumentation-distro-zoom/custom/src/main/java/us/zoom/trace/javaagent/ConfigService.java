package us.zoom.trace.javaagent;

import com.fasterxml.jackson.core.type.TypeReference;
import org.jetbrains.annotations.NotNull;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;
import us.zoom.trace.javaagent.bootstrap.AgentConfigurationRefreshResult;
import us.zoom.trace.javaagent.logging.ILog;
import us.zoom.trace.javaagent.logging.LogManager;
import us.zoom.trace.javaagent.util.JsonUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static us.zoom.trace.javaagent.bootstrap.EnvironmentConfig.TRACES_DYNAMIC_CONFIG_FILE_PATH;

/**
 * <AUTHOR>
 */
public class ConfigService {

    private static final ILog logger = LogManager.getLogger(ConfigService.class);

    private final List<Listener> listeners;

    private final ScheduledExecutorService executor;

    private final AgentConfigurationServiceClient remoteConfigReader;

    public ConfigService() {
        this.listeners = new ArrayList<>();
        this.executor = Executors.newScheduledThreadPool(1, new ThreadFactory() {

            private final AtomicInteger atomicThreadNumber = new AtomicInteger(1);
            @Override
            public Thread newThread(@NotNull Runnable r) {
                Thread thread = new Thread(r);
                thread.setName("Agent-configService-thread-" + atomicThreadNumber.getAndIncrement());
                thread.setDaemon(true);
                return thread;
            }
        });

        remoteConfigReader = new AgentConfigurationServiceClient();
    }

    public void start() {
        executor.scheduleWithFixedDelay(this::refreshAgentConfiguration,
                30, 120, TimeUnit.SECONDS);
    }

    public void registerListener(Listener listener) {
        listeners.add(listener);
    }

    private void refreshAgentConfiguration() {

        AgentConfiguration agentConfiguration = AgentConfiguration.globalConfiguration();
        AgentConfigurationRefreshResult refreshResult = remoteConfigReader.synchronize(agentConfiguration);
        boolean refreshed = false;
        if (!refreshResult.isSuccess() && TRACES_DYNAMIC_CONFIG_FILE_PATH.getValue() != null) {
            File file = new File(TRACES_DYNAMIC_CONFIG_FILE_PATH.getValue());
            if (!file.exists()) {
                logger.error(null, "config file not exist in path {}", TRACES_DYNAMIC_CONFIG_FILE_PATH.getValue());
                return;
            }
            try (InputStream inputStream = new FileInputStream(file)) {
                byte[] buffer = new byte[inputStream.available()];
                inputStream.read(buffer);
                String content = new String(buffer);
                AgentConfiguration localConfig = JsonUtil.fromJson(content, new TypeReference<AgentConfiguration>() {
                });
                if (localConfig != null && file.lastModified() > agentConfiguration.getTimestamp()) {
                    localConfig.setTimestamp(file.lastModified());
                    agentConfiguration = localConfig;
                    refreshed = true;
                }
            } catch (Throwable e) {
                logger.error(e, "load dynamic config from local file system failed");
            }
        } else if (refreshResult.getAgentConfiguration() != null) {
            agentConfiguration = refreshResult.getAgentConfiguration();
            refreshed = true;
        }
        if (refreshed) {
            AgentConfiguration.setGlobalConfiguration(agentConfiguration);
            if (listeners.isEmpty()) {
                return;
            }
            listeners.forEach(listener -> {
                try {
                    listener.onChanged(AgentConfiguration.globalConfiguration());
                } catch (Throwable e) {
                    logger.error(e, "Failed to notify listener, listener: {}",
                            listener.getClass().getSimpleName());
                }
            });
        }
    }
}
