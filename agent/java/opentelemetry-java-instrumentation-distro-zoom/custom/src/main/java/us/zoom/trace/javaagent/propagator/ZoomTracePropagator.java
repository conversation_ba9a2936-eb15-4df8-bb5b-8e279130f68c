package us.zoom.trace.javaagent.propagator;

import io.opentelemetry.context.Context;
import io.opentelemetry.context.propagation.TextMapGetter;
import io.opentelemetry.context.propagation.TextMapPropagator;
import io.opentelemetry.context.propagation.TextMapSetter;
import org.jetbrains.annotations.Nullable;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;
import us.zoom.trace.javaagent.bootstrap.EnvironmentConfig;
import us.zoom.trace.javaagent.common.ZoomTrackingContext;
import us.zoom.trace.javaagent.propagator.extractor.AsyncMQRootSpanNameGetter;
import us.zoom.trace.javaagent.propagator.extractor.RootSpanNameGetterManager;
import us.zoom.trace.javaagent.propagator.extractor.TomcatRootSpanNameGetter;
import us.zoom.trace.javaagent.propagator.extractor.UndertowRootSpanNameGetter;

import java.util.*;

import static us.zoom.trace.javaagent.common.AttributeKeys.ZM_TRACE_UPSTREAM;
import static us.zoom.trace.javaagent.common.AttributeKeys.ZM_TRACKING_ID;

/**
 * <AUTHOR>
 */
public class ZoomTracePropagator implements TextMapPropagator {

    private static volatile ZoomTracePropagator instance;

    private ZoomTracePropagator() {
        RootSpanNameGetterManager.getInstance().registerRootSpanNameGetter(AsyncMQRootSpanNameGetter.getInstance());
        RootSpanNameGetterManager.getInstance().registerRootSpanNameGetter(TomcatRootSpanNameGetter.getInstance());
        RootSpanNameGetterManager.getInstance().registerRootSpanNameGetter(UndertowRootSpanNameGetter.getInstance());
    }

    public static ZoomTracePropagator getInstance() {
        if (instance == null) {
            synchronized (ZoomTracePropagator.class) {
                if (instance == null) {
                    instance = new ZoomTracePropagator();
                }
            }
        }
        return instance;
    }

    @Override
    public Collection<String> fields() {
        return new ArrayList<String>() {{
            add(ZM_TRACKING_ID.getKey());
        }};
    }

    @Override
    public <C> void inject(Context context, @Nullable C carrier, TextMapSetter<C> setter) {
        //inject service name
        String appName = EnvironmentConfig.CMDB_APP_NAME.getValue();
        if (appName != null && !appName.equals(EnvironmentConfig.CMDB_APP_NAME.getDefaultValue())) {
            setter.set(carrier, ZM_TRACE_UPSTREAM.getKey(), appName);
        }
        Set<String> propagableKeys = AgentConfiguration.globalConfiguration().getPropagableKeys();
        ZoomTrackingContext zoomTrackingContext = context.get(ZoomTrackingContext.CONTEXT_KEY);
        if (propagableKeys == null || propagableKeys.isEmpty() || zoomTrackingContext == null
                || zoomTrackingContext.getExtraTags() == null || zoomTrackingContext.getExtraTags().isEmpty()) {
            return;
        }
        for (String propagableKey : propagableKeys) {
            Map<String, String> extraTags = zoomTrackingContext.getExtraTags();
            String value = extraTags.get(propagableKey);
            if (value != null) {
                setter.set(carrier, propagableKey, value);
            }
        }
    }

    @Override
    public <C> Context extract(Context context, @Nullable C carrier, TextMapGetter<C> getter) {
        if (context == null) {
            return Context.root();
        }
        String trackingIdHeader = getter.get(carrier, ZM_TRACKING_ID.getKey());
        ZoomTrackingContext zmContext;
        try {
            zmContext = ZoomTrackingContext.fromField(trackingIdHeader);
        } catch (RuntimeException e) {
            return context;
        }
        Set<String> recordKeys = AgentConfiguration.globalConfiguration().getRecordKeys();
        if (recordKeys != null && !recordKeys.isEmpty()) {
            Map<String, String> extraTags = new HashMap<>(recordKeys.size());
            for (String recordKey : recordKeys) {
                String value = getter.get(carrier, recordKey);
                if (value == null) {
                    continue;
                }
                extraTags.put(recordKey, value);
            }
            zmContext.setExtraTags(extraTags);
        }
        String upstreamServiceName = getter.get(carrier, ZM_TRACE_UPSTREAM.getKey());
        if (upstreamServiceName != null) {
            zmContext.setUpstreamServiceName(upstreamServiceName);
        }
        String rootSpanName = RootSpanNameGetterManager.getInstance().getRootSpanName(carrier);
        if (rootSpanName != null) {
            zmContext.setRootName(rootSpanName);
        }

        return context.with(zmContext);
    }
}
