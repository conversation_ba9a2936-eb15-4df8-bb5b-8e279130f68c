package us.zoom.trace.javaagent.propagator.extractor;

import us.zoom.trace.javaagent.bootstrap.CommonConstant;
import us.zoom.trace.javaagent.common.MethodUtil;

import java.lang.invoke.MethodHandle;

public class UndertowRootSpanNameGetter extends LazyInitializeRootSpanNameGetter {


    private static UndertowRootSpanNameGetter instance;


    public static UndertowRootSpanNameGetter getInstance() {
        if (instance == null) {
            synchronized (UndertowRootSpanNameGetter.class) {
                if (instance == null) {
                    instance = new UndertowRootSpanNameGetter();
                }
            }
        }
        return instance;
    }

    private UndertowRootSpanNameGetter() {}

    private MethodHandle uri;
    private MethodHandle method;

    @Override
    protected void doInit(ClassLoader classLoader) throws Throwable {
        Class<?> mainClass = classLoader.loadClass(carrierClassName());
        uri = MethodUtil.findMethodOrNull(mainClass, "getRequestURI", String.class);
        method = MethodUtil.findMethodOrNull(mainClass, "getRequestMethod", classLoader.loadClass("io.undertow.util.HttpString"));
    }


    @Override
    public String doGetRootSpanName(Object carrier) throws Throwable {
        if (uri == null || method == null) {
            return null;
        }
        return method.invoke(carrier).toString() + CommonConstant.SPACE + uri.invoke(carrier).toString();
    }

    @Override
    public String carrierClassName() {
        return "io.undertow.server.HttpServerExchange";
    }
}
