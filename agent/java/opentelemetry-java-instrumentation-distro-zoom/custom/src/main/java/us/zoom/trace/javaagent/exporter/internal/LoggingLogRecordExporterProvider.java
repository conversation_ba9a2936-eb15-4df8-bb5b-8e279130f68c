/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.exporter.internal;

import io.opentelemetry.sdk.autoconfigure.spi.ConfigProperties;
import io.opentelemetry.sdk.autoconfigure.spi.logs.ConfigurableLogRecordExporterProvider;
import io.opentelemetry.sdk.logs.export.LogRecordExporter;
import io.opentelemetry.sdk.trace.export.SpanExporter;
import us.zoom.trace.javaagent.exporter.FileJsonLoggingLogRecordExporter;
import us.zoom.trace.javaagent.exporter.FileJsonLoggingSpanExporter;

/**
 * {@link LogRecordExporter} SPI implementation for {@link FileJsonLoggingLogRecordExporter}.
 *
 * <p>This class is internal and is hence not for public use. Its APIs are unstable and can change
 * at any time.
 */
public class LoggingLogRecordExporterProvider implements ConfigurableLogRecordExporterProvider {

    @Override
    public LogRecordExporter createExporter(ConfigProperties config) {
        return new FileJsonLoggingLogRecordExporter();
    }

    @Override
    public String getName() {
        return "logging-file";
    }
}
