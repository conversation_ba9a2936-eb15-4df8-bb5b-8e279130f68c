package us.zoom.trace.javaagent.util;

import us.zoom.trace.javaagent.bootstrap.EnvironmentConfig;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 */
public class FileUtil {

    private static final Path BASIC_PATH = Paths.get(EnvironmentConfig.APPLICATION_LOG_PATH.getValue(), "zoom_middleware");

    public static Path getTraceLogDirectory() {
        return Paths.get(BASIC_PATH.toString(), "trace", EnvironmentConfig.CMDB_APP_NAME.getValue());
    }

    public static Path getErrorLogDirectory() {
        return Paths.get(BASIC_PATH.toString(), "log", EnvironmentConfig.CMDB_APP_NAME.getValue());
    }
}
