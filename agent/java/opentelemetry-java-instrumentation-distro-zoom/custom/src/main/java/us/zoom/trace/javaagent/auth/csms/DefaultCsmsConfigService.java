package us.zoom.trace.javaagent.auth.csms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import us.zoom.trace.javaagent.OkHttpClientFactory;
import us.zoom.trace.javaagent.auth.JwtAlgorithmSupport;
import us.zoom.trace.javaagent.auth.JwtPrivateKeyResponse;
import us.zoom.trace.javaagent.bootstrap.EnvironmentConfig;
import us.zoom.trace.javaagent.logging.ILog;
import us.zoom.trace.javaagent.logging.LogManager;
import us.zoom.trace.javaagent.util.JsonUtil;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DefaultCsmsConfigService implements CsmsConfigService {


    private static final ILog logger = LogManager.getLogger(DefaultCsmsConfigService.class);

    private static final String CSMS_API = EnvironmentConfig.CSMS_ENDPOINTS.getValue() + "/api/1.0/getSecret";

    private static final String TICKET_HEADER = "Ticket";

    private static final String AUTH_TYPE_HEADER = "AuthType";

    private static final String AUTH_TYPE_HEADER_VALUE = "token";
    private static final String CURRENT_TAG = "CURRENT";

    private static final String SIGNER_NAME = "signerName";


    @Override
    public JwtPrivateKeyResponse getJwtPrivateKey() {
        String ticket = getTicket();
        if (ticket == null) {
            throw new RuntimeException("cannot get csms ticket, will skip get token");
        }
        OkHttpClient okHttpClient = OkHttpClientFactory.getOkHttpClient();
        String url = CSMS_API + "?prefixPath=" + EnvironmentConfig.CSMS_APP_PATH.getValue() + "&pageSize=1&alias=jwtSigningPrivate&strict=false";
        Request request = new Request.Builder()
                .url(url)
                .addHeader(TICKET_HEADER, ticket)
                .addHeader(AUTH_TYPE_HEADER, AUTH_TYPE_HEADER_VALUE)
                .get()
                .build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            boolean successful = response.isSuccessful();
            if (!successful) {
                throw new RuntimeException("request failed, response code: " + response.code());
            }
            if (response.body() == null) {
                logger.warn("get empty response body");
                throw new RuntimeException("get empty response body");
            }
            String bodyStr = response.body().string();
            CsmsResponse csmsResponse = JsonUtil.fromJson(bodyStr, CsmsResponse.class);
            if (csmsResponse == null || !csmsResponse.isSuccess()) {
                throw new RuntimeException("csms return failed, response body: " + bodyStr);
            } else if (csmsResponse.getSecret() == null || csmsResponse.getSecret().isEmpty() ||
                    csmsResponse.getSecret().get(0).getVersionList() == null) {
              throw new RuntimeException("csms return empty value");
            }
            SecretGroup secretGroup = csmsResponse.getSecret().get(0);
            List<VersionValue> versionList = secretGroup.getVersionList();
            VersionValue versionValue = versionList.stream()
                    .filter(sc -> Objects.equals(sc.getStage(), CURRENT_TAG)).findFirst().orElse(null);
            if (versionValue == null) {
               throw new RuntimeException("can't find current version value");
            }
            String privateKey = versionValue.getSecret();
            String genAlgo = versionValue.getGenAlgo();
            String fingerprint = null, issuer = null;
            if (privateKey != null && genAlgo != null) {
                fingerprint = JwtAlgorithmSupport.getFingerprint(privateKey, genAlgo);
            }
            Map<String, Object> properties = secretGroup.getProperties();
            if (properties != null) {
                issuer = (String) properties.get(SIGNER_NAME);
            }
            return new JwtPrivateKeyResponse(issuer, privateKey, fingerprint, secretGroup.getPrefixPath());
        } catch (Throwable e) {
            logger.error(e, "refresh jwt failed");
            return JwtPrivateKeyResponse.DEFAULT_RESPONSE;
        }
    }

    private String getTicket() {
        try {
            String filePath = EnvironmentConfig.AWS_WEB_IDENTITY_TOKEN_FILE.getValue();
            String roleArn = EnvironmentConfig.AWS_ROLE_ARN.getValue();
            if (filePath == null || roleArn == null) {
                logger.warn("AWS_WEB_IDENTITY_TOKEN_FILE {} is empty or AWS_ROLE_ARN {} is empty", filePath, roleArn);
                return null;
            }
            File file = new File(filePath);
            if (!file.exists()) {
                logger.warn("cannot find file for AWS_WEB_IDENTITY_TOKEN_FILE, file path: {}", filePath);
                return null;
            }
            // JDK 8 compatible way to read file content
            byte[] fileBytes = Files.readAllBytes(file.toPath());
            String awsToken = new String(fileBytes);
            String encodedAwsToken = Base64.getEncoder().encodeToString(awsToken.getBytes()).trim();
            String encodedRoleArn = Base64.getEncoder().encodeToString(roleArn.getBytes()).trim();
            return encodedRoleArn + "." + encodedAwsToken;
        } catch (Throwable e) {
            logger.error(e, "refresh csms ticket failed");
            return null;
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CsmsResponse {
        private boolean success;
        private List<SecretGroup> secret;

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public List<SecretGroup> getSecret() {
            return secret;
        }

        public void setSecret(List<SecretGroup> secret) {
            this.secret = secret;
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SecretGroup {
        private Map<String, Object> properties;
        private List<VersionValue> versionList;
        private String prefixPath;

        public Map<String, Object> getProperties() {
            return properties;
        }

        public void setProperties(Map<String, Object> properties) {
            this.properties = properties;
        }

        public List<VersionValue> getVersionList() {
            return versionList;
        }

        public void setVersionList(List<VersionValue> versionList) {
            this.versionList = versionList;
        }

        public String getPrefixPath() {
            return prefixPath;
        }

        public void setPrefixPath(String prefixPath) {
            this.prefixPath = prefixPath;
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VersionValue {
        private String versionId;
        private String secret;
        private String genAlgo;
        private String stage;

        public String getVersionId() {
            return versionId;
        }

        public void setVersionId(String versionId) {
            this.versionId = versionId;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }

        public String getGenAlgo() {
            return genAlgo;
        }

        public void setGenAlgo(String genAlgo) {
            this.genAlgo = genAlgo;
        }

        public String getStage() {
            return stage;
        }

        public void setStage(String stage) {
            this.stage = stage;
        }
    }


}
