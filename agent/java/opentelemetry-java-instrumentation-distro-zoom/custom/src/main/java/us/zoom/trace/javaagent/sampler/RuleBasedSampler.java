package us.zoom.trace.javaagent.sampler;

import io.opentelemetry.api.baggage.Baggage;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class RuleBasedSampler {

    private static final class InstanceHolder {
        private static final RuleBasedSampler INSTANCE = new RuleBasedSampler();
    }

    public static RuleBasedSampler getInstance() {
        return InstanceHolder.INSTANCE;
    }


    private static final String SPAN_NAME_KEY = "spanName";

    private Map<String, List<Map<AttributeKey<String>, Pattern>>> rules;

    private RuleBasedSampler() {}

    public void setRules(Map<String, List<Map<AttributeKey<String>, Pattern>>> rules) {
        this.rules = rules;
    }

    public boolean sampleForExcludeRules(Attributes attributes, String name, Baggage baggage) {
        // Exclude samples based on the rules provided
        List<Map<AttributeKey<String>, Pattern>> excludes = rules.get("exclude");
        return tryMatch(attributes, name, baggage, excludes);
    }
    public boolean sampleForIncludeRules(Attributes attributes, String name, Baggage baggage) {
        // Include samples based on the rules provided
        List<Map<AttributeKey<String>, Pattern>> includes = rules.get("include");
        return tryMatch(attributes, name, baggage, includes);
    }

    private boolean tryMatch(Attributes attributes, String name, Baggage baggage, List<Map<AttributeKey<String>, Pattern>> patterns) {
        if (patterns != null && !patterns.isEmpty()) {
            for (Map<AttributeKey<String>, Pattern> patternMap : patterns) {
                boolean include = matchPattern(name, attributes, baggage, patternMap);
                if (include) {
                    return include;
                }
            }
        }
        return false;
    }

    private boolean matchPattern(String name, Attributes attributes, Baggage baggage, Map<AttributeKey<String>, Pattern> patternMap) {
        Set<AttributeKey<String>> attributeKeys = patternMap.keySet();
        for (AttributeKey<String> key : attributeKeys) {
            String value;
            if (!key.getKey().equals(SPAN_NAME_KEY)) {
                value = attributes.get(key);
                if (value == null) {
                    value = baggage.getEntryValue(key.getKey());
                }
            } else {
                value = name;
            }
            if (value == null || !patternMap.get(key).matcher(value).find()) {
                return false;
            }
        }
        return true;
    }
}
