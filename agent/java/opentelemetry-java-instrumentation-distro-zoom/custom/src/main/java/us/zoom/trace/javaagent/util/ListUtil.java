package us.zoom.trace.javaagent.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ListUtil {

    public static <T> Collection<Collection<T>> splitCollection(Collection<T> collection, int batchSize) {
        if (collection == null || collection.isEmpty() || batchSize <= 0) {
            return Collections.emptyList();
        }
        Collection<Collection<T>> result = new ArrayList<>();
        List<T> batch = new ArrayList<>(batchSize);
        for (T t : collection) {
            batch.add(t);
            if (batch.size() == batchSize) {
                result.add(batch);
                batch = new ArrayList<>(batchSize);
            }
        }
        if (!batch.isEmpty()) {
            result.add(batch);
        }

        return result;
    }

}
