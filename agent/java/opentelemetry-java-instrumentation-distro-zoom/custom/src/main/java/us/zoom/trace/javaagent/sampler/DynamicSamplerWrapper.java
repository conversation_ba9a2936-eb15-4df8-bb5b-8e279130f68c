/*
 * Copyright 2023 Domstoladministrasjonen, Norway
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package us.zoom.trace.javaagent.sampler;

import io.opentelemetry.api.baggage.Baggage;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.context.Context;
import io.opentelemetry.sdk.trace.data.LinkData;
import io.opentelemetry.sdk.trace.samplers.Sampler;
import io.opentelemetry.sdk.trace.samplers.SamplingDecision;
import io.opentelemetry.sdk.trace.samplers.SamplingResult;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;

import java.util.List;

/**
 * This type serves as a basic wrapper for the actual {@link Sampler}
 * implementation which can be dynamically replaced. In addition it provides
 * means of filtering head-based samples.
 *
 * @since 1.0
 */
public class DynamicSamplerWrapper implements Sampler {

    private Sampler currentSampler;

    @Override
    public SamplingResult shouldSample(Context parentContext, String traceId, String name,
                                       SpanKind spanKind,
                                       Attributes attributes, List<LinkData> parentLinks) {
        SamplingResult samplingResult = getCurrentSampler().shouldSample(parentContext, traceId, name,
                spanKind, attributes, parentLinks);
        boolean sampled = samplingResult.getDecision().equals(SamplingDecision.RECORD_AND_SAMPLE);
        Baggage baggage = Baggage.fromContext(parentContext);
        if (!sampled) {
            sampled = RuleBasedSampler.getInstance().sampleForIncludeRules(attributes, name, baggage);
        }
        return sampled ? SamplingResult.recordAndSample() :
                (AgentConfiguration.globalConfiguration().captureErrorSpanEnabled() ? SamplingResult.recordOnly() : SamplingResult.drop());
    }

    @Override
    public String getDescription() {
        return "Dynamic Sampler";
    }

    public Sampler getCurrentSampler() {
        return currentSampler;
    }

    public void setCurrentSampler(Sampler currentSampler) {
        this.currentSampler = currentSampler;
    }

}
