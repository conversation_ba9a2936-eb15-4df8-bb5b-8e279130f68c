/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.processor;

import io.opentelemetry.api.baggage.Baggage;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.context.Context;
import io.opentelemetry.sdk.common.CompletableResultCode;
import io.opentelemetry.sdk.trace.ReadWriteSpan;
import io.opentelemetry.sdk.trace.ReadableSpan;
import io.opentelemetry.sdk.trace.SpanProcessor;
import io.opentelemetry.sdk.trace.data.EventData;
import io.opentelemetry.sdk.trace.export.BatchSpanProcessor;
import io.opentelemetry.sdk.trace.export.SpanExporter;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;
import us.zoom.trace.javaagent.common.AttributeKeys;
import us.zoom.trace.javaagent.common.CommonThreadPool;
import us.zoom.trace.javaagent.common.CustomSpan;
import us.zoom.trace.javaagent.exporter.FileJsonLoggingSpanExporter;
import us.zoom.trace.javaagent.sampler.RuleBasedSampler;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;


public final class BatchSpanProcessorWrapper implements SpanProcessor {

    private static final long shutdownDelay = 60 * 1000;

    private volatile BatchSpanProcessor batchSpanProcessor;

    private volatile int maxBatchSize;

    public BatchSpanProcessorWrapper(BatchSpanProcessor batchSpanProcessor, int maxBatchSize) {
        this.batchSpanProcessor = batchSpanProcessor;
        this.maxBatchSize = maxBatchSize;
    }

    @Override
    public void onStart(Context parentContext, ReadWriteSpan span) {
    }

    @Override
    public boolean isStartRequired() {
        return false;
    }

    @Override
    public void onEnd(ReadableSpan span) {
        boolean triggeredSampleForExclude = false;
        Baggage baggage = Baggage.fromContext(Context.current());
        if (!span.getSpanContext().isSampled()) {
            if (AgentConfiguration.globalConfiguration().captureErrorSpanEnabled()) {
                boolean sampled = Boolean.TRUE.equals(span.getAttribute(AttributeKeys.POST_SAMPLED_FLAG_ATTRIBUTE)) || hasError(span);
                if (!sampled) {
                    return;
                }
                boolean dropped = RuleBasedSampler.getInstance().sampleForExcludeRules(span.getAttributes(), span.getName(), baggage);
                triggeredSampleForExclude = true;
                if (dropped) {
                    return;
                }
            } else {
                return;
            }
        }
        if (!triggeredSampleForExclude && RuleBasedSampler.getInstance().sampleForExcludeRules(span.getAttributes(), span.getName(), baggage)) {
            return;
        }
        List<EventData> events = getEventData(span);
        if (events != null && !events.isEmpty() && batchSpanProcessor.getSpanExporter() instanceof FileJsonLoggingSpanExporter) {
            //prevent logs too long
            batchSpanProcessor.getSpanExporter().export(Collections.singletonList(span.toSpanData()));
            return;
        }
        batchSpanProcessor.onEnd(span);
    }

    private boolean hasError(ReadableSpan span) {
        if (span instanceof CustomSpan) {
            CustomSpan customSpan = (CustomSpan) span;
            return customSpan.getStatus() != null && customSpan.getStatus().getStatusCode() == StatusCode.ERROR;
        }
        return false;
    }

    private List<EventData> getEventData(ReadableSpan span) {
        if (span instanceof CustomSpan) {
            CustomSpan customSpan = (CustomSpan) span;
            return customSpan.getEvents();
        }
        return Collections.emptyList();
    }

    @Override
    public boolean isEndRequired() {
        return true;
    }

    @Override
    public CompletableResultCode shutdown() {
        return batchSpanProcessor.shutdown();
    }

    @Override
    public CompletableResultCode forceFlush() {
        return batchSpanProcessor.forceFlush();
    }

    @Override
    public String toString() {
        return batchSpanProcessor.toString();
    }

    public void update(BatchSpanProcessor batchSpanProcessor, int maxBatchSize) {
        BatchSpanProcessor oldProcessor = this.batchSpanProcessor;
        this.batchSpanProcessor = batchSpanProcessor;
        this.maxBatchSize = maxBatchSize;
        CommonThreadPool.getInstance().schedule(oldProcessor::shutdown, shutdownDelay, TimeUnit.MILLISECONDS);

    }

    public int getMaxBatchSize() {
        return maxBatchSize;
    }

    public BatchSpanProcessor getBatchSpanProcessor() {
        return batchSpanProcessor;
    }
}
