package us.zoom.trace.javaagent.propagator.extractor;

import us.zoom.trace.javaagent.exporter.FileJsonLoggingLogRecordExporter;
import us.zoom.trace.javaagent.logging.ILog;
import us.zoom.trace.javaagent.logging.LogManager;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Abstract base class providing immediate asynchronous lazy initialization in constructor.
 * Initialization is deferred and executed asynchronously immediately.
 */
public abstract class LazyInitializeRootSpanNameGetter implements RootSpanNameGetter {


    private static final ILog logger =
            LogManager.getLogger(LazyInitializeRootSpanNameGetter.class);

    // Initialization state tracking
    protected final AtomicBoolean hasError = new AtomicBoolean(false);
    protected final AtomicBoolean initStatus = new AtomicBoolean(false);


    /**
     * Abstract method to be implemented by subclasses for actual initialization logic.
     * This method will be called immediately in an asynchronous thread.
     *
     * @throws Throwable if initialization fails
     */
    protected abstract void doInit(ClassLoader classLoader) throws Throwable;

    protected abstract String doGetRootSpanName(Object carrier) throws Throwable;

    @Override
    public String getRootSpanName(Object carrier) {
        if (!initStatus.get()) {
            if (initStatus.compareAndSet(false, true)) {
                try {
                    doInit(carrier.getClass().getClassLoader());
                } catch (Throwable throwable) {
                    logger.error(throwable, "init rootSpanNameGetter failed");
                    hasError.set(true);
                }
            }
        }
        if (hasError.get()) {
            return null;
        }
        try {
            return doGetRootSpanName(carrier);
        } catch (Throwable e) {
            return null;
        }
    }

    public abstract String carrierClassName();
}
