package us.zoom.trace.javaagent.propagator.extractor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Singleton manager for managing RootSpanNameGetter implementations.
 * Uses thread-safe double-checked locking pattern for singleton instance creation.
 */
public class RootSpanNameGetterManager implements RootSpanNameGetter {

    // Singleton instance using volatile for thread safety
    private static volatile RootSpanNameGetterManager instance;
    
    // Thread-safe mapping from carrier class names to getter implementations
    private final Map<String, RootSpanNameGetter> carrierClassToGetterMap = new ConcurrentHashMap<>();

    // Private constructor to prevent external instantiation
    private RootSpanNameGetterManager() {

    }

    /**
     * Get singleton instance using double-checked locking pattern for thread safety.
     * This ensures only one instance is created even in multi-threaded environment.
     * 
     * @return the singleton instance of RootSpanNameGetterManager
     */
    public static RootSpanNameGetterManager getInstance() {
        if (instance == null) {
            synchronized (RootSpanNameGetterManager.class) {
                if (instance == null) {
                    instance = new RootSpanNameGetterManager();
                }
            }
        }
        return instance;
    }

    /**
     * Register a RootSpanNameGetter implementation for a specific carrier class.
     * This is a static convenience method that delegates to the singleton instance.
     *
     * @param getter the RootSpanNameGetter implementation for this carrier class
     */
    public void registerRootSpanNameGetter(RootSpanNameGetter getter) {
        if (getter == null) {
            return;
        }
        carrierClassToGetterMap.putIfAbsent(getter.carrierClassName(), getter);
    }

    /**
     * Get the root span name for the given carrier object.
     * Looks up the appropriate getter based on the carrier's class name.
     * 
     * @param carrier the carrier object to get the root span name from
     * @return the root span name, or null if no getter is registered for this carrier type
     */
    @Override
    public String getRootSpanName(Object carrier) {
        if (carrier == null) {
            return null;
        }
        
        Class<?> carrierClass = carrier.getClass();
        RootSpanNameGetter getter = carrierClassToGetterMap.get(carrierClass.getName());
        if (getter != null) {
            return getter.getRootSpanName(carrier);
        }
        return null;
    }

    @Override
    public String carrierClassName() {
        return null;
    }
}