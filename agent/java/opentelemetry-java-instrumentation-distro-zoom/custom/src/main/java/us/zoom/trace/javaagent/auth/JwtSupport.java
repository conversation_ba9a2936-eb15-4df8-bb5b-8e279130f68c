package us.zoom.trace.javaagent.auth;

import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.SignatureGenerationException;
import com.fasterxml.jackson.core.JsonProcessingException;
import us.zoom.trace.javaagent.auth.csms.CsmsConfigServiceManager;
import us.zoom.trace.javaagent.logging.ILog;
import us.zoom.trace.javaagent.logging.LogManager;
import us.zoom.trace.javaagent.util.JsonUtil;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class JwtSupport {

    private static final ILog logger = LogManager.getLogger(JwtSupport.class);

    private static final long TOKEN_EXPIRE_TIME = 30 * 60 * 1000;
    private static final long TOKEN_REFRESH_TIME = 20 * 60 * 1000;

    private static final String ISSUED_AT = "iat";
    private static final String EXPIRED_DAY = "exp";
    private static final String ISSUER_KEY = "iss";

    private static final String PATH_KEY = "path";

    private static final String FINGERPRINT_KEY = "k";

    private Algorithm algorithm;

    private String token;

    private long lastRefreshTime;

    private Map<String, Object> headers = new HashMap<>();


    private Map<String, Object> payload = new HashMap<>();


    private JwtSupport() {
        headers.put("zm_skm", "zm_o2m");
        headers.put("typ", "JWT");
        headers.put("alg", "ES256");
        payload.put("aud", "cube");
    }

    private static final class InstanceHolder {
        private static final JwtSupport INSTANCE = new JwtSupport();
    }

    public static JwtSupport getInstance() {
        return InstanceHolder.INSTANCE;
    }

    public String getToken() {
        refreshToken();
        return token;
    }

    public void refreshToken() {
        if (System.currentTimeMillis() - lastRefreshTime < TOKEN_REFRESH_TIME) {
            return;
        }
        JwtPrivateKeyResponse response = CsmsConfigServiceManager.getInstance().getJwtPrivateKey();
        if (!response.isSuccess()) {
            return;
        }
        Algorithm tmpAlgorithm = JwtAlgorithmSupport.getAlgorithm(response.getPrivateKey());
        if (tmpAlgorithm == null) {
            return;
        }
        algorithm = tmpAlgorithm;
        headers.put(FINGERPRINT_KEY, response.getFingerprint());
        payload.put(ISSUER_KEY, response.getIssuer());
        payload.put(PATH_KEY, response.getPath());
        try {
            Instant instant = new Date().toInstant();
            payload.put(ISSUED_AT, instant.getEpochSecond());
            payload.put(EXPIRED_DAY, instant.plusMillis(TOKEN_EXPIRE_TIME).getEpochSecond());
            String sign = sign();
            this.token = sign == null ? token : sign;
            lastRefreshTime = System.currentTimeMillis();
        } catch (Throwable e) {
            logger.error(e, "refresh jwt failed");
        }
    }

    private String sign() throws SignatureGenerationException, JsonProcessingException {
        if (algorithm == null) {
            return null;
        }
        String headerBase64Str = Base64.getEncoder().encodeToString(JsonUtil.toJson(headers).getBytes(StandardCharsets.UTF_8));
        String payloadBase64Str = Base64.getEncoder().encodeToString(JsonUtil.toJson(payload).getBytes(StandardCharsets.UTF_8));

        byte[] signatureBytes = algorithm.sign(headerBase64Str.getBytes(StandardCharsets.UTF_8), payloadBase64Str.getBytes(StandardCharsets.UTF_8));
        String signature = Base64.getEncoder().encodeToString(signatureBytes);

        return String.format("%s.%s.%s", headerBase64Str, payloadBase64Str, signature);
    }



}
