package us.zoom.trace.javaagent.auth;

import com.auth0.jwt.algorithms.Algorithm;
import org.bouncycastle.jce.ECNamedCurveTable;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.jce.spec.ECPublicKeySpec;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.io.pem.PemReader;
import us.zoom.trace.javaagent.bootstrap.EnvironmentConfig;
import us.zoom.trace.javaagent.logging.ILog;
import us.zoom.trace.javaagent.logging.LogManager;

import java.io.StringReader;
import java.lang.reflect.Field;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.Security;
import java.security.interfaces.ECPrivateKey;
import java.security.spec.EncodedKeySpec;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import static com.auth0.jwt.algorithms.Algorithm.ECDSA256;

/**
 * <AUTHOR>
 */
public class JwtAlgorithmSupport {

    private static final ILog logger = LogManager.getLogger(JwtAlgorithmSupport.class);
    public static final String P8_BEGIN_MARKER = "-----BEGIN PRIVATE"; //$NON-NLS-1$
    public static final String P8_END_MARKER = "-----END PRIVATE"; //$NON-NLS-1$
    public static final String SPECIAL_CHARS_REG = "[\\t\\n\\r]";
    public static final String KEY_SUBFIX = " KEY-----";
    private static final int HEX_CHAR_LEN = 12;
    private static final String SHA256 = "SHA-256";

    private static final Map<String, String[]> ASYMMETRIC_ALGORITHM;


    static {
        BouncyCastleProvider bouncyCastleProvider = new BouncyCastleProvider();
        try {
            Field name = bouncyCastleProvider.getClass().getSuperclass().getDeclaredField("name");
            name.setAccessible(true);
            name.set(bouncyCastleProvider, "OTEL_BC");
            Security.addProvider(bouncyCastleProvider);
        } catch (Throwable e) {
            logger.error(e, "replace provider name failed, will skip add provider to prevent conflict");
        }
        ASYMMETRIC_ALGORITHM = new HashMap<>(3);
        ASYMMETRIC_ALGORITHM.put("ecdsa-p256", new String[]{"EC", "secp256r1"});
        ASYMMETRIC_ALGORITHM.put("ecdsa-p384", new String[]{"EC", "secp384r1"});
        ASYMMETRIC_ALGORITHM.put("ecdsa-p521", new String[]{"EC", "secp521r1"});

    }

    public static Algorithm getAlgorithm(String base64EncodedPrivateKey) {
        try {
            base64EncodedPrivateKey = removeSpecialCharsOfPrivateKey(base64EncodedPrivateKey);
            byte[] encoded = Base64.getDecoder().decode(base64EncodedPrivateKey);
            KeyFactory keyFactory = KeyFactory.getInstance("EC");
            EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
            return ECDSA256((ECPrivateKey) keyFactory.generatePrivate(keySpec));
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            logger.error(e, "get algorithm failed");
            return null;
        }
    }

    public static String removeSpecialCharsOfPrivateKey(String key) {
        if (key.contains(P8_BEGIN_MARKER)) {
            key = key.replace(P8_BEGIN_MARKER + KEY_SUBFIX, "")
                    .replaceAll(EnvironmentConfig.LINE_SEPARATOR.getValue(), "")
                    .replaceAll(SPECIAL_CHARS_REG, "")
                    .replace(P8_END_MARKER + KEY_SUBFIX, "").trim();
        }
        return key;
    }

    public static String getFingerprint(String secret, String algorithm) {
        byte[] bytes = extractAsymmetricPublicKey(secret, algorithm);
        return fingerprint(bytes);
    }

    private static byte[] extractAsymmetricPublicKey(String pemPrivateKeyBase64Str, String algorithm) {
        String[] algorithmInfo = ASYMMETRIC_ALGORITHM.get(algorithm);
        if (algorithmInfo == null) {
            throw new IllegalArgumentException("Not support algorithm type " + algorithm + " to generate asymmetric public key");
        }
        try {
            KeyFactory keyFactory = KeyFactory.getInstance(algorithmInfo[0]);
            byte[] privateKeyBytes = new PemReader(new StringReader(pemPrivateKeyBase64Str)).readPemObject().getContent();
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            ECPrivateKey privateKey = (ECPrivateKey) keyFactory.generatePrivate(keySpec);
            ECParameterSpec ecSpec = ECNamedCurveTable.getParameterSpec(algorithmInfo[1]);
            ECPoint Q = ecSpec.getG().multiply((privateKey).getS());
            ECPublicKeySpec pubSpec = new org.bouncycastle.jce.spec.ECPublicKeySpec(Q, ecSpec);
            PublicKey publicKeyGenerated = keyFactory.generatePublic(pubSpec);
            return publicKeyGenerated.getEncoded();
        } catch (Throwable e) {
            throw new RuntimeException("Extract asymmetric public key error", e);
        }
    }

    private static String fingerprint(byte[] raw) {
        if (raw == null || raw.length == 0) {
            throw new RuntimeException("Source Data is empty, Can't generate fingerprint");
        }
        String sha256Str = sha256sum(raw);
        String subHashStr = sha256Str.substring(0, HEX_CHAR_LEN);
        byte[] binaryArr = hexTo2BinaryArr(subHashStr);
        return Base64.getEncoder().encodeToString(binaryArr);
    }

    private static String sha256sum(byte[] raw) {
        String hashText;
        try {
            MessageDigest sha256 = MessageDigest.getInstance(SHA256);
            sha256.reset();
            sha256.update(raw);
            byte[] hash = sha256.digest();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < hash.length; i++) {
                sb.append(Integer.toString((hash[i] & 0xff) + 0x100, 16).substring(1));
            }
            hashText = sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Generate SHA-256 sum error", e);
        }
        return hashText;
    }

    private static byte[] hexTo2BinaryArr(String hexStr) {
        int len = hexStr.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexStr.charAt(i), 16) << 4)
                    + Character.digit(hexStr.charAt(i + 1), 16));
        }
        return data;
    }


}
