/*
 * Copyright 2023 Domstoladministrasjonen, Norway
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package us.zoom.trace.javaagent;

import com.fasterxml.jackson.core.type.TypeReference;
import us.zoom.trace.javaagent.auth.JwtSupport;
import us.zoom.trace.javaagent.bootstrap.AgentConfigRequestParam;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;
import us.zoom.trace.javaagent.bootstrap.AgentConfigurationRefreshResult;
import us.zoom.trace.javaagent.bootstrap.EnvironmentConfig;
import us.zoom.trace.javaagent.logging.ILog;
import us.zoom.trace.javaagent.logging.LogManager;
import io.opentelemetry.sdk.trace.samplers.Sampler;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.BufferedSink;
import us.zoom.trace.javaagent.util.JsonUtil;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Objects;

/**
 * This type deals with the OpenTelemetry Configuration Service.
 *
 * @since 1.0
 */
public class AgentConfigurationServiceClient {

    private static final ILog logger = LogManager.getLogger(AgentConfigurationServiceClient.class);

    private static final String SUCCESS = "success";

    private static final String CONFIG_URI = "/cfg/trace/agent/agentConfiguration";

    private static final String HOST_NAME;

    private static final String AUTHENTICATION = "Authentication";

    static {
        String hostname;
        try {
            hostname = System.getenv().getOrDefault("HOSTNAME", InetAddress.getLocalHost().getHostName());
        } catch (UnknownHostException e) {
            hostname = "unknown";
        }
        HOST_NAME = hostname;
    }

    private final OkHttpClient httpClient;
    private final String configServerUrl;

    public AgentConfigurationServiceClient() {
        this.httpClient = OkHttpClientFactory.getOkHttpClient();
        this.configServerUrl = EnvironmentConfig.OTEL_CONFIG_SERVICE_URL.getValue();
    }


    /**
     * Calls the configuration service to obtain a sampler configuration for this
     * agent. If the agent is not registered or obtaining a configuration fails, the
     * initial configuration will be returned.
     * <p>
     * If the configuration service contains the agent configuration, collected
     * {@link Sampler} metrics for this will be posted.
     * </p>
     *
     * @param localConfig Initial agent configuration
     * @return the agent configuration
     */
    public AgentConfigurationRefreshResult synchronize(AgentConfiguration localConfig) {

        AgentConfigRequestParam requestParam = new AgentConfigRequestParam();
        requestParam.setCluster(EnvironmentConfig.CLUSTER.getValue());
        requestParam.setRegion(EnvironmentConfig.REGION.getValue());
        requestParam.setServiceName(EnvironmentConfig.CMDB_APP_NAME.getValue());
        requestParam.setHostname(HOST_NAME);
        requestParam.setTimestamp(localConfig.getTimestamp());
        AgentConfiguration newConfig = null;
        boolean success = true;
        try {
            logger.info("start get configuration from url: {}", configServerUrl);
            Request.Builder requestBuilder = new Request.Builder().url(configServerUrl + CONFIG_URI);
            String token = JwtSupport.getInstance().getToken();
            if (token == null) {
                logger.warn("can't generate jwt from environment");
                return new AgentConfigurationRefreshResult(false);
            }
            requestBuilder.addHeader(AUTHENTICATION, token);
            RequestBody requestBody = new JsonRequestBody(requestParam);
            requestBuilder.post(requestBody);
            try (Response response = httpClient.newCall(requestBuilder.build()).execute()) {
                ResponseBody body = response.body();
                if (response.isSuccessful() && body != null) {
                    ResponseObject<AgentConfiguration> responseObject = JsonUtil.fromJson(body.bytes(),
                            new TypeReference<ResponseObject<AgentConfiguration>>() {});
                    if (Objects.equals(responseObject.getStatus(), SUCCESS)) {
                        newConfig = responseObject.getData();
                        if (newConfig == null) {
                            // not modified
                            logger.info(responseObject.getMessage());
                        } else {
                            logger.info("get configuration from remote server successful, config lastModifiedTIme: {}",
                                    newConfig.getTimestamp());
                        }
                    } else {
                        success = false;
                        logger.error(null, "get configuration inner error, message: {}, code: {}, status: {}",
                                responseObject.getMessage(), responseObject.getOperCode(), responseObject.getStatus());
                    }
                } else {
                    success = false;
                    logger.error(null, "get configuration unknown error, message:{}, code: {}",
                            response.message(),
                            response.code());
                }
            }
        } catch (Throwable e) {
            success = false;
            logger.error("get configuration error", e);
        }
        return new AgentConfigurationRefreshResult(newConfig, success);
    }

    private class JsonRequestBody extends RequestBody {

        private final Object data;
        private final MediaType mediaType;

        private JsonRequestBody(Object data) {
            this.data = data;
            this.mediaType = MediaType.parse("application/json");
        }

        @Override
        public long contentLength() {
            // will not record content-length
            return -1L;
        }

        @Override
        public MediaType contentType() {
            return mediaType;
        }

        @Override
        public void writeTo(BufferedSink bufferedSink) throws IOException {
            JsonUtil.getObjectMapper().writeValue(bufferedSink.outputStream(), data);
        }
    }

    public static class ResponseObject<T> {
        private String status;
        private String operId;
        private String message;
        private String operCode;
        private T data;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getOperId() {
            return operId;
        }

        public void setOperId(String operId) {
            this.operId = operId;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getOperCode() {
            return operCode;
        }

        public void setOperCode(String operCode) {
            this.operCode = operCode;
        }

        public T getData() {
            return data;
        }

        public void setData(T data) {
            this.data = data;
        }

    }

}
