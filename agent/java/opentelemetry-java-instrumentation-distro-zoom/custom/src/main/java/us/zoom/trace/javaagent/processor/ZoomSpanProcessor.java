package us.zoom.trace.javaagent.processor;

import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.context.Context;
import io.opentelemetry.sdk.trace.ReadWriteSpan;
import io.opentelemetry.sdk.trace.ReadableSpan;
import io.opentelemetry.sdk.trace.SpanProcessor;
import us.zoom.trace.javaagent.common.ZoomTrackingContext;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static us.zoom.trace.javaagent.common.AttributeKeys.*;

public class ZoomSpanProcessor implements SpanProcessor {

    private static final Set<SpanKind> LOCAL_ROOT_SPAN_KINDS = new HashSet<SpanKind>(){{
        add(SpanKind.SERVER);
        add(SpanKind.CONSUMER);
    }};

    public ZoomSpanProcessor() {
    }

    @Override
    public void onStart(Context parentContext, ReadWriteSpan span) {
        ZoomTrackingContext context = parentContext.get(ZoomTrackingContext.CONTEXT_KEY);
        if (context == null) {
            return;
        }
        String trackingId = context.getTrackingId();
        if (trackingId != null) {
            span.setAttribute(SHORT_KEY_TRACKING_ID, trackingId);
        }
        String upstreamServiceName = context.getUpstreamServiceName();
        if (LOCAL_ROOT_SPAN_KINDS.contains(span.getKind())) {
            if (upstreamServiceName != null) {
                span.setAttribute(ZM_TRACE_UPSTREAM.getKey(), upstreamServiceName);
            }
        }
        Map<String, String> extraTags = context.getExtraTags();
        if (extraTags != null) {
            extraTags.forEach((k, v) -> {
                if (v != null) {
                    span.setAttribute(k, v);
                }
            });
        }
        String rootName = context.getRootName();
        if (rootName != null && !LOCAL_ROOT_SPAN_KINDS.contains(span.getKind())) {
            span.setAttribute(ZM_TRACE_SPAN_ROOT_NAME.getKey(), rootName);
        }
    }

    @Override
    public boolean isStartRequired() {
        return true;
    }

    @Override
    public void onEnd(ReadableSpan span) {
    }

    @Override
    public boolean isEndRequired() {
        return false;
    }

}
