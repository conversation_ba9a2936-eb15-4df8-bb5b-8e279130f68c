package us.zoom.trace.javaagent.util;

import io.opentelemetry.api.common.AttributeKey;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class RuleUtil {

    /**
     * Returns a compiled set of rules. This representation is already converted
     * into {@link AttributeKey} and {@link Pattern}, removing the need for doing
     * this conversion during filtering.
     *
     * @return a compiled set of filtering rules
     */
    public static Map<String, List<Map<AttributeKey<String>, Pattern>>> convertRules(List<AgentConfiguration.Rules> rules) {
        Map<String, List<Map<AttributeKey<String>, Pattern>>> ruleSets = new HashMap<>();
        if (rules == null) {
            return ruleSets;
        }

        List<Map<AttributeKey<String>, Pattern>> excludeSet = new ArrayList<>();
        List<Map<AttributeKey<String>, Pattern>> includeSet = new ArrayList<>();

        for (AgentConfiguration.Rules rule : rules) {
            compileRules(excludeSet, rule.getExclude());
            compileRules(includeSet, rule.getInclude());
        }

        ruleSets.put("exclude", excludeSet);
        ruleSets.put("include", includeSet);

        return ruleSets;
    }

    private static void compileRules(List<Map<AttributeKey<String>, Pattern>> set,
                              List<Map<String, String>> spec) {
        if (spec != null) {
            for (Map<String, String> map : spec) {
                Map<AttributeKey<String>, Pattern> ruleGroup = new HashMap<>();
                for (String string : map.keySet()) {
                    if (map.get(string) != null) {
                        ruleGroup.put(AttributeKey.stringKey(string), Pattern.compile(map.get(string)));
                    }
                }
                set.add(ruleGroup);
            }
        }
    }
}
