package us.zoom.trace.javaagent.propagator.extractor;

import us.zoom.trace.javaagent.bootstrap.CommonConstant;
import us.zoom.trace.javaagent.common.MethodUtil;

import java.lang.invoke.MethodHandle;

public class TomcatRootSpanNameGetter extends LazyInitializeRootSpanNameGetter {

    private static TomcatRootSpanNameGetter instance;


    public static TomcatRootSpanNameGetter getInstance() {
        if (instance == null) {
            synchronized (TomcatRootSpanNameGetter.class) {
                if (instance == null) {
                    instance = new TomcatRootSpanNameGetter();
                }
            }
        }
        return instance;
    }

    private TomcatRootSpanNameGetter() {}

    private MethodHandle uri;
    private MethodHandle method;

    @Override
    protected void doInit(ClassLoader classLoader) throws Throwable {
        Class<?> mainClass = classLoader.loadClass(carrierClassName());
        Class<?> returnClazz = classLoader.loadClass("org.apache.tomcat.util.buf.MessageBytes");
        uri = MethodUtil.findMethodOrNull(mainClass, "requestURI", returnClazz);
        method = MethodUtil.findMethodOrNull(mainClass, "method", returnClazz);
    }


    @Override
    public String doGetRootSpanName(Object carrier) throws Throwable {
        if (uri == null || method == null) {
            return null;
        }
        return method.invoke(carrier).toString() + CommonConstant.SPACE + uri.invoke(carrier).toString();
    }

    @Override
    public String carrierClassName() {
        return "org.apache.coyote.Request";
    }
}
