package us.zoom.trace.javaagent.generator;

import io.opentelemetry.sdk.trace.IdGenerator;

/**
 * <AUTHOR>
 */
public class ZoomIdGenerator implements IdGenerator {

    private static final IdGenerator random = IdGenerator.random();

    @Override
    public String generateSpanId() {
        return random.generateSpanId();
    }

    @Override
    public String generateTraceId() {
        return "";
    }
}
