{"otel.java.disabled.resource.providers": "io.opentelemetry.instrumentation.resources.ProcessResourceProvider,io.opentelemetry.instrumentation.resources.ProcessRuntimeResourceProvider,io.opentelemetry.instrumentation.resources.OsResourceProvider,io.opentelemetry.instrumentation.resources.HostIdResourceProvider,io.opentelemetry.instrumentation.resources.HostResourceProvider,io.opentelemetry.instrumentation.resources.ContainerResourceProvider", "otel.instrumentation.kafka.enabled": "false", "otel.instrumentation.logback-appender.enabled": "false", "otel.instrumentation.log4j-appender.enabled": "false", "otel.traces.capture.error.span": "true", "otel.logs.capture.error.log": "true", "otel.traces.exporter": "none", "otel.metrics.exporter": "none", "otel.logs.exporter": "logging-file", "otel.blrp.max.export.batch.size": 4, "otel.experimental.resource.disabled.keys": "service.instance.id,telemetry.distro.name,telemetry.sdk.name,telemetry.sdk.version", "otel.instrumentation.aws-sdk.experimental-span-attributes": "true", "otel.instrumentation.experimental.span-suppression-strategy": "span-kind", "otel.instrumentation.http.capture-headers.server.response": "content-length", "otel.instrumentation.logback-appender.experimental-log-attributes": "true", "otel.instrumentation.logback-appender.experimental.capture-code-attributes": "true", "otel.instrumentation.logback-appender.experimental.capture-mdc-attributes": "x-zm-trackingid"}