plugins {
  id "java"
}
apply from: "$rootDir/gradle/repositories.gradle"
dependencies {
  compileOnly(project(":bootstrap"))
  implementation(project(":common"))
  compileOnly("io.opentelemetry:opentelemetry-sdk")
  compileOnly("io.opentelemetry:opentelemetry-exporter-otlp-common")
  compileOnly("io.opentelemetry:opentelemetry-exporter-otlp")
  compileOnly("io.opentelemetry.semconv:opentelemetry-semconv")
  compileOnly("io.opentelemetry:opentelemetry-sdk-extension-autoconfigure-spi")
  compileOnly("io.opentelemetry.javaagent:opentelemetry-javaagent-extension-api")
  compileOnly("io.opentelemetry.javaagent:opentelemetry-javaagent-tooling")
  compileOnly("io.opentelemetry.javaagent:opentelemetry-javaagent-bootstrap")
  implementation("com.fasterxml.jackson.core:jackson-databind:2.17.2")
  implementation("com.lmax:disruptor:3.3.6")
  implementation("com.squareup.okhttp3:okhttp:4.12.0")
  implementation("com.auth0:java-jwt:4.4.0")
  implementation("org.bouncycastle:bcutil-jdk18on:1.77")
//  implementation("io.pyroscope:agent:2.0.0")
}
