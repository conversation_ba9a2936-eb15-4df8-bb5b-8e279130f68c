pluginManagement {
  repositories {
    maven {
      url "${zoom_rt_repo_url}"
      credentials {
        username = "${zoom_rt_user}"
        password = "${zoom_rt_password}"
      }
    }
  }
}

rootProject.name = 'opentelemetry-java-instrumentation-distro-zoom'

include "agent"
include "bootstrap"
include "custom"
include "common"
include "instrumentation"
include "instrumentation:asyncmq-2"
include "instrumentation:executors"
include "instrumentation:logback-extension-1.0"
include "instrumentation:csms-sdk-0.6.0"
include "instrumentation:mybatis-extension"
include "instrumentation:zoom-spring-web:spring-web-3.1"
include "instrumentation:zoom-spring-web:spring-web-6.0"
include "instrumentation:aws-sdk-enhance:aws-sdk-1.11-enhance"
include "smoke-tests"
include "testing:agent-for-testing"
