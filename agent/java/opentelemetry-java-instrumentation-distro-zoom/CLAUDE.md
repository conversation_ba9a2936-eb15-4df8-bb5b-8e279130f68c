# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is Zoom's custom distribution of the OpenTelemetry Java instrumentation agent. It extends the upstream OpenTelemetry Java instrumentation agent with Zoom-specific functionality, custom instrumentations, and configuration. The project packages these extensions into a standalone Java agent JAR file.

## Architecture

The project follows a modular structure with the following key components:

- **`agent/`** - Main packaging module that assembles the final Java agent JAR
- **`bootstrap/`** - Bootstrap components loaded early in JVM lifecycle  
- **`custom/`** - Core Zoom-specific extensions (configuration, processors, exporters, auth)
- **`common/`** - Shared utilities and common classes used across modules
- **`instrumentation/`** - Custom instrumentations for specific libraries/frameworks:
  - `asyncmq-2/` - AsyncMQ messaging instrumentation
  - `executors/` - Enhanced executor service instrumentation  
  - `logback-extension-1.0/` - Logback logging integration
  - `csms-sdk-0.6.0/` - CSMS SDK instrumentation
  - `zoom-spring-web/` - Spring Web framework instrumentation (versions 3.1 & 6.0)
  - `aws-sdk-enhance/` - Enhanced AWS SDK instrumentation
- **`smoke-tests/`** - Integration tests using TestContainers
- **`testing/`** - Test utilities and agent-for-testing build

## Build System

The project uses **Gradle** as the build system with the following key tasks:

### Common Development Commands

```bash
# Build the entire project (including the final agent JAR)
gradle build

# Build only the agent JAR 
gradle :agent:shadowJar

# Run all tests
gradle test

# Run smoke tests specifically
gradle :smoke-tests:test

# Clean build artifacts
gradle clean

# Assemble without running tests
gradle assemble

# Publish to artifactory (production command)
gradle :agent:artifactoryPublish -x test --no-scan
```

### Build Configuration Options

```bash
# Specify Java compilation version (default is 17)
gradle build -PjavaVersion=11
gradle :agent:artifactoryPublish -PjavaVersion=11 -x test --no-scan

# Disable specific instrumentation modules
gradle build -PdisableInstrumentations=asyncmq-2,executors
gradle :agent:shadowJar -PdisableInstrumentations=spring-web-3.1,aws-sdk-enhance

# Combine options
gradle :agent:artifactoryPublish -PjavaVersion=11 -PdisableInstrumentations=asyncmq-2,logback-extension -x test --no-scan
```

**Available Instrumentation Modules to Disable:**
- `asyncmq-2` - AsyncMQ messaging instrumentation
- `executors` - Enhanced executor service instrumentation  
- `logback-extension` - Logback logging integration
- `csms-sdk` - CSMS SDK instrumentation
- `spring-web-3.1` - Spring Web 3.1 instrumentation
- `spring-web-6.0` - Spring Web 6.0 instrumentation
- `aws-sdk-enhance` - Enhanced AWS SDK instrumentation

### Key Build Processes

The agent JAR is built in 3 stages:
1. **Relocate** - All custom javaagent libraries are relocated to avoid conflicts
2. **Isolate** - Relocated libraries are moved to `inst/` directory and classes renamed to `.classdata`
3. **Merge** - Everything is merged with the upstream OpenTelemetry agent JAR

## Configuration

- **Default configuration**: `custom/src/main/resources/default_config.json`
- **Gradle properties**: `gradle.properties` (contains version info and JVM args)
- **Repository settings**: `settings.gradle` (includes Zoom artifact repository configuration)

## Key Extension Points

The project demonstrates various OpenTelemetry extension patterns:

- **Custom Span Processors** - `ZoomSpanProcessor`, `BaggageSpanProcessor`
- **Custom Exporters** - `FileJsonLoggingSpanExporter`, `FileJsonLoggingLogRecordExporter`  
- **Custom Samplers** - `RuleBasedSampler`, `DynamicSamplerWrapper`
- **Custom Propagators** - `ZoomTracePropagator`
- **Configuration Services** - Integration with Zoom's CSMS configuration system
- **Authentication** - JWT-based authentication for configuration services

## Testing

- **Unit tests** use JUnit 5 with Mockito
- **Integration tests** in `smoke-tests/` use TestContainers 
- **Instrumentation tests** use the OpenTelemetry testing framework with a test agent

## Dependencies

- **OpenTelemetry SDK**: 1.47.0
- **OpenTelemetry Java Agent**: 2.13.1
- **Java**: Requires Java 17+ (configured in build files)
- **Shadow Plugin**: Used for creating fat JARs with dependency relocation

## Zoom-Specific Features

- **Configuration Management** - Integration with Zoom's CSMS (Configuration Service Management System)
- **Enhanced Logging** - Custom file-based JSON logging exporters
- **Authentication** - JWT-based authentication for secure configuration retrieval  
- **Spring Integration** - Custom filters and instrumentations for Spring Web applications
- **AWS SDK Enhancement** - Extended AWS SDK instrumentation with additional attributes
- **AsyncMQ Support** - Instrumentation for Zoom's internal messaging system

## Development Notes

- The project uses dependency relocation to avoid classpath conflicts
- Custom instrumentations follow OpenTelemetry's instrumentation patterns
- All custom code is under the `us.zoom.trace.javaagent` package
- The final agent JAR includes both upstream OpenTelemetry instrumentation and Zoom's custom extensions