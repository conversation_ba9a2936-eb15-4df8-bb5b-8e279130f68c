buildscript {
  apply from: 'gradle/repositories.gradle'
  dependencies {
    classpath "org.jfrog.buildinfo:build-info-extractor-gradle:4.33.2"
    classpath "com.gradleup.shadow:shadow-gradle-plugin:8.3.1"
    classpath "io.opentelemetry.instrumentation:gradle-plugins:2.8.0-alpha"
  }
}
plugins {
  id 'java-library'
  id 'com.jfrog.artifactory' version "4.28.2"
}
subprojects {
  apply from: "$rootDir/gradle/repositories.gradle"
  version = rootProject.version

  apply plugin: "java"

  ext {
    versions = [
      // this line is managed by .github/scripts/update-sdk-version.sh
      opentelemetrySdk           : "1.47.0",

      // these lines are managed by .github/scripts/update-version.sh
      opentelemetryJavaagent     : "2.13.1",
      opentelemetryJavaagentAlpha: "2.13.1-alpha",

      autoservice                : "1.1.1",
      junit                      : "5.11.4"
    ]

    deps = [
      autoservice: [
        "com.google.auto.service:auto-service:${versions.autoservice}",
        "com.google.auto.service:auto-service-annotations:${versions.autoservice}",
      ]
    ]
  }

  dependencies {
    implementation(platform("io.opentelemetry:opentelemetry-bom:${versions.opentelemetrySdk}"))

    // these serve as a test of the instrumentation boms
    implementation(platform("io.opentelemetry.instrumentation:opentelemetry-instrumentation-bom:${versions.opentelemetryJavaagent}"))
    implementation(platform("io.opentelemetry.instrumentation:opentelemetry-instrumentation-bom-alpha:${versions.opentelemetryJavaagentAlpha}"))

    testImplementation("org.mockito:mockito-core:5.15.2")
    testImplementation(enforcedPlatform("org.junit:junit-bom:${versions.junit}"))
    testImplementation("org.junit.jupiter:junit-jupiter-api:${versions.junit}")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine:${versions.junit}")
  }

  tasks {
    test {
      useJUnitPlatform()
    }

    compileJava {
      // Allow command line override of Java version: -PjavaVersion=11
      def javaVersion = project.hasProperty('javaVersion') ? project.javaVersion as Integer : 17
      options.release.set(javaVersion)
    }
  }
}
