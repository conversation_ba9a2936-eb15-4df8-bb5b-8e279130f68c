package us.zoom.trace.javaagent.common;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

public class CommonThreadPool {
    
    private static final int DEFAULT_CORE_POOL_SIZE = 1;
    private static volatile CommonThreadPool instance;
    private static final Object lock = new Object();
    
    private final ScheduledExecutorService scheduledExecutor;
    private final AtomicBoolean isShutdown = new AtomicBoolean(false);
    
    private CommonThreadPool(int corePoolSize) {
        this.scheduledExecutor = Executors.newScheduledThreadPool(
            corePoolSize, 
            new ZoomThreadFactory("cube-trace-common-pool")
        );
        
        Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
    }
    
    public static CommonThreadPool getInstance() {
        return getInstance(DEFAULT_CORE_POOL_SIZE);
    }
    
    public static CommonThreadPool getInstance(int corePoolSize) {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new CommonThreadPool(corePoolSize);
                }
            }
        }
        return instance;
    }
    
    public void execute(Runnable task) {
        if (isShutdown.get()) {
            throw new IllegalStateException("CommonThreadPool has been shutdown");
        }
        scheduledExecutor.execute(task);
    }
    
    public ScheduledFuture<?> schedule(Runnable command, long delay, TimeUnit unit) {
        if (isShutdown.get()) {
            throw new IllegalStateException("CommonThreadPool has been shutdown");
        }
        return scheduledExecutor.schedule(command, delay, unit);
    }
    
    public ScheduledFuture<?> scheduleAtFixedRate(Runnable command, long initialDelay, long period, TimeUnit unit) {
        if (isShutdown.get()) {
            throw new IllegalStateException("CommonThreadPool has been shutdown");
        }
        return scheduledExecutor.scheduleAtFixedRate(command, initialDelay, period, unit);
    }
    
    public ScheduledFuture<?> scheduleWithFixedDelay(Runnable command, long initialDelay, long delay, TimeUnit unit) {
        if (isShutdown.get()) {
            throw new IllegalStateException("CommonThreadPool has been shutdown");
        }
        return scheduledExecutor.scheduleWithFixedDelay(command, initialDelay, delay, unit);
    }
    
    public boolean isShutdown() {
        return isShutdown.get();
    }
    
    public boolean isTerminated() {
        return scheduledExecutor.isTerminated();
    }
    
    public void shutdown() {
        if (isShutdown.compareAndSet(false, true)) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    public void shutdownNow() {
        if (isShutdown.compareAndSet(false, true)) {
            scheduledExecutor.shutdownNow();
        }
    }
    
    private static class ZoomThreadFactory implements ThreadFactory {
        private final String namePrefix;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        
        ZoomThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }
        
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + "-" + threadNumber.getAndIncrement());
            thread.setDaemon(true);
            thread.setPriority(Thread.NORM_PRIORITY);
            return thread;
        }
    }
}