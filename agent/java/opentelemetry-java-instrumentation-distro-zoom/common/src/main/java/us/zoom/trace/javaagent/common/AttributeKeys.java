/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.common;

import io.opentelemetry.api.common.AttributeKey;

import java.util.List;

/**
 *
 */
public interface AttributeKeys {
    AttributeKey<String> MESSAGING_DESTINATION_PARTITION_ID =
            AttributeKey.stringKey("messaging.destination.partition.id");
    AttributeKey<String> MESSAGING_ASYNCMQ_CONSUMER_GROUP =
            AttributeKey.stringKey("messaging.asyncmq.consumer.group");
    AttributeKey<String> MESSAGING_ASYNCMQ_MESSAGE_KEY =
            AttributeKey.stringKey("messaging.asyncmq.message.key");
    AttributeKey<Long> MESSAGING_ASYNCMQ_MESSAGE_OFFSET =
            AttributeKey.longKey("messaging.asyncmq.message.offset");
    AttributeKey<Boolean> MESSAGING_ASYNCMQ_MESSAGE_TOMBSTONE =
            AttributeKey.booleanKey("messaging.asyncmq.message.tombstone");

    AttributeKey<String> MESSAGING_ASYNCMQ_ORIGINAL_TASK_ID =
            AttributeKey.stringKey("messaging.asyncmq.original.task.id");

    AttributeKey<String> MESSAGING_ASYNCMQ_TASK_ID =
            AttributeKey.stringKey("messaging.asyncmq.task.id");

    AttributeKey<Long> MESSAGING_ASYNCMQ_MESSAGE_CODE =
            AttributeKey.longKey("messaging.asyncmq.message.code");
    AttributeKey<Long> MESSAGING_MESSAGE_BODY_SIZE =
            AttributeKey.longKey("messaging.message.body.size");
    AttributeKey<String> MESSAGING_CLIENT_ID =
            AttributeKey.stringKey("messaging.client_id");

    AttributeKey<String> MESSAGING_PROTOCOL_VERSION =
            AttributeKey.stringKey("messaging.protocol.version");


    AttributeKey<String> ZM_TRACKING_ID =
            AttributeKey.stringKey("x-zm-trackingid");

    AttributeKey<String> PROFILING_FLAG =
            AttributeKey.stringKey("x-profiling-enabled");

    AttributeKey<String> SHORT_KEY_TRACKING_ID =
            AttributeKey.stringKey("trackingId");

    AttributeKey<List<String>> DYNAMODB_TABLE_NAMES =
            AttributeKey.stringArrayKey("aws.dynamodb.table_names");

    AttributeKey<String> SPAN_KIND_ATTRIBUTE =
            AttributeKey.stringKey("span.kind");

    AttributeKey<String> SPAN_NAME_ATTRIBUTE =
            AttributeKey.stringKey("span.name");

    AttributeKey<String> COMPONENT_ATTRIBUTE =
            AttributeKey.stringKey("component.name");

    AttributeKey<Boolean> POST_SAMPLED_FLAG_ATTRIBUTE =
            AttributeKey.booleanKey("post_sampled.flag");

    AttributeKey<String> ZM_TRACE_UPSTREAM =
            AttributeKey.stringKey("zm-trace-upstream");

    AttributeKey<String> ZM_TRACE_SPAN_ROOT_NAME =
            AttributeKey.stringKey("root.operation");

    AttributeKey<Long> DB_RETURNED_ROWS_SIZE =
            AttributeKey.longKey("db.response.returned_rows");

    AttributeKey<Long> DB_AFFECT_ROWS_SIZE =
            AttributeKey.longKey("db.response.affect_rows");
    AttributeKey<String> CODE_FILEPATH = AttributeKey.stringKey("code.filepath");
    AttributeKey<String> CODE_FUNCTION = AttributeKey.stringKey("code.function");
    AttributeKey<Long> CODE_LINENO = AttributeKey.longKey("code.lineno");
    AttributeKey<String> CODE_NAMESPACE =
            AttributeKey.stringKey("code.namespace");
    // copied from
    AttributeKey<Long> THREAD_ID = AttributeKey.longKey("thread.id");
    AttributeKey<String> THREAD_NAME = AttributeKey.stringKey("thread.name");
    AttributeKey<String> ERROR_MESSAGE = AttributeKey.stringKey("error.message");

//    AttributeKey<String> DB_SQL_TABLE = AttributeKey.stringKey("db.sql.table");
    AttributeKey<String> DB_STATEMENT = AttributeKey.stringKey("db.statement");
    AttributeKey<String> DB_OPERATION = AttributeKey.stringKey("db.operation");
    AttributeKey<Long> DB_OPERATION_BATCH_SIZE = AttributeKey.longKey("db.operation.batch.size");
    AttributeKey<String> DB_SYSTEM_NAME =
            AttributeKey.stringKey("db.system.name");
    AttributeKey<String> DB_SQL_TABLE=
            AttributeKey.stringKey("db.sql.table");
    AttributeKey<String> DB_NAME = AttributeKey.stringKey("db.name");

}
