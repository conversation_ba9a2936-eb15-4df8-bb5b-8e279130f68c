package us.zoom.trace.javaagent.common;

import io.opentelemetry.sdk.trace.data.EventData;
import io.opentelemetry.sdk.trace.data.StatusData;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CustomSpan {


    default StatusData getStatus() {
        return StatusData.unset();
    }

    default List<EventData> getEvents() {
        return Collections.emptyList();
    }
}
