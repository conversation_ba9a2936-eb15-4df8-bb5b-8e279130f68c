package us.zoom.trace.javaagent.common;

import io.opentelemetry.context.Context;
import io.opentelemetry.context.ContextKey;
import io.opentelemetry.context.ImplicitContextKeyed;

import java.util.Map;


/**
 * <AUTHOR>
 */
public class ZoomTrackingContext implements ImplicitContextKeyed {

    public static final ContextKey<ZoomTrackingContext> CONTEXT_KEY = ContextKey.named(
            "opentelemetry-x-zm-trackingid");

    private String trackingId;

    private String originalTrackingId;

    private String upstreamServiceName;

    private String rootName;

    private Map<String, String> extraTags;

    public ZoomTrackingContext() {
    }

    public ZoomTrackingContext(String originalTrackingId) {
        this.originalTrackingId = originalTrackingId;
    }

    public String getOriginalTrackingId() {
        return originalTrackingId;
    }

    public String getTrackingId() {
        return trackingId;
    }

    public void setTrackingId(String trackingId) {
        this.trackingId = trackingId;
    }

    public String getUpstreamServiceName() {
        return upstreamServiceName;
    }

    public void setUpstreamServiceName(String upstreamServiceName) {
        this.upstreamServiceName = upstreamServiceName;
    }

    public String getRootName() {
        return rootName;
    }

    public void setRootName(String rootName) {
        this.rootName = rootName;
    }

    public Map<String, String> getExtraTags() {
        return extraTags;
    }

    public void setExtraTags(Map<String, String> extraTags) {
        this.extraTags = extraTags;
    }

    @Override
    public Context storeInContext(Context context) {
        return context.with(CONTEXT_KEY, this);
    }

    public static ZoomTrackingContext fromField(String field) {
        if (field == null || field.isEmpty()) {
            return new ZoomTrackingContext();
        }
        ZoomTrackingContext zoomTrackingContext = new ZoomTrackingContext(field);
        String[] split = field.split(";");
        if (split.length == 1 && !split[0].isEmpty()) {
            zoomTrackingContext.setTrackingId(split[0]);
            return zoomTrackingContext;
        }
        for (String pair : split) {
            if (pair.isEmpty()) {
                continue;
            }
            String[] kv = pair.trim().split("=");
            if (kv.length != 2) {
                continue;
            }
            String key = kv[0].trim();
            String value = kv[1].trim();
            if (value.isEmpty()) {
                continue;
            }
            if (key.equals("rid")) {
                zoomTrackingContext.setTrackingId(value);
            }
        }
        return zoomTrackingContext;
    }

    @Override
    public String toString() {
        return "ZoomTrackingContext{" +
                "trackingId='" + trackingId + '\'' +
                ", originalTrackingId='" + originalTrackingId + '\'' +
                ", upstreamServiceName='" + upstreamServiceName + '\'' +
                ", rootName='" + rootName + '\'' +
                ", extraTags=" + extraTags +
                '}';
    }
}
