package us.zoom.trace.javaagent.common;

import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.invoke.MethodType;

public class MethodUtil {

    public static Object invokeOrNull(MethodHandle methodHandle, Object obj) {
        if (methodHandle == null) {
            return null;
        }
        try {
            return methodHandle.invoke(obj);
        } catch (Throwable t) {
            return null;
        }
    }

    public static MethodHandle findMethodOrNull(Class<?> clz, String methodName, Class<?> returnType) {
        try {
            return MethodHandles.publicLookup()
                    .findVirtual(clz, methodName, MethodType.methodType(returnType));
        } catch (Throwable t) {
            return null;
        }
    }

    public static MethodHandle findGetterWithoutPublicOrNull(Class<?> clz, String fieldName) {
        try {
            java.lang.reflect.Field field = clz.getDeclaredField(fieldName);
            field.setAccessible(true);
            return MethodHandles.lookup().unreflectGetter(field);
        } catch (Throwable t) {
            return null;
        }
    }

    public static MethodHandle findNonPublicMethodOrNull(Class<?> clz, String methodName, Class<?>... paramTypes) {
        try {
            java.lang.reflect.Method method = clz.getDeclaredMethod(methodName, paramTypes);
            method.setAccessible(true);
            return MethodHandles.lookup().unreflect(method);
        } catch (Throwable t) {
            return null;
        }
    }
}
