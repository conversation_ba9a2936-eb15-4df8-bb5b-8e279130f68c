/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import static io.opentelemetry.api.common.AttributeKey.longKey;

import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.AttributesBuilder;
import io.opentelemetry.context.Context;
import io.opentelemetry.instrumentation.api.instrumenter.AttributesExtractor;

import javax.annotation.Nullable;

import us.zoom.mq.common.entity.TaskEntity;

final class AsyncMQConsumerExperimentalAttributesExtractor
        implements AttributesExtractor<ConsumeRequest, Void> {

    private static final AttributeKey<Long> ASYNCMQ_RECORD_QUEUE_TIME_MS =
            longKey("asyncmq.record.queue_time_ms");

    @Override
    public void onStart(
            AttributesBuilder attributes, Context parentContext, ConsumeRequest request) {

        Long produceTime = request.getProduceTime();
        attributes.put(
                ASYNCMQ_RECORD_QUEUE_TIME_MS, Math.max(0L, System.currentTimeMillis() - produceTime));
    }

    @Override
    public void onEnd(
            AttributesBuilder attributes,
            Context context,
            ConsumeRequest request,
            @Nullable Void unused,
            @Nullable Throwable error) {
    }
}
