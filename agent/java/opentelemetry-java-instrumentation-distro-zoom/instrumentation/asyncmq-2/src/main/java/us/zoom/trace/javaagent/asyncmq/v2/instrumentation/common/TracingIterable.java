/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.context.Context;
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter;
import org.checkerframework.checker.units.qual.K;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.Iterator;
import java.util.function.BooleanSupplier;

/**
 * This class is internal and is hence not for public use. Its APIs are unstable and can change at
 * any time.
 */
public class TracingIterable<T> implements Iterable<TaskEntity<T>> {
    private final Iterable<TaskEntity<T>> delegate;
    private final Instrumenter<ConsumeRequest, Void> instrumenter;
    private boolean firstIterator = true;

    protected TracingIterable(
            Iterable<TaskEntity<T>> delegate,
            Instrumenter<ConsumeRequest, Void> instrumenter) {
        this.delegate = delegate;
        this.instrumenter = instrumenter;
    }

    @Override
    public Iterator<TaskEntity<T>> iterator() {
        Iterator<TaskEntity<T>> it;
        // We should only return one iterator with tracing.
        // However, this is not thread-safe, but usually the first (hopefully only) traversal of
        // TaskEntitys is performed in the same thread that called poll()
        if (firstIterator) {
            it = TracingIterator.wrap(delegate.iterator(), instrumenter);
            firstIterator = false;
        } else {
            it = delegate.iterator();
        }

        return it;
    }
}
