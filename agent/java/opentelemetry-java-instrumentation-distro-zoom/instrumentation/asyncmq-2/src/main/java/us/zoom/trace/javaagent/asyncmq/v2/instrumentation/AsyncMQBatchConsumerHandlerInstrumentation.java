/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation;

import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;

import static io.opentelemetry.javaagent.extension.matcher.AgentElementMatchers.hasSuperType;
import static net.bytebuddy.matcher.ElementMatchers.*;

public class AsyncMQBatchConsumerHandlerInstrumentation implements TypeInstrumentation {
    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return hasSuperType(named("us.zoom.mq.client.clients.consumer.handle.BatchHandler"));
    }

    @Override
    public void transform(TypeTransformer transformer) {
        transformer.applyAdviceToMethod(
                named("onMessages")
                        .and(takesArgument(0, named("java.util.List")))
                        .and(returns(named("us.zoom.mq.common.BatchConsumeResult")))
                        .and(takesArguments(1))
                        .and(isPublic()),
                AsyncMQBatchConsumerAdvice.class.getName());
    }
}
