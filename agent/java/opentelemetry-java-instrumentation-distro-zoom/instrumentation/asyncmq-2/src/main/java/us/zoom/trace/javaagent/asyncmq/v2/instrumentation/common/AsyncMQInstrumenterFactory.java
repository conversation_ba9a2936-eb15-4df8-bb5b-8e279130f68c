/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import static java.util.Collections.emptyList;

import com.google.errorprone.annotations.CanIgnoreReturnValue;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.instrumentation.api.incubator.semconv.messaging.MessageOperation;
import io.opentelemetry.instrumentation.api.incubator.semconv.messaging.MessagingAttributesExtractor;
import io.opentelemetry.instrumentation.api.incubator.semconv.messaging.MessagingAttributesGetter;
import io.opentelemetry.instrumentation.api.incubator.semconv.messaging.MessagingSpanNameExtractor;
import io.opentelemetry.instrumentation.api.instrumenter.AttributesExtractor;
import io.opentelemetry.instrumentation.api.instrumenter.ErrorCauseExtractor;
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter;
import io.opentelemetry.instrumentation.api.instrumenter.InstrumenterBuilder;
import io.opentelemetry.instrumentation.api.instrumenter.SpanKindExtractor;
import io.opentelemetry.instrumentation.api.internal.PropagatorBasedSpanLinksExtractor;

import java.util.Collections;
import java.util.List;

/**
 * This class is internal and is hence not for public use. Its APIs are unstable and can change at
 * any time.
 */
public final class AsyncMQInstrumenterFactory {

    private final OpenTelemetry openTelemetry;
    private final String instrumentationName;
    private ErrorCauseExtractor errorCauseExtractor = ErrorCauseExtractor.getDefault();
    private List<String> capturedHeaders = emptyList();
    private boolean captureExperimentalSpanAttributes = false;
    private boolean messagingReceiveInstrumentationEnabled = false;

    public AsyncMQInstrumenterFactory(OpenTelemetry openTelemetry, String instrumentationName) {
        this.openTelemetry = openTelemetry;
        this.instrumentationName = instrumentationName;
    }

    @CanIgnoreReturnValue
    public AsyncMQInstrumenterFactory setErrorCauseExtractor(ErrorCauseExtractor errorCauseExtractor) {
        this.errorCauseExtractor = errorCauseExtractor;
        return this;
    }

    @CanIgnoreReturnValue
    public AsyncMQInstrumenterFactory setCapturedHeaders(List<String> capturedHeaders) {
        this.capturedHeaders = capturedHeaders;
        return this;
    }

    @CanIgnoreReturnValue
    public AsyncMQInstrumenterFactory setCaptureExperimentalSpanAttributes(
            boolean captureExperimentalSpanAttributes) {
        this.captureExperimentalSpanAttributes = captureExperimentalSpanAttributes;
        return this;
    }

    /**
     * @deprecated if you have a need for this configuration option please open an issue in the <a
     * href="https://github.com/open-telemetry/opentelemetry-java-instrumentation/issues">opentelemetry-java-instrumentation</a>
     * repository.
     */
    @Deprecated
    @CanIgnoreReturnValue
    public AsyncMQInstrumenterFactory setPropagationEnabled(boolean propagationEnabled) {
        return this;
    }

    @CanIgnoreReturnValue
    public AsyncMQInstrumenterFactory setMessagingReceiveInstrumentationEnabled(
            boolean messagingReceiveInstrumentationEnabled) {
        this.messagingReceiveInstrumentationEnabled = messagingReceiveInstrumentationEnabled;
        return this;
    }

    public Instrumenter<ProduceRequest, ProduceResultWrapper> createProducerInstrumenter() {
        return createProducerInstrumenter(Collections.emptyList());
    }

    public Instrumenter<ProduceRequest, ProduceResultWrapper> createProducerInstrumenter(
            Iterable<AttributesExtractor<ProduceRequest, ProduceResultWrapper>> extractors) {

        AsyncMQProducerAttributesGetter getter = AsyncMQProducerAttributesGetter.INSTANCE;
        MessageOperation operation = MessageOperation.PUBLISH;

        return Instrumenter.<ProduceRequest, ProduceResultWrapper>builder(
                        openTelemetry,
                        instrumentationName,
                        MessagingSpanNameExtractor.create(getter, operation))
                .addAttributesExtractor(
                        buildMessagingAttributesExtractor(getter, operation, capturedHeaders))
                .addAttributesExtractors(extractors)
                .addAttributesExtractor(new AsyncMQProducerAttributesExtractor())
                .setErrorCauseExtractor(errorCauseExtractor)
                .buildProducerInstrumenter(AsyncMQProducerAttributesSetter.INSTANCE);
    }


    public Instrumenter<ConsumeRequest, Void> createConsumerProcessInstrumenter() {
        return createConsumerProcessInstrumenter(Collections.emptyList());
    }

    public Instrumenter<ConsumeRequest, Void> createConsumerProcessInstrumenter(
            Iterable<AttributesExtractor<ConsumeRequest, Void>> extractors) {
        AsyncMQConsumerAttributesGetter getter = AsyncMQConsumerAttributesGetter.INSTANCE;
        MessageOperation operation = MessageOperation.PROCESS;

        InstrumenterBuilder<ConsumeRequest, Void> builder =
                Instrumenter.<ConsumeRequest, Void>builder(
                                openTelemetry,
                                instrumentationName,
                                MessagingSpanNameExtractor.create(getter, operation))
                        .addAttributesExtractor(
                                buildMessagingAttributesExtractor(getter, operation, capturedHeaders))
                        .addAttributesExtractor(new AsyncMQConsumerAttributesExtractor())
                        .addAttributesExtractors(extractors)
                        .setErrorCauseExtractor(errorCauseExtractor);
        if (captureExperimentalSpanAttributes) {
            builder.addAttributesExtractor(new AsyncMQConsumerExperimentalAttributesExtractor());
        }

        if (messagingReceiveInstrumentationEnabled) {
            builder.addSpanLinksExtractor(
                    new PropagatorBasedSpanLinksExtractor<>(
                            openTelemetry.getPropagators().getTextMapPropagator(),
                            AsyncMQConsumerRecordGetter.INSTANCE));
            return builder.buildInstrumenter(SpanKindExtractor.alwaysConsumer());
        } else {
            return builder.buildConsumerInstrumenter(AsyncMQConsumerRecordGetter.INSTANCE);
        }
    }

    private static <REQUEST, RESPONSE>
    AttributesExtractor<REQUEST, RESPONSE> buildMessagingAttributesExtractor(
            MessagingAttributesGetter<REQUEST, RESPONSE> getter,
            MessageOperation operation,
            List<String> capturedHeaders) {
        return MessagingAttributesExtractor.builder(getter, operation)
                .setCapturedHeaders(capturedHeaders)
                .build();
    }
}