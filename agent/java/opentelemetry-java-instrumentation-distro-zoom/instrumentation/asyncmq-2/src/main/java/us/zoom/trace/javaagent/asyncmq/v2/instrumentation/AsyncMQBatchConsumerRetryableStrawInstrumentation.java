/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation;

import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;

import static io.opentelemetry.javaagent.extension.matcher.AgentElementMatchers.hasSuperType;
import static net.bytebuddy.matcher.ElementMatchers.isPublic;
import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.returns;
import static net.bytebuddy.matcher.ElementMatchers.takesArgument;
import static net.bytebuddy.matcher.ElementMatchers.takesArguments;

public class AsyncMQBatchConsumerRetryableStrawInstrumentation implements TypeInstrumentation {


    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return hasSuperType(named("us.zoom.mq.client.clients.consumer.RetryableStraw"));
    }

    @Override
    public void transform(TypeTransformer transformer) {
        transformer.applyAdviceToMethod(
                named("onMessage")
                        .and(takesArgument(0, named("java.util.List")))
                        .and(returns(boolean.class))
                        .and(takesArguments(1))
                        .and(isPublic()),
                AsyncMQBatchConsumerAdvice.class.getName());
    }
}
