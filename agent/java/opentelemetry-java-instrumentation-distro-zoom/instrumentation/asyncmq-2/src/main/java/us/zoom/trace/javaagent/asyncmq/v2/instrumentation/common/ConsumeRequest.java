package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import us.zoom.mq.common.entity.TaskEntity;

import java.util.Map;

import static us.zoom.mq.common.client.Constant.SIMPLE_PROTOCOL_VERSION;

/**
 * <AUTHOR>
 */
public class ConsumeRequest {

    private TaskEntity<?> taskEntity;

    public ConsumeRequest(TaskEntity<?> taskEntity) {
        this.taskEntity = taskEntity;
    }

    public boolean isSimpleProtocolVersion() {
        return taskEntity != null && SIMPLE_PROTOCOL_VERSION.equals(taskEntity.getProtocolVersion());
    }

    public Map<String, String> getTrackingInfo() {
        if (taskEntity == null || taskEntity.getTaskContext() == null) {
            return null;
        }
        return taskEntity.getTaskContext().getTrackingInfo();
    }

    public Map<String, Object> getExtraInfo() {
        if (taskEntity == null || taskEntity.getTaskContext() == null) {
            return null;
        }
        return taskEntity.getTaskContext().getExtraInfo();
    }

    public String getKey() {
        if (taskEntity == null || taskEntity.getMetadata() == null) {
            return null;
        }
        return taskEntity.getMetadata().getKey();
    }

    public String getTopic() {
        if (taskEntity == null || taskEntity.getTaskContext() == null) {
            return null;
        }
        return taskEntity.getTaskContext().getTopicName();
    }

    public String getConsumerGroup() {
        if (taskEntity == null || taskEntity.getTaskContext() == null) {
            return null;
        }
        return taskEntity.getTaskContext().getGroup();
    }

    public Integer getPartition() {
        if (taskEntity == null || taskEntity.getMetadata() == null) {
            return null;
        }
        return taskEntity.getMetadata().getPartition();
    }

    public Long getOffset() {
        if (taskEntity == null || taskEntity.getMetadata() == null) {
            return null;
        }
        return taskEntity.getMetadata().getOffset();
    }

    public Integer getMessageSize() {
        if (taskEntity == null || taskEntity.getTaskContext() == null) {
            return null;
        }
        return taskEntity.getTaskContext().getPayloadByteSize();
    }

    public Long getProduceTime() {
        if (taskEntity == null || taskEntity.getTaskContext() == null) {
            return null;
        }
        return taskEntity.getTaskContext().getProduceTime();
    }

    public String getClientId() {
        if (taskEntity == null || taskEntity.getTaskContext() == null || taskEntity.getTaskContext().getClientInfo() == null) {
            return null;
        }
        return taskEntity.getTaskContext().getClientInfo().getClientId();
    }

    public boolean hasPayload() {
        return taskEntity != null && taskEntity.getPayload() != null;
    }

    public String getProtocolVersion() {
        return taskEntity != null ? taskEntity.getProtocolVersion() : null;
    }

    public String getTaskId() {
        if (taskEntity == null || taskEntity.getTaskContext() == null) {
            return null;
        }
        return taskEntity.getTaskContext().getTaskId();
    }

    public String getOriginalTaskId() {
        if (taskEntity == null || taskEntity.getTaskContext() == null) {
            return null;
        }
        return taskEntity.getTaskContext().getOriginalTaskId();
    }

}
