/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter;
import io.opentelemetry.javaagent.bootstrap.internal.AgentInstrumentationConfig;
import io.opentelemetry.javaagent.bootstrap.internal.ExperimentalConfig;

public final class AsyncMQSingletons {
    private static final String INSTRUMENTATION_NAME = "io.opentelemetry.asyncmq-2";

    private static final boolean PRODUCER_PROPAGATION_ENABLED =
            AgentInstrumentationConfig.get()
                    .getBoolean("otel.instrumentation.asyncmq.producer-propagation.enabled", true);

    private static final Instrumenter<ProduceRequest, ProduceResultWrapper> PRODUCER_INSTRUMENTER;
    private static final Instrumenter<ConsumeRequest, Void> CONSUMER_PROCESS_INSTRUMENTER;

    static {
        AsyncMQInstrumenterFactory instrumenterFactory =
                new AsyncMQInstrumenterFactory(GlobalOpenTelemetry.get(), INSTRUMENTATION_NAME)
                        .setCapturedHeaders(ExperimentalConfig.get().getMessagingHeaders())
                        .setCaptureExperimentalSpanAttributes(
                                AgentInstrumentationConfig.get()
                                        .getBoolean("otel.instrumentation.asyncmq.experimental-span-attributes", false))
                        .setMessagingReceiveInstrumentationEnabled(
                                ExperimentalConfig.get().messagingReceiveInstrumentationEnabled());
        PRODUCER_INSTRUMENTER = instrumenterFactory.createProducerInstrumenter();
        CONSUMER_PROCESS_INSTRUMENTER = instrumenterFactory.createConsumerProcessInstrumenter();
    }

    public static boolean isProducerPropagationEnabled() {
        return PRODUCER_PROPAGATION_ENABLED;
    }

    public static Instrumenter<ProduceRequest, ProduceResultWrapper> producerInstrumenter() {
        return PRODUCER_INSTRUMENTER;
    }


    public static Instrumenter<ConsumeRequest, Void> consumerProcessInstrumenter() {
        return CONSUMER_PROCESS_INSTRUMENTER;
    }

    private AsyncMQSingletons() {
    }
}