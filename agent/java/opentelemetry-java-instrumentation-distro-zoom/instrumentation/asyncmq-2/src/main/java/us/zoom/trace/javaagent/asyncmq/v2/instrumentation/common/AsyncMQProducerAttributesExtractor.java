/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.api.common.AttributesBuilder;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.context.Context;
import io.opentelemetry.instrumentation.api.instrumenter.AttributesExtractor;
import us.zoom.mq.common.entity.Metadata;
import us.zoom.mq.common.entity.TaskContext;

import javax.annotation.Nullable;

import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_MESSAGE_CODE;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_MESSAGE_KEY;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_MESSAGE_OFFSET;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_MESSAGE_TOMBSTONE;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_ORIGINAL_TASK_ID;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_TASK_ID;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_CLIENT_ID;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_DESTINATION_PARTITION_ID;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_MESSAGE_BODY_SIZE;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_PROTOCOL_VERSION;

final class AsyncMQProducerAttributesExtractor
        implements AttributesExtractor<ProduceRequest, ProduceResultWrapper> {

    @Override
    public void onStart(
            AttributesBuilder attributes, Context parentContext, ProduceRequest request) {

        String key = request.getKey();
        if (key != null && !key.isBlank()) {
            attributes.put(MESSAGING_ASYNCMQ_MESSAGE_KEY, key);
        }
        if (!request.hasPayload()) {
            attributes.put(MESSAGING_ASYNCMQ_MESSAGE_TOMBSTONE, true);
        }
        if (request.getOriginalTaskId() != null && !request.getOriginalTaskId().isBlank()) {
            attributes.put(MESSAGING_ASYNCMQ_ORIGINAL_TASK_ID, request.getOriginalTaskId());
        }
        String protocolVersion = request.getProtocolVersion();
        if (protocolVersion != null && !protocolVersion.isBlank()) {
            attributes.put(MESSAGING_PROTOCOL_VERSION, protocolVersion);
        }
    }

    @Override
    public void onEnd(
            AttributesBuilder attributes,
            Context context,
            ProduceRequest request,
            @Nullable ProduceResultWrapper result,
            @Nullable Throwable error) {
        Span span = Span.fromContext(context);
        if (error != null) {
            span.recordException(error);
        }
        if (result == null) {
            return;
        }
        span.setAttribute(MESSAGING_ASYNCMQ_MESSAGE_CODE, result.getCode());
        boolean success = result.isSuccess();
        if (!success) {
            Exception exception = result.getException();
            if (exception != null) {
                span.recordException(exception);
            }
        } else {
            if (result.getMetadata() != null) {
                Metadata metadata = result.getMetadata();
                span.setAttribute(MESSAGING_DESTINATION_PARTITION_ID, String.valueOf(metadata.getPartition()));
                span.setAttribute(MESSAGING_ASYNCMQ_MESSAGE_OFFSET, metadata.getOffset());
            }


            if (result.getTaskContext() != null) {
                TaskContext taskContext = result.getTaskContext();
                span.setAttribute(MESSAGING_MESSAGE_BODY_SIZE, taskContext.getPayloadByteSize());
                if (taskContext.getClientInfo() != null && !taskContext.getClientInfo().getClientId().isBlank()) {
                    span.setAttribute(MESSAGING_CLIENT_ID, taskContext.getClientInfo().getClientId());
                }
                String taskId = taskContext.getTaskId();
                if (taskId != null && !taskId.isBlank()) {
                    span.setAttribute(MESSAGING_ASYNCMQ_TASK_ID, taskId);
                }
            }

        }
    }
}
