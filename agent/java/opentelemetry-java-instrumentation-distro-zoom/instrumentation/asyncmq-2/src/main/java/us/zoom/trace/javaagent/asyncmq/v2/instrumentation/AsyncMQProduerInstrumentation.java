/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation;

import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.client.task.Task;
import us.zoom.mq.common.listener.ProduceListener;
import us.zoom.mq.common.response.ProduceResult;
import us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.AsyncMQProducerListenerWrapper;
import us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.ProduceRequest;
import us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.ProduceResultWrapper;


import static io.opentelemetry.javaagent.bootstrap.Java8BytecodeBridge.currentContext;
import static io.opentelemetry.javaagent.extension.matcher.AgentElementMatchers.hasSuperType;
import static net.bytebuddy.matcher.ElementMatchers.isPublic;
import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.returns;
import static net.bytebuddy.matcher.ElementMatchers.takesArgument;
import static us.zoom.mq.client.clients.producer.AsyncMQProducer.LOGGER_ONLY_LISTENER;
import static us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.AsyncMQSingletons.producerInstrumenter;
import static us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.AsyncMqHelperClass.buildProduceResultWrapper;

public class AsyncMQProduerInstrumentation implements TypeInstrumentation {

    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return hasSuperType(named("us.zoom.mq.client.clients.producer.Producer"));
    }

    @Override
    public void transform(TypeTransformer transformer) {

        transformer.applyAdviceToMethod(
                named("sendSync")
                        .and(takesArgument(0, named("us.zoom.mq.common.client.task.Task")))
                        .and(returns(named("us.zoom.mq.common.Result")))
                        .and(isPublic()),
                AsyncMQProducerSendSyncAdvice.class.getName());

        transformer.applyAdviceToMethod(
                named("sendAsync")
                        .and(takesArgument(0, named("us.zoom.mq.common.client.task.Task")))
                        .and(takesArgument(1, named("us.zoom.mq.common.listener.ProduceListener")))
                        .and(isPublic()),
                AsyncMQProducerSendASyncAdvice.class.getName());

        //todo add job advice
//        transformer.applyAdviceToMethod(
//                named("createDelayJob")
//                        .and(takesArgument(0, named("us.zoom.mq.common.client.job.DelayJob")))
//                        .and(isPublic()),
//                this.getClass().getName() + "$AsyncMQConsumerAdvice");
//
//        transformer.applyAdviceToMethod(
//                named("createFixedTimeJob")
//                        .and(takesArgument(0, named("us.zoom.mq.common.client.job.FixedTimeJob")))
//                        .and(isPublic()),
//                this.getClass().getName() + "$AsyncMQConsumerAdvice");
    }

    @SuppressWarnings("unused")
    public static class AsyncMQProducerSendSyncAdvice {

        @Advice.OnMethodEnter(suppress = Throwable.class)
        public static <T> void onEnter(@Advice.Argument(value = 0) Task<T> task,
                                       @Advice.Local("otelRequest") ProduceRequest request,
                                       @Advice.Local("otelContext") Context context,
                                       @Advice.Local("otelScope") Scope scope) {
            Context parentContext = currentContext();
            request = new ProduceRequest(task);

            if (task == null || !producerInstrumenter().shouldStart(parentContext, request)) {
                return;
            }

            context = producerInstrumenter().start(parentContext, request);
            scope = context.makeCurrent();
        }


        @Advice.OnMethodExit(onThrowable = Throwable.class, suppress = Throwable.class)
        public static <T> void onExit(@Advice.Thrown Throwable throwable,
                                      @Advice.Return Result<ProduceResult> result,
                                      @Advice.Local("otelRequest") ProduceRequest request,
                                      @Advice.Local("otelContext") Context context,
                                      @Advice.Local("otelScope") Scope scope) {
            if (scope != null) {
                scope.close();
                ProduceResultWrapper wrapper = buildProduceResultWrapper(result);
                producerInstrumenter().end(context, request, wrapper, throwable);
            }
        }
    }

    @SuppressWarnings("unused")
    public static class AsyncMQProducerSendASyncAdvice {

        @Advice.OnMethodEnter(suppress = Throwable.class)
        public static <T> void onEnter(@Advice.Argument(value = 0) Task<T> task,
                                       @Advice.Argument(value = 1, readOnly = false) ProduceListener<T> produceListener,
                                       @Advice.Local("otelRequest") ProduceRequest request,
                                       @Advice.Local("otelContext") Context context,
                                       @Advice.Local("otelScope") Scope scope) {
            Context parentContext = currentContext();
            request = new ProduceRequest(task);

            if (task == null || !producerInstrumenter().shouldStart(parentContext, request)) {
                return;
            }

            context = producerInstrumenter().start(parentContext, request);
            scope = context.makeCurrent();

            final ProduceListener<T> listener = produceListener == null ? LOGGER_ONLY_LISTENER : produceListener;
            produceListener = new AsyncMQProducerListenerWrapper<>(context, parentContext, request, listener);
        }


        @Advice.OnMethodExit(onThrowable = Throwable.class, suppress = Throwable.class)
        public static <T> void onExit(@Advice.Thrown Throwable throwable,
                                      @Advice.Local("otelRequest") ProduceRequest request,
                                      @Advice.Local("otelContext") Context context,
                                      @Advice.Local("otelScope") Scope scope) {


            if (scope != null) {
                scope.close();
                if (throwable != null) {
                    producerInstrumenter().end(context, request, null, throwable);
                }
                // span finished by ProduceListener
            }
        }
    }
}
