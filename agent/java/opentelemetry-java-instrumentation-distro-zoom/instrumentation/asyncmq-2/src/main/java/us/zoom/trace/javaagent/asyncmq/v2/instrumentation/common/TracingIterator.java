/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter;
import us.zoom.mq.common.entity.TaskEntity;

import javax.annotation.Nullable;
import java.util.Iterator;

import static io.opentelemetry.javaagent.bootstrap.Java8BytecodeBridge.currentContext;

/**
 * This class is internal and is hence not for public use. Its APIs are unstable and can change at
 * any time.
 */
public class TracingIterator<T> implements Iterator<TaskEntity<T>> {

    private final Iterator<TaskEntity<T>> delegateIterator;
    private final Instrumenter<ConsumeRequest, Void> instrumenter;


    /*
     * Note: this may potentially create problems if this iterator is used from different threads. But
     * at the moment we cannot do much about this.
     */
    @Nullable
    private ConsumeRequest currentRequest;
    @Nullable
    private Context currentContext;
    @Nullable
    private Scope currentScope;

    private TracingIterator(
            Iterator<TaskEntity<T>> delegateIterator,
            Instrumenter<ConsumeRequest, Void> instrumenter) {
        this.delegateIterator = delegateIterator;
        this.instrumenter = instrumenter;
    }

    public static <T> Iterator<TaskEntity<T>> wrap(
            Iterator<TaskEntity<T>> delegateIterator,
            Instrumenter<ConsumeRequest, Void> instrumenter) {
        return new TracingIterator<>(
                delegateIterator, instrumenter);
    }

    @Override
    public boolean hasNext() {
        closeScopeAndEndSpan();
        return delegateIterator.hasNext();
    }

    @Override
    public TaskEntity<T> next() {
        // in case they didn't call hasNext()...
        closeScopeAndEndSpan();

        // it's important not to suppress consumer span creation here using Instrumenter.shouldStart()
        // because this instrumentation can leak the context and so there may be a leaked consumer span
        // in the context, in which case it's important to overwrite the leaked span instead of
        // suppressing the correct span
        // (https://github.com/open-telemetry/opentelemetry-java-instrumentation/issues/1947)
        TaskEntity<T> next = delegateIterator.next();
        if (next != null) {
            currentRequest = new ConsumeRequest(next);
            if (instrumenter.shouldStart(currentContext(), currentRequest)) {
                currentContext = instrumenter.start(Context.root(), currentRequest);
                currentScope = currentContext.makeCurrent();
            }
        }
        return next;
    }

    private void closeScopeAndEndSpan() {
        if (currentScope != null) {
            currentScope.close();
            instrumenter.end(currentContext, currentRequest, null, null);
            currentScope = null;
            currentRequest = null;
            currentContext = null;
        }
    }

    @Override
    public void remove() {
        delegateIterator.remove();
    }
}
