/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.Collection;
import java.util.List;
import java.util.ListIterator;

/**
 * This class is internal and is hence not for public use. Its APIs are unstable and can change at
 * any time.
 */
public class TracingList<T> extends TracingIterable<T> implements List<TaskEntity<T>> {
    private final List<TaskEntity<T>> delegate;

    private TracingList(
            List<TaskEntity<T>> delegate,
            Instrumenter<ConsumeRequest, Void> instrumenter) {
        super(delegate, instrumenter);
        this.delegate = delegate;
    }

    public static <T> List<TaskEntity<T>> wrap(
            List<TaskEntity<T>> delegate,
            Instrumenter<ConsumeRequest, Void> instrumenter) {
        return new TracingList<>(delegate, instrumenter);
    }

    @Override
    public int size() {
        return delegate.size();
    }

    @Override
    public boolean isEmpty() {
        return delegate.isEmpty();
    }

    @Override
    public boolean contains(Object o) {
        return delegate.contains(o);
    }

    @Override
    public Object[] toArray() {
        return delegate.toArray();
    }

    @Override
    public <T> T[] toArray(T[] a) {
        return delegate.toArray(a);
    }

    @Override
    public boolean add(TaskEntity<T> TaskEntity) {
        return delegate.add(TaskEntity);
    }

    @Override
    public void add(int index, TaskEntity<T> element) {
        delegate.add(index, element);
    }

    @Override
    public boolean remove(Object o) {
        return delegate.remove(o);
    }

    @Override
    public TaskEntity<T> remove(int index) {
        return delegate.remove(index);
    }

    @Override
    public boolean containsAll(Collection<?> c) {
        return delegate.containsAll(c);
    }

    @Override
    public boolean addAll(Collection<? extends TaskEntity<T>> c) {
        return delegate.addAll(c);
    }

    @Override
    public boolean addAll(int index, Collection<? extends TaskEntity<T>> c) {
        return delegate.addAll(index, c);
    }

    @Override
    public boolean removeAll(Collection<?> c) {
        return delegate.removeAll(c);
    }

    @Override
    public boolean retainAll(Collection<?> c) {
        return delegate.retainAll(c);
    }

    @Override
    public void clear() {
        delegate.clear();
    }

    @Override
    public TaskEntity<T> get(int index) {
        // TODO: should this be instrumented as well?
        return delegate.get(index);
    }

    @Override
    public TaskEntity<T> set(int index, TaskEntity<T> element) {
        return delegate.set(index, element);
    }

    @Override
    public int indexOf(Object o) {
        return delegate.indexOf(o);
    }

    @Override
    public int lastIndexOf(Object o) {
        return delegate.lastIndexOf(o);
    }

    @Override
    public ListIterator<TaskEntity<T>> listIterator() {
        // TODO: the API for ListIterator is not really good to instrument it in context of Kafka
        // Consumer so we will not do that for now
        return delegate.listIterator();
    }

    @Override
    public ListIterator<TaskEntity<T>> listIterator(int index) {
        // TODO: the API for ListIterator is not really good to instrument it in context of Kafka
        // Consumer so we will not do that for now
        return delegate.listIterator(index);
    }

    @Override
    public List<TaskEntity<T>> subList(int fromIndex, int toIndex) {
        // TODO: the API for subList is not really good to instrument it in context of Kafka
        // Consumer so we will not do that for now
        // Kafka is essentially a sequential commit log. We should only enable tracing when traversing
        // sequentially with an iterator
        return delegate.subList(fromIndex, toIndex);
    }
}
