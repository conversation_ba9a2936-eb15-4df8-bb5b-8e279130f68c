package us.zoom.trace.javaagent.asyncmq.v2.instrumentation;

import net.bytebuddy.asm.Advice;
import us.zoom.mq.common.entity.TaskEntity;
import us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.TracingList;

import java.util.List;

import static us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.AsyncMQSingletons.consumerProcessInstrumenter;

public class AsyncMQBatchConsumerAdvice {

    @Advice.OnMethodEnter(suppress = Throwable.class)
    public static <T> void onEnter(@Advice.Argument(value = 0, readOnly = false) List taskEntities) {
        // double check, make sure the input is correct type
        if (taskEntities != null && !taskEntities.isEmpty()
                && taskEntities.get(0) instanceof TaskEntity<?>) {
            taskEntities = TracingList.wrap(taskEntities, consumerProcessInstrumenter());
        }
    }
}