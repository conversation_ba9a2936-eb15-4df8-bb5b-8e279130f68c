/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation;

import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;
import us.zoom.mq.common.entity.TaskEntity;
import us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.ConsumeRequest;

import static io.opentelemetry.javaagent.bootstrap.Java8BytecodeBridge.currentContext;
import static io.opentelemetry.javaagent.extension.matcher.AgentElementMatchers.hasSuperType;
import static net.bytebuddy.matcher.ElementMatchers.*;
import static us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.AsyncMQSingletons.consumerProcessInstrumenter;

public class AsyncMQConsumerInstrumentation implements TypeInstrumentation {


    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return hasSuperType(named("us.zoom.mq.client.clients.consumer.handle.SingleHandler"));
    }

    @Override
    public void transform(TypeTransformer transformer) {
        transformer.applyAdviceToMethod(
                named("onMessage")
                        .and(takesArgument(0, named("us.zoom.mq.common.entity.TaskEntity")))
                        .and(isPublic()),
                AsyncMQConsumerAdvice.class.getName());
    }

    @SuppressWarnings("unused")
    public static class AsyncMQConsumerAdvice {

        @Advice.OnMethodEnter(suppress = Throwable.class)
        public static <T> void onEnter(@Advice.Argument(value = 0) TaskEntity<T> taskEntity,
                                       @Advice.Local("otelRequest") ConsumeRequest request,
                                       @Advice.Local("otelContext") Context context,
                                       @Advice.Local("otelScope") Scope scope) {
            Context parentContext = currentContext();
            request = new ConsumeRequest(taskEntity);

            if (taskEntity == null || !consumerProcessInstrumenter().shouldStart(parentContext, request)) {
                return;
            }

            context = consumerProcessInstrumenter().start(parentContext, request);
            scope = context.makeCurrent();
        }


        @Advice.OnMethodExit(onThrowable = Throwable.class, suppress = Throwable.class)
        public static <T> void onExit(@Advice.Thrown Throwable throwable,
                                      @Advice.Local("otelRequest") ConsumeRequest request,
                                      @Advice.Local("otelContext") Context context,
                                      @Advice.Local("otelScope") Scope scope) {
            if (scope != null) {
                scope.close();
                consumerProcessInstrumenter().end(context, request, null, throwable);
            }
        }
    }
}
