/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.context.propagation.TextMapSetter;
import org.jetbrains.annotations.Nullable;

/**
 * This class is internal and is hence not for public use. Its APIs are unstable and can change at
 * any time.
 */
enum AsyncMQProducerAttributesSetter
        implements TextMapSetter<ProduceRequest> {
    INSTANCE;

    @Override
    public void set(@Nullable ProduceRequest request, String key, String value) {
        if (request == null) {
            return;
        }
        request.addKeyValueInTrackingContext(key, value);
    }
}
