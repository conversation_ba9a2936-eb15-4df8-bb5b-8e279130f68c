package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import us.zoom.mq.common.client.task.Task;
import us.zoom.mq.common.enums.ProtocolStrategy;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ProduceRequest {
    private Task<?> task;

    public ProduceRequest(Task<?> task) {
        this.task = task;
    }

    public String getTopic() {
        return task != null ? task.getTopicName() : null;
    }

    public void addKeyValueInTrackingContext(String key, String value) {
        if (task == null) {
            return;
        }
        ProtocolStrategy protocolStrategy = task.getProtocolStrategy();
        if (protocolStrategy == ProtocolStrategy.SIMPLE) {
            Map<String, Object> extraInfo = task.getExtraInfo();
            if (extraInfo == null) {
                extraInfo = new HashMap<>();
                task.setExtraInfo(extraInfo);
            }
            extraInfo.putIfAbsent(key, value);
        } else {
            Map<String, String> trackingInfo = task.getTrackingInfo();
            if (trackingInfo == null) {
                trackingInfo = new HashMap<>();
                task.setTrackingInfo(trackingInfo);
            }
            trackingInfo.putIfAbsent(key, value);
        }
    }


    public String getKey() {
        return task != null ? task.getKey() : null;
    }

    public String getOriginalTaskId() {
        return task != null ? task.getOriginalTaskId() : null;
    }

    public boolean hasPayload() {
        return task != null && task.getPayload() != null;
    }

    public String getProtocolVersion() {
        return (task != null && task.getProtocolStrategy() != null) ?
                task.getProtocolStrategy().getProtocolVersion() : null;
    }
}
