/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.context.propagation.TextMapGetter;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nullable;

enum AsyncMQConsumerRecordGetter implements TextMapGetter<ConsumeRequest> {
    INSTANCE;

    @Override
    public Iterable<String> keys(ConsumeRequest carrier) {
        if (carrier == null) {
            return Collections.emptyList();
        }
        boolean simpleProtocolVersion = carrier.isSimpleProtocolVersion();
        if (simpleProtocolVersion) {
            return Optional.ofNullable(carrier.getExtraInfo()).map(Map::keySet).orElse(Collections.emptySet());
        } else {
            return Optional.ofNullable(carrier.getTrackingInfo()).map(Map::keySet).orElse(Collections.emptySet());
        }
    }

    @Nullable
    @Override
    public String get(@Nullable ConsumeRequest carrier, String key) {
        if (carrier == null) {
            return null;
        }
        if (carrier.isSimpleProtocolVersion()) {
            Map<String, Object> extraInfo = carrier.getExtraInfo();
            return extraInfo != null ? String.valueOf(extraInfo.get(key)) : null;
        } else {
            Map<String, String> trackingInfo = carrier.getTrackingInfo();
            return trackingInfo != null ? trackingInfo.get(key) : null;
        }
    }
}
