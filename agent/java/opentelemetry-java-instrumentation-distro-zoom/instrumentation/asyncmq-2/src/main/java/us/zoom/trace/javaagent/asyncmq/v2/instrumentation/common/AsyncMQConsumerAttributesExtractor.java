/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.api.common.AttributesBuilder;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.context.Context;
import io.opentelemetry.instrumentation.api.instrumenter.AttributesExtractor;

import javax.annotation.Nullable;

import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_CONSUMER_GROUP;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_MESSAGE_KEY;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_MESSAGE_OFFSET;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_MESSAGE_TOMBSTONE;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_ORIGINAL_TASK_ID;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_ASYNCMQ_TASK_ID;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_DESTINATION_PARTITION_ID;
import static us.zoom.trace.javaagent.common.AttributeKeys.MESSAGING_PROTOCOL_VERSION;


final class AsyncMQConsumerAttributesExtractor
        implements AttributesExtractor<ConsumeRequest, Void> {

    @Override
    public void onStart(
            AttributesBuilder attributes, Context parentContext, ConsumeRequest request) {

        attributes.put(MESSAGING_DESTINATION_PARTITION_ID, String.valueOf(request.getPartition()));
        attributes.put(MESSAGING_ASYNCMQ_MESSAGE_OFFSET, request.getOffset());

        String key = request.getKey();
        if (key != null && !key.isBlank()) {
            attributes.put(MESSAGING_ASYNCMQ_MESSAGE_KEY, key);
        }
        if (!request.hasPayload()) {
            attributes.put(MESSAGING_ASYNCMQ_MESSAGE_TOMBSTONE, true);
        }

        String consumerGroup = request.getConsumerGroup();
        if (consumerGroup != null && !consumerGroup.isEmpty()) {
            attributes.put(MESSAGING_ASYNCMQ_CONSUMER_GROUP, consumerGroup);
        }
        if (request.getProtocolVersion() != null && !request.getProtocolVersion().isBlank()) {
            attributes.put(MESSAGING_PROTOCOL_VERSION, request.getProtocolVersion());
        }
        if (request.getOriginalTaskId() != null && !request.getOriginalTaskId().isBlank()) {
            attributes.put(MESSAGING_ASYNCMQ_ORIGINAL_TASK_ID, request.getOriginalTaskId());
        }
        if (request.getTaskId() != null && !request.getTaskId().isBlank()) {
            attributes.put(MESSAGING_ASYNCMQ_TASK_ID, request.getTaskId());
        }
    }

    @Override
    public void onEnd(
            AttributesBuilder attributes,
            Context context,
            ConsumeRequest request,
            @Nullable Void unused,
            @Nullable Throwable error) {
        if (error != null) {
            Span.fromContext(context).recordException(error);
        }
    }
}
