/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.define;

import com.google.auto.service.AutoService;
import io.opentelemetry.javaagent.extension.instrumentation.InstrumentationModule;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import net.bytebuddy.matcher.ElementMatcher;
import us.zoom.trace.javaagent.asyncmq.v2.instrumentation.AsyncMQBatchConsumerHandlerInstrumentation;
import us.zoom.trace.javaagent.asyncmq.v2.instrumentation.AsyncMQBatchConsumerRetryableStrawInstrumentation;
import us.zoom.trace.javaagent.asyncmq.v2.instrumentation.AsyncMQConsumerInstrumentation;
import us.zoom.trace.javaagent.asyncmq.v2.instrumentation.AsyncMQProduerInstrumentation;

import java.util.ArrayList;
import java.util.List;

import static io.opentelemetry.javaagent.extension.matcher.AgentElementMatchers.hasClassesNamed;

/**
 * for asyncmq
 */
@AutoService(InstrumentationModule.class)
public final class AsyncMQInstrumentationModule extends InstrumentationModule {

    public AsyncMQInstrumentationModule() {
        super("asyncmq", "asyncmq-2");
    }

    @Override
    public List<TypeInstrumentation> typeInstrumentations() {
        return new ArrayList<>() {{
            add(new AsyncMQBatchConsumerRetryableStrawInstrumentation());
            add(new AsyncMQBatchConsumerHandlerInstrumentation());
            add(new AsyncMQProduerInstrumentation());
            add(new AsyncMQConsumerInstrumentation());
        }};
    }

    @Override
    public ElementMatcher.Junction<ClassLoader> classLoaderMatcher() {
        return hasClassesNamed("us.zoom.mq.common.client.task.Task", "us.zoom.mq.common.entity.TaskEntity");
    }

    @Override
    public boolean isHelperClass(String className) {
        return className.startsWith("us.zoom.trace.javaagent.common") || className.startsWith("io.opentelemetry.javaagent.extension") ||
                className.startsWith("us.zoom.trace.javaagent.asyncmq.v2.instrumentation");
    }
}
