/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import us.zoom.mq.common.Result;
import us.zoom.mq.common.response.ProduceResult;

public final class AsyncMqHelperClass {


    private AsyncMqHelperClass() {
    }


    public static ProduceResultWrapper buildProduceResultWrapper(Result<ProduceResult> result) {
        ProduceResultWrapper wrapper = new ProduceResultWrapper();
        if (result != null) {
            wrapper.setCode(result.getCode());
            wrapper.setException(result.getException());
            wrapper.setSuccess(result.isSuccess());
            if (result.getResponse() != null) {
                wrapper.setTaskContext(result.getResponse().getTaskContext());
                wrapper.setMetadata(result.getResponse().getMetadata());
            }
        }
        return wrapper;
    }

    public static ProduceResultWrapper buildProduceResultWrapper(ProduceResult result) {
        ProduceResultWrapper wrapper = new ProduceResultWrapper();
        if (result != null) {
            wrapper.setTaskContext(result.getTaskContext());
            wrapper.setMetadata(result.getMetadata());
        }
        return wrapper;
    }
}
