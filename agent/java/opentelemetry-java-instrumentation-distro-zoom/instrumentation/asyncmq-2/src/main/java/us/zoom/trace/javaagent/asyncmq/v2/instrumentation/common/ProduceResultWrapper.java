package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import us.zoom.mq.common.entity.Metadata;
import us.zoom.mq.common.entity.TaskContext;

/**
 * <AUTHOR>
 */
public class ProduceResultWrapper {
    private TaskContext taskContext;
    private Metadata metadata;
    private Exception exception;
    private int code;
    private boolean success;

    public ProduceResultWrapper(TaskContext taskContext, Metadata metadata, Exception exception, int code, boolean success) {
        this.taskContext = taskContext;
        this.metadata = metadata;
        this.exception = exception;
        this.code = code;
        this.success = success;
    }

    public ProduceResultWrapper() {
    }

    public TaskContext getTaskContext() {
        return taskContext;
    }

    public void setTaskContext(TaskContext taskContext) {
        this.taskContext = taskContext;
    }

    public Metadata getMetadata() {
        return metadata;
    }

    public void setMetadata(Metadata metadata) {
        this.metadata = metadata;
    }

    public Exception getException() {
        return exception;
    }

    public void setException(Exception exception) {
        this.exception = exception;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
