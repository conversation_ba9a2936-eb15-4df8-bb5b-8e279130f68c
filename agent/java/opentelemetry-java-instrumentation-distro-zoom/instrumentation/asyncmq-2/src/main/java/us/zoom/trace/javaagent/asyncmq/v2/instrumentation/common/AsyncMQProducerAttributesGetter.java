/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.instrumentation.api.incubator.semconv.messaging.MessagingAttributesGetter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;

/**
 * This class is internal and is hence not for public use. Its APIs are unstable and can change at
 * any time.
 */
enum AsyncMQProducerAttributesGetter
        implements MessagingAttributesGetter<ProduceRequest, ProduceResultWrapper> {
    INSTANCE;

    @Override
    public String getSystem(ProduceRequest request) {
        return "asyncmq";
    }

    @Override
    public String getDestination(ProduceRequest request) {
        return request.getTopic();
    }

    @Nullable
    @Override
    public String getDestinationTemplate(ProduceRequest request) {
        return null;
    }

    @Override
    public boolean isTemporaryDestination(ProduceRequest request) {
        return false;
    }

    @Override
    public boolean isAnonymousDestination(ProduceRequest request) {
        return false;
    }

    @Override
    @Nullable
    public String getConversationId(ProduceRequest request) {
        return null;
    }

    @Nullable
    @Override
    public Long getMessageBodySize(ProduceRequest request) {
        return null;
    }

    @Nullable
    @Override
    public Long getMessageEnvelopeSize(ProduceRequest request) {
        return null;
    }

    @Override
    @Nullable
    public String getMessageId(
            ProduceRequest request, @Nullable ProduceResultWrapper response) {
        return (response == null || response.getTaskContext() == null) ? null : response.getTaskContext().getTaskId();
    }

    @Nullable
    @Override
    public String getClientId(ProduceRequest request) {
        return null;
    }

    @Nullable
    @Override
    public Long getBatchMessageCount(
            ProduceRequest request, @Nullable ProduceResultWrapper response) {
        return null;
    }

    @Override
    public List<String> getMessageHeader(ProduceRequest request, String name) {
        // ignore
        return Collections.emptyList();
    }
}
