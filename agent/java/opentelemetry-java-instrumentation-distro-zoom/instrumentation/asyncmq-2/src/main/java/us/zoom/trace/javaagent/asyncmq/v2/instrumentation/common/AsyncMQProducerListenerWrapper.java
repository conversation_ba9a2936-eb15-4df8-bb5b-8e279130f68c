package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import us.zoom.mq.common.entity.TaskEntity;
import us.zoom.mq.common.listener.ProduceListener;
import us.zoom.mq.common.response.ProduceResult;

import static us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.AsyncMQSingletons.producerInstrumenter;
import static us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common.AsyncMqHelperClass.buildProduceResultWrapper;

/**
 * <AUTHOR>
 */
public class AsyncMQProducerListenerWrapper<T> implements ProduceListener<T> {

    private final Context context;
    private final Context parentContext;
    private final ProduceRequest request;
    private final ProduceListener<T> delegate;

    public AsyncMQProducerListenerWrapper(Context context, Context parentContext, ProduceRequest request, ProduceListener<T> delegate) {
        this.context = context;
        this.parentContext = parentContext;
        this.request = request;
        this.delegate = delegate;
    }

    @Override
    public void onSuccess(ProduceResult produceResult, TaskEntity<T> entity) {
        ProduceResultWrapper wrapper = buildProduceResultWrapper(produceResult);
        wrapper.setCode(200);
        wrapper.setSuccess(true);
        producerInstrumenter().end(context, request, wrapper, null);
        try (Scope ignored = parentContext.makeCurrent()) {
            delegate.onSuccess(produceResult, entity);
        }
    }

    @Override
    public void onFailed(ProduceResult produceResult, TaskEntity<T> entity, Exception e) {
        ProduceResultWrapper wrapper = buildProduceResultWrapper(produceResult);
        wrapper.setCode(-1);
        wrapper.setException(e);
        producerInstrumenter().end(context, request, wrapper, null);
        try (Scope ignored = parentContext.makeCurrent()) {
            delegate.onFailed(produceResult, entity, e);
        }
    }


}
