/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation.common;

import io.opentelemetry.instrumentation.api.incubator.semconv.messaging.MessagingAttributesGetter;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;

enum AsyncMQConsumerAttributesGetter implements MessagingAttributesGetter<ConsumeRequest, Void> {
    INSTANCE;

    @Override
    public String getSystem(ConsumeRequest request) {
        return "asyncmq";
    }

    @Override
    public String getDestination(ConsumeRequest request) {
        return request.getTopic();
    }

    @Nullable
    @Override
    public String getDestinationTemplate(ConsumeRequest request) {
        return null;
    }

    @Override
    public boolean isTemporaryDestination(ConsumeRequest request) {
        return false;
    }

    @Override
    public boolean isAnonymousDestination(ConsumeRequest request) {
        return false;
    }

    @Override
    @Nullable
    public String getConversationId(ConsumeRequest request) {
        return null;
    }

    @Nullable
    @Override
    public Long getMessageBodySize(ConsumeRequest request) {
        return request.getMessageSize() == null ? null : Long.valueOf(request.getMessageSize());
    }

    @Nullable
    @Override
    public Long getMessageEnvelopeSize(ConsumeRequest request) {
        return null;
    }

    @Override
    @Nullable
    public String getMessageId(ConsumeRequest request, @Nullable Void unused) {
        return null;
    }

    @Nullable
    @Override
    public String getClientId(ConsumeRequest request) {
        return request.getClientId();
    }

    @Nullable
    @Override
    public Long getBatchMessageCount(ConsumeRequest request, @Nullable Void unused) {
        return null;
    }

    @Override
    public List<String> getMessageHeader(ConsumeRequest request, String name) {
        // ignore
        return Collections.emptyList();
    }
}
