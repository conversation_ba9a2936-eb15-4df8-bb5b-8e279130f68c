/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.asyncmq.v2.instrumentation;


import io.opentelemetry.instrumentation.testing.junit.AgentInstrumentationExtension;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;

/**
 * This is a demo instrumentation test that verifies that the custom servlet instrumentation was
 * applied.
 */
class AsyncMqInstrumentationTest {
    @RegisterExtension
    static final AgentInstrumentationExtension instrumentation =
            AgentInstrumentationExtension.create();


    static int port;

    @BeforeAll
    static void startServer() throws Exception {

    }

    @AfterAll
    static void stopServer() throws Exception {

    }

    @Test
    void shouldAddCustomHeader() throws Exception {
        // given

    }
}
