apply from: "$rootDir/gradle/instrumentation.gradle"

muzzle {
  pass {
    group.set("us.zoom.mq")
    module.set("asyncmq-client")
    versions.set("[2.0,)")
    assertInverse.set(true)
  }
  pass {
    group.set("us.zoom.mq")
    module.set("asyncmq-common")
    versions.set("[2.0,)")
    assertInverse.set(true)
  }
  pass {
    group.set("us.zoom.mq")
    module.set("asyncmq-client-adapter")
    versions.set("[2.0,)")
    assertInverse.set(true)
  }
}
dependencies {
  compileOnly project(":bootstrap")
  compileOnly "us.zoom.mq:asyncmq-client:2.6.8"
  compileOnly "io.opentelemetry.instrumentation:opentelemetry-instrumentation-api-incubator"
  implementation project(":common")

}
