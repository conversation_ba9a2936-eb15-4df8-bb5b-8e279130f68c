package us.zoom.trace.javaagent.instrumentation.executors;

import io.opentelemetry.instrumentation.api.incubator.semconv.code.CodeAttributesGetter;


/**
 * <EMAIL>
 */
public class SchedulingCodeAttributesGetter implements CodeAttributesGetter<Runnable> {

    @Override
    public Class<?> getCodeClass(Runnable runnable) {
        return runnable.getClass();
    }

    @Override
    public String getMethodName(Runnable runnable) {
        return "run";
    }
}