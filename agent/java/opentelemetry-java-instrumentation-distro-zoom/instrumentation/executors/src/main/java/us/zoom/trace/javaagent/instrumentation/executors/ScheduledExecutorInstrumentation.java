package us.zoom.trace.javaagent.instrumentation.executors;

import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.agent.builder.AgentBuilder;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.dynamic.DynamicType;
import net.bytebuddy.matcher.ElementMatcher;
import net.bytebuddy.utility.JavaModule;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;

import java.security.ProtectionDomain;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static net.bytebuddy.matcher.ElementMatchers.hasSuperType;
import static net.bytebuddy.matcher.ElementMatchers.isMethod;
import static net.bytebuddy.matcher.ElementMatchers.isPublic;
import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.namedOneOf;
import static net.bytebuddy.matcher.ElementMatchers.takesArgument;
import static net.bytebuddy.matcher.ElementMatchers.takesArguments;

/**
 * <EMAIL>
 */
public class ScheduledExecutorInstrumentation implements TypeInstrumentation {


    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return hasSuperType(named("java.util.concurrent.ScheduledExecutorService"));
    }

    @Override
    public void transform(TypeTransformer transformer) {
        transformer.applyAdviceToMethod(
                isMethod()
                        .and(isPublic())
                        .and(namedOneOf("scheduleWithFixedDelay", "scheduleAtFixedRate"))
                        .and(takesArguments(4))
                        .and(takesArgument(0, Runnable.class))
                        .and(takesArgument(1, long.class))
                        .and(takesArgument(2, long.class))
                        .and(takesArgument(3, TimeUnit.class)),
                ScheduledExecutorInstrumentation.class.getName() + "$ScheduleWithFixedDelayAdvice");
    }

    @SuppressWarnings("unused")
    public static class ScheduleWithFixedDelayAdvice {

        @Advice.OnMethodEnter(suppress = Throwable.class)
        public static void methodEnter(
                @Advice.Argument(value = 0, readOnly = false) Runnable runnable,
                @Advice.Argument(1) long initialDelay,
                @Advice.Argument(2) long delay,
                @Advice.Argument(3) TimeUnit unit) {
            List<String> tracesScheduledClassNames = AgentConfiguration.globalConfiguration().getTracesScheduledClassNames();
            if (tracesScheduledClassNames == null || tracesScheduledClassNames.isEmpty()) {
                return;
            }
            for (String tracesScheduledClassName : tracesScheduledClassNames) {
                if (runnable != null && runnable.getClass().getName().startsWith(tracesScheduledClassName)) {
                    runnable = SchedulingRunnableWrapper.wrapIfNeeded(runnable);
                    break;
                }
            }
        }
    }


}
