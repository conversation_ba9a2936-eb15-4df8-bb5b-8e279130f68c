/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.executors;

import com.google.auto.service.AutoService;
import io.opentelemetry.javaagent.extension.instrumentation.InstrumentationModule;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;

import java.util.Arrays;
import java.util.List;


/**
 * <EMAIL>
 */
@AutoService(InstrumentationModule.class)
public class ExecutorEnhanceInstrumentationModule extends InstrumentationModule {

    public ExecutorEnhanceInstrumentationModule() {
        super("executors-enhance");
    }

    @Override
    public List<TypeInstrumentation> typeInstrumentations() {
        return Arrays.asList(new ScheduledExecutorInstrumentation(),
                new VirtualThreadInstrumentation());
    }

    @Override
    public boolean isHelperClass(String className) {
        return className.startsWith("io.opentelemetry.javaagent.extension") ||
                className.startsWith("us.zoom.trace.javaagent.instrumentation.executors");
    }
}
