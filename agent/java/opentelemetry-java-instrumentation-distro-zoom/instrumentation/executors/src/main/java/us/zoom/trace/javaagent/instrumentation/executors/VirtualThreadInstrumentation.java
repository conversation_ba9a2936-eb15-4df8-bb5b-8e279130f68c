package us.zoom.trace.javaagent.instrumentation.executors;

import io.opentelemetry.javaagent.bootstrap.executors.ExecutorAdviceHelper;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static net.bytebuddy.matcher.ElementMatchers.hasSuperType;
import static net.bytebuddy.matcher.ElementMatchers.isMethod;
import static net.bytebuddy.matcher.ElementMatchers.isPublic;
import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.namedOneOf;
import static net.bytebuddy.matcher.ElementMatchers.takesArgument;
import static net.bytebuddy.matcher.ElementMatchers.takesArguments;

/**
 * <EMAIL>
 */
public class VirtualThreadInstrumentation implements TypeInstrumentation {


    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return named("java.lang.VirtualThread");
    }

    @Override
    public void transform(TypeTransformer transformer) {
        transformer.applyAdviceToMethod(
                isMethod().and(named("submitRunContinuation")),
                VirtualThreadInstrumentation.class.getName() + "$DisablePropagationAdvice");
    }

    @SuppressWarnings("unused")
    public static class DisablePropagationAdvice {

        @Advice.OnMethodEnter(suppress = Throwable.class)
        public static void enter() {
            ExecutorAdviceHelper.disablePropagation();
        }

        @Advice.OnMethodExit(suppress = Throwable.class)
        public static void exit() {
            ExecutorAdviceHelper.enablePropagation();
        }
    }
}
