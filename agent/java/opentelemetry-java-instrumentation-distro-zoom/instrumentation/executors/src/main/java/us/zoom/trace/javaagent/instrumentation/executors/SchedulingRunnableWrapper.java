package us.zoom.trace.javaagent.instrumentation.executors;

import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;

import static io.opentelemetry.javaagent.bootstrap.Java8BytecodeBridge.currentContext;
import static us.zoom.trace.javaagent.instrumentation.executors.SchedulingSingletons.instrumenter;


/**
 * <EMAIL>
 */
public class SchedulingRunnableWrapper implements Runnable {
    private final Runnable runnable;

    private SchedulingRunnableWrapper(Runnable runnable) {
        this.runnable = runnable;
    }

    @Override
    public void run() {
        if (runnable == null) {
            return;
        }

        Context parentContext = currentContext();
        if (!instrumenter().shouldStart(parentContext, runnable)) {
            runnable.run();
            return;
        }

        Context context = instrumenter().start(parentContext, runnable);
        // remember the context, so it could be reused in error handler
        try (Scope ignored = context.makeCurrent()) {
            runnable.run();
            instrumenter().end(context, runnable, null, null);
        } catch (Throwable throwable) {
            instrumenter().end(context, runnable, null, throwable);
            throw throwable;
        }
    }

    public static Runnable wrapIfNeeded(Runnable task) {
        // We wrap only lambdas' anonymous classes and if given object has not already been wrapped.
        // Anonymous classes have '/' in class name which is not allowed in 'normal' classes.
        if (task instanceof SchedulingRunnableWrapper) {
            return task;
        }
        return new SchedulingRunnableWrapper(task);
    }
}