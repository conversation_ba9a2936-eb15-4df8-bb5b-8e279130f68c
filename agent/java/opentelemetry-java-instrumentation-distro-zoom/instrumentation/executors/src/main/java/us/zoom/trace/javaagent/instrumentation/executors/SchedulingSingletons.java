package us.zoom.trace.javaagent.instrumentation.executors;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.instrumentation.api.incubator.semconv.code.CodeAttributesExtractor;
import io.opentelemetry.instrumentation.api.incubator.semconv.code.CodeSpanNameExtractor;
import io.opentelemetry.instrumentation.api.instrumenter.AttributesExtractor;
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter;
import io.opentelemetry.instrumentation.api.instrumenter.InstrumenterBuilder;


/**
 * <EMAIL>
 */
public final class SchedulingSingletons {

    private static final Instrumenter<Runnable, Void> INSTRUMENTER;

    static {
        SchedulingCodeAttributesGetter codeAttributesGetter =
                new SchedulingCodeAttributesGetter();

        InstrumenterBuilder<Runnable, Void> builder =
                Instrumenter.<Runnable, Void>builder(
                                GlobalOpenTelemetry.get(),
                                "io.opentelemetry.jdk-scheduling",
                                CodeSpanNameExtractor.create(codeAttributesGetter))
                        .addAttributesExtractor(CodeAttributesExtractor.create(codeAttributesGetter));

        builder.addAttributesExtractor(
                AttributesExtractor.constant(AttributeKey.stringKey("job.system"), "jdk_scheduling"));

        INSTRUMENTER = builder.buildInstrumenter();
    }

    public static Instrumenter<Runnable, Void> instrumenter() {
        return INSTRUMENTER;
    }

    private SchedulingSingletons() {
    }
}