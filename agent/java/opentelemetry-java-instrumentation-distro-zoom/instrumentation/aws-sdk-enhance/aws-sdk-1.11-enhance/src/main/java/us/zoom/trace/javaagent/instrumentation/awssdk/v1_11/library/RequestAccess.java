/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.awssdk.v1_11.library;

import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.invoke.MethodType;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Nullable;

final class RequestAccess {

    private static final ClassValue<RequestAccess> REQUEST_ACCESSORS =
            new ClassValue<RequestAccess>() {
                @Override
                protected RequestAccess computeValue(Class<?> type) {
                    return new RequestAccess(type);
                }
            };


    @Nullable
    static List<String> getTableNames(Object request) {
        try {
            RequestAccess access = REQUEST_ACCESSORS.get(request.getClass());
            Object result = invokeOrNull(access.getTableNames, request);
            if (result instanceof Map) {
                return (List<String>) ((Map) result).keySet().stream().collect(Collectors.toList());
            }
        } catch (Throwable t) {
            //ignore, normally will not occur
        }
        return null;
    }


    @Nullable
    private static Object invokeOrNull(@Nullable MethodHandle method, Object obj) {
        if (method == null) {
            return null;
        }
        try {
            return method.invoke(obj);
        } catch (Throwable t) {
            return null;
        }
    }

    @Nullable
    private final MethodHandle getTableNames;

    private RequestAccess(Class<?> clz) {
        getTableNames = findAccessorOrNull(clz, "getRequestItems", Map.class);
    }

    @Nullable
    private static MethodHandle findAccessorOrNull(Class<?> clz, String methodName, Class<?> rtClazz) {
        try {
            return MethodHandles.publicLookup()
                    .findVirtual(clz, methodName, MethodType.methodType(rtClazz));
        } catch (Throwable t) {
            return null;
        }
    }
}
