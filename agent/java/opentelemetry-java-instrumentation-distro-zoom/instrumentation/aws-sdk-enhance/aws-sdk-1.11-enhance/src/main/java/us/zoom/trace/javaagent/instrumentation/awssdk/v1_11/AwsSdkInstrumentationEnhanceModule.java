/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.awssdk.v1_11;

import com.google.auto.service.AutoService;
import io.opentelemetry.javaagent.extension.instrumentation.InstrumentationModule;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;

import java.util.List;

import static java.util.Collections.singletonList;

@AutoService(InstrumentationModule.class)
public class AwsSdkInstrumentationEnhanceModule extends InstrumentationModule {
    public AwsSdkInstrumentationEnhanceModule() {
        super("aws-sdk-enhance", "aws-sdk-1.11-enhance");
    }

    @Override
    public List<TypeInstrumentation> typeInstrumentations() {
        return singletonList(new AwsClientEnhanceInstrumentation());
    }

    @Override
    public boolean isHelperClass(String className) {
        return className.startsWith("us.zoom.trace.javaagent.common") ||
                className.startsWith("us.zoom.trace.javaagent.instrumentation.awssdk.v1_11.library");
    }
}
