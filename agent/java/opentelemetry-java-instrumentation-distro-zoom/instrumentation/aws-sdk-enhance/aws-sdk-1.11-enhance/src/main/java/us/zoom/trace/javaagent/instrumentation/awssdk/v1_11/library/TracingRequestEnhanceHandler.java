/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.awssdk.v1_11.library;

import com.amazonaws.AmazonWebServiceRequest;
import com.amazonaws.Request;
import com.amazonaws.handlers.RequestHandler2;
import io.opentelemetry.api.trace.Span;

import java.util.List;

import static us.zoom.trace.javaagent.common.AttributeKeys.DYNAMODB_TABLE_NAMES;

/**
 * A {@link RequestHandler2} for use in the agent. Unlike library instrumentation, the agent will
 * also instrument the underlying HTTP client, and we must set the context as current to be able to
 * suppress it. Also unlike library instrumentation, we are able to instrument the SDK's internal
 * classes to handle buggy behavior related to exceptions that can cause scopes to never be closed
 * otherwise which would be disastrous. We hope there won't be anymore significant changes to this
 * legacy SDK that would cause these workarounds to break in the future.
 */
// NB: If the error-handling workarounds stop working, we should consider introducing the same
// x-amzn-request-id header check in Apache instrumentation for suppressing spans that we have in
// Netty instrumentation.
public class TracingRequestEnhanceHandler extends RequestHandler2 {

    @Override
    public void beforeRequest(Request<?> request) {
        Span currentSpan = Span.current();
        // it if currentSpan is null, just skip
        if (currentSpan == null) {
            return;
        }
        AmazonWebServiceRequest originalRequest = request.getOriginalRequest();
        List<String> tableNames = RequestAccess.getTableNames(originalRequest);
        currentSpan.setAttribute(DYNAMODB_TABLE_NAMES, tableNames);
    }
}
