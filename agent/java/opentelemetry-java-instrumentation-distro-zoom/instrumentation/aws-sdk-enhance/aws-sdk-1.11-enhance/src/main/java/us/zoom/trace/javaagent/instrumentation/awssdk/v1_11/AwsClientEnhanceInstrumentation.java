/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.awssdk.v1_11;

import static net.bytebuddy.matcher.ElementMatchers.declaresField;
import static net.bytebuddy.matcher.ElementMatchers.isConstructor;
import static net.bytebuddy.matcher.ElementMatchers.named;

import com.amazonaws.handlers.RequestHandler2;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;

import java.util.List;

import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;
import us.zoom.trace.javaagent.instrumentation.awssdk.v1_11.library.TracingRequestEnhanceHandler;

/**
 * This instrumentation might work with versions before 1.11.0, but this was the first version that
 * is tested. It could possibly be extended earlier.
 */
public class AwsClientEnhanceInstrumentation implements TypeInstrumentation {

    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return named("com.amazonaws.AmazonWebServiceClient")
                .and(declaresField(named("requestHandler2s")));
    }

    @Override
    public void transform(TypeTransformer transformer) {
        transformer.applyAdviceToMethod(
                isConstructor(), AwsClientEnhanceInstrumentation.class.getName() + "$AwsClientAdvice");
    }

    @SuppressWarnings("unused")
    public static class AwsClientAdvice {

        // Since we're instrumenting the constructor, we can't add onThrowable.
        @Advice.OnMethodExit(suppress = Throwable.class)
        public static void addHandler(
                @Advice.FieldValue("requestHandler2s") List<RequestHandler2> handlers) {
            boolean hasAgentHandler = false;
            for (RequestHandler2 handler : handlers) {
                if (handler instanceof TracingRequestEnhanceHandler) {
                    hasAgentHandler = true;
                    break;
                }
            }
            if (!hasAgentHandler) {
                handlers.add(new TracingRequestEnhanceHandler());
            }
        }
    }
}
