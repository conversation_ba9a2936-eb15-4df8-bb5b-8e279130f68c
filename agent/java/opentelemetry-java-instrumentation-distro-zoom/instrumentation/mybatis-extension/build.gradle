apply from: "$rootDir/gradle/instrumentation.gradle"


muzzle {
  pass {
    group.set("org.mybatis")
    module.set("mybatis")
    versions.set("[3.2.0,)")
    assertInverse.set(true)
  }

}


dependencies {
  compileOnly project(":bootstrap")
  compileOnly "io.opentelemetry.instrumentation:opentelemetry-instrumentation-api-incubator"
  compileOnly "org.mybatis:mybatis:3.2.0"
  implementation project(":common")
}
