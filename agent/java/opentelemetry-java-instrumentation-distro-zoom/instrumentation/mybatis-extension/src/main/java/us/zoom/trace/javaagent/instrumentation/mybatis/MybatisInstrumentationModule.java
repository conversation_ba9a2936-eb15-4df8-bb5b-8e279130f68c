/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.mybatis;

import com.google.auto.service.AutoService;
import io.opentelemetry.javaagent.extension.instrumentation.InstrumentationModule;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.sdk.autoconfigure.spi.ConfigProperties;

import java.util.List;

import static java.util.Arrays.asList;

@AutoService(InstrumentationModule.class)
public class MybatisInstrumentationModule extends InstrumentationModule {

    public MybatisInstrumentationModule() {
        super("mybatis-extension", "mybatis-3.2-extension");
    }

    @Override
    public List<TypeInstrumentation> typeInstrumentations() {
        return asList(new MapperMethodInstrumentation(), new SqlCommandInstrumentation());
    }

    @Override
    public boolean isHelperClass(String className) {
        return className.startsWith("us.zoom.trace.javaagent.common") || className.startsWith("io.opentelemetry.javaagent.extension") ||
                className.startsWith("us.zoom.trace.javaagent.instrumentation.mybatis");
    }
}
