/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.mybatis;

import io.opentelemetry.instrumentation.api.incubator.semconv.util.ClassAndMethod;
import io.opentelemetry.instrumentation.api.util.VirtualField;
import org.apache.ibatis.binding.MapperMethod.SqlCommand;
import org.apache.ibatis.mapping.MappedStatement;

import java.lang.reflect.Method;

public final class SqlCommandUtil {
    private static final VirtualField<SqlCommand, MybatisRequest> field =
            VirtualField.find(SqlCommand.class, MybatisRequest.class);

    public static void setClassAndMethod(SqlCommand command, Class<?> clazz, Method method) {
        if (clazz == null || method == null || method.getName() == null) {
            return;
        }
        MybatisRequest mybatisRequest = field.get(command);
        if (mybatisRequest == null) {
            mybatisRequest = new MybatisRequest();
            field.set(command, mybatisRequest);
        }
        ClassAndMethod classAndMethod = ClassAndMethod.create(clazz, method.getName());
        mybatisRequest.setClassAndMethod(classAndMethod);
    }

    public static void setMappedStatement(SqlCommand command, MappedStatement ms) {
        if (ms == null) {
            return;
        }
        MybatisRequest mybatisRequest = field.get(command);
        if (mybatisRequest == null) {
            mybatisRequest = new MybatisRequest();
            field.set(command, mybatisRequest);
        }
        mybatisRequest.setMappedStatement(ms);
    }

    public static MybatisRequest getClassAndMethod(SqlCommand command) {
        return field.get(command);
    }

    private SqlCommandUtil() {
    }
}
