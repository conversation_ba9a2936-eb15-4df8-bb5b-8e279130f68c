/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.mybatis;

import javax.annotation.Nullable;

/**
 * This class is internal and is hence not for public use. Its APIs are unstable and can change at
 * any time.
 */
public class DbInfo {
    public static final DbInfo DEFAULT = builder().build();

    private final String system;
    private final String subtype;
    private final String shortUrl;
    private final String user;
    private final String name;
    private final String db;
    private final String host;
    private final Integer port;

    private DbInfo(Builder builder) {
        this.system = builder.system;
        this.subtype = builder.subtype;
        this.shortUrl = builder.shortUrl;
        this.user = builder.user;
        this.name = builder.name;
        this.db = builder.db;
        this.host = builder.host;
        this.port = builder.port;
    }

    public static DbInfo.Builder builder() {
        return new Builder();
    }

    @Nullable
    public String getSystem() {
        return system;
    }

    @Nullable
    public String getSubtype() {
        return subtype;
    }

    // "type:[subtype:]//host:port"
    @Nullable
    public String getShortUrl() {
        return shortUrl;
    }

    @Nullable
    public String getUser() {
        return user;
    }

    @Nullable
    public String getName() {
        return name;
    }

    @Nullable
    public String getDb() {
        return db;
    }

    @Nullable
    public String getHost() {
        return host;
    }

    @Nullable
    public Integer getPort() {
        return port;
    }

    public Builder toBuilder() {
        return builder()
                .system(getSystem())
                .subtype(getSubtype())
                .shortUrl(getShortUrl())
                .user(getUser())
                .name(getName())
                .db(getDb())
                .host(getHost())
                .port(getPort());
    }

    @Override
    public String toString() {
        return "DbInfo{" +
                "system='" + system + '\'' +
                ", subtype='" + subtype + '\'' +
                ", shortUrl='" + shortUrl + '\'' +
                ", user='" + user + '\'' +
                ", name='" + name + '\'' +
                ", db='" + db + '\'' +
                ", host='" + host + '\'' +
                ", port=" + port +
                '}';
    }

    /**
     * This class is internal and is hence not for public use. Its APIs are unstable and can change at
     * any time.
     */
    public static class Builder {
        private String system;
        private String subtype;
        private String shortUrl;
        private String user;
        private String name;
        private String db;
        private String host;
        private Integer port;

        public Builder system(String system) {
            this.system = system;
            return this;
        }

        public Builder subtype(String subtype) {
            this.subtype = subtype;
            return this;
        }

        public Builder shortUrl(String shortUrl) {
            this.shortUrl = shortUrl;
            return this;
        }

        public Builder user(String user) {
            this.user = user;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder db(String db) {
            this.db = db;
            return this;
        }

        public Builder host(String host) {
            this.host = host;
            return this;
        }

        public Builder port(Integer port) {
            this.port = port;
            return this;
        }

        public DbInfo build() {
            return new DbInfo(this);
        }
    }
}
