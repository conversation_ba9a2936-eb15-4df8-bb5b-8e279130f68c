/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.mybatis;

import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.ibatis.binding.MapperMethod.SqlCommand;
import org.apache.ibatis.mapping.MappedStatement;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;

import java.lang.reflect.Method;

import static net.bytebuddy.matcher.ElementMatchers.*;

public class SqlCommandInstrumentation implements TypeInstrumentation {
    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return named("org.apache.ibatis.binding.MapperMethod$SqlCommand");
    }

    @Override
    public void transform(TypeTransformer transformer) {
        transformer.applyAdviceToMethod(
                named("resolveMappedStatement"), SqlCommandInstrumentation.class.getName() + "$ResolveMappedStatementAdvice");
        transformer.applyAdviceToMethod(
                isConstructor().and(takesArgument(1, Class.class)).and(takesArgument(2, Method.class)),
                SqlCommandInstrumentation.class.getName() + "$ConstructorAdvice");
    }


    @SuppressWarnings("unused")
    public static class ResolveMappedStatementAdvice {

        @Advice.OnMethodExit(suppress = Throwable.class)
        public static void onExit(
                @Advice.This SqlCommand command,
                @Advice.Return MappedStatement mappedStatement) {
            if (!AgentConfiguration.globalConfiguration().isEnableMybatisEnhancement()) {
                return;
            }
            SqlCommandUtil.setMappedStatement(command, mappedStatement);
        }
    }

    @SuppressWarnings("unused")
    public static class ConstructorAdvice {

        @Advice.OnMethodExit(suppress = Throwable.class)
        public static void onExit(
                @Advice.This SqlCommand command,
                @Advice.Argument(1) Class<?> mapperInterface,
                @Advice.Argument(2) Method method) {
            if (!AgentConfiguration.globalConfiguration().isEnableMybatisEnhancement()) {
                return;
            }
            SqlCommandUtil.setClassAndMethod(command, mapperInterface, method);
        }
    }
}
