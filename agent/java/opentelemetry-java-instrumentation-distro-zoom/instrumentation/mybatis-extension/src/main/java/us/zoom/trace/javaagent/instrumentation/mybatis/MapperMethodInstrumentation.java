/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.mybatis;

import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.binding.MapperMethod.SqlCommand;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.session.SqlSession;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static io.opentelemetry.javaagent.bootstrap.Java8BytecodeBridge.currentContext;
import static net.bytebuddy.matcher.ElementMatchers.named;
import static us.zoom.trace.javaagent.instrumentation.mybatis.MybatisSingletons.instrumenter;

public class MapperMethodInstrumentation implements TypeInstrumentation {

    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return named("org.apache.ibatis.binding.MapperMethod");
    }

    @Override
    public void transform(TypeTransformer transformer) {

        transformer.applyAdviceToMethod(
                named("execute"), MapperMethodInstrumentation.class.getName() + "$ExecuteAdvice");
    }

    @SuppressWarnings("unused")
    public static class ExecuteAdvice {

        @Advice.OnMethodEnter(suppress = Throwable.class)
        public static void getMapperInfo(
                @Advice.Argument(1) Object[] args,
                @Advice.Argument(0) SqlSession sqlSession,
                @Advice.FieldValue("command") SqlCommand command,
                @Advice.FieldValue("method") MapperMethod.MethodSignature method,
                @Advice.Local("otelRequest") MybatisRequest request,
                @Advice.Local("otelContext") Context context,
                @Advice.Local("otelScope") Scope scope) {
            if (!AgentConfiguration.globalConfiguration().isEnableMybatisEnhancement()) {
                return;
            }
            if (command == null) {
                return;
            }
            request = SqlCommandUtil.getClassAndMethod(command);
            if (request == null) {
                return;
            }
            Context parentContext = currentContext();
            if (!instrumenter().shouldStart(parentContext, request)) {
                return;
            }
            if (AgentConfiguration.globalConfiguration().isMybatisEnhancementAsClient()) {
                try {
                    DbInfo dbInfo = JdbcUtils.extractDbInfo(sqlSession.getConnection());
                    Object parameterObject = method.convertArgsToSqlCommandParam(args);
                    request.setDbInfo(dbInfo);
                    int paramSize = 0;
                    if (parameterObject instanceof MapperMethod.ParamMap) {
                        MapperMethod.ParamMap<Object> paramMap = (MapperMethod.ParamMap<Object>) parameterObject;
                        MapperMethod.ParamMap<Object> copyMap = new MapperMethod.ParamMap<>();

                        for (Map.Entry<String, Object> stringObjectEntry : paramMap.entrySet()) {
                            String key = stringObjectEntry.getKey();
                            Object value = stringObjectEntry.getValue();
                            if ((value instanceof Collection) && !((Collection) value).isEmpty()) {
                                paramSize = ((Collection) value).size();
                                Object firstElement = ((Collection) value).stream().findFirst().get();
                                List singletonList = new ArrayList<>();
                                singletonList.add(firstElement);
                                copyMap.put(key, singletonList);
                            } else {
                                copyMap.put(key, value);
                            }
                        }
                        BoundSql cleanedBoundSql = request.getMappedStatement().getBoundSql(copyMap);
                        request.setSql(cleanedBoundSql.getSql());
                        request.setParamSize(paramSize);
                    } else if (parameterObject instanceof Collection) {
                        Collection collection = (Collection) parameterObject;
                        paramSize =  collection.size();
                        Object first = collection.stream().findFirst().orElse(null);
                        if (first != null) {
                            collection = new ArrayList(){{add(first);}};
                            BoundSql cleanedBoundSql = request.getMappedStatement().getBoundSql(collection);
                            request.setSql(cleanedBoundSql.getSql());
                        }
                        request.setParamSize(paramSize);
                    } else {
                        BoundSql cleanedBoundSql = request.getMappedStatement().getBoundSql(parameterObject);
                        request.setSql(cleanedBoundSql.getSql());
                    }
                } catch (Throwable throwable) {
                    //ignore
                }
            }
            context = instrumenter().start(parentContext, request);
            scope = context.makeCurrent();
        }

        @Advice.OnMethodExit(onThrowable = Throwable.class, suppress = Throwable.class)
        public static void stopSpan(
                @Advice.Thrown Throwable throwable,
                @Advice.FieldValue("command") SqlCommand command,
                @Advice.Return Object result,
                @Advice.Local("otelRequest") MybatisRequest request,
                @Advice.Local("otelContext") Context context,
                @Advice.Local("otelScope") Scope scope) {
            if (scope != null) {
                scope.close();
                ResultWrapper resultWrapper = new ResultWrapper();
                resultWrapper.setResult(result);
                resultWrapper.setSqlCommandType(command.getType());
                instrumenter().end(context, request, resultWrapper, throwable);
            }
        }
    }
}
