/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.mybatis;

import io.opentelemetry.instrumentation.api.semconv.network.ServerAttributesGetter;

import javax.annotation.Nullable;
import java.util.Optional;

/**
 * This class is internal and is hence not for public use. Its APIs are unstable and can change at
 * any time.
 */
public final class JdbcNetworkAttributesGetter implements ServerAttributesGetter<MybatisRequest> {

    @Nullable
    @Override
    public String getServerAddress(MybatisRequest request) {
        return Optional.ofNullable(request.getDbInfo()).map(DbInfo::getHost).orElse(null);
    }

    @Nullable
    @Override
    public Integer getServerPort(MybatisRequest request) {
        return Optional.ofNullable(request.getDbInfo()).map(DbInfo::getPort).orElse(null);
    }
}
