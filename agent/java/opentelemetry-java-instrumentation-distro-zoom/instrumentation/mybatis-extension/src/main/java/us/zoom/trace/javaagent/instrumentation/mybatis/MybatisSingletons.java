/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.mybatis;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.common.AttributesBuilder;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.context.Context;
import io.opentelemetry.instrumentation.api.incubator.semconv.code.CodeAttributesExtractor;
import io.opentelemetry.instrumentation.api.incubator.semconv.code.CodeAttributesGetter;
import io.opentelemetry.instrumentation.api.incubator.semconv.code.CodeSpanNameExtractor;
import io.opentelemetry.instrumentation.api.incubator.semconv.db.SqlStatementInfo;
import io.opentelemetry.instrumentation.api.incubator.semconv.db.SqlStatementSanitizer;
import io.opentelemetry.instrumentation.api.instrumenter.AttributesExtractor;
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter;
import io.opentelemetry.instrumentation.api.semconv.network.ServerAttributesExtractor;
import org.apache.ibatis.mapping.SqlCommandType;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;
import us.zoom.trace.javaagent.common.AttributeKeys;

import javax.annotation.Nullable;
import java.util.Collection;

public final class MybatisSingletons {
    private static final String INSTRUMENTATION_NAME = "io.opentelemetry.mybatis-3.2-extension";
    private static final Instrumenter<MybatisRequest, ResultWrapper> INSTRUMENTER;
    private static final SqlStatementSanitizer sanitizer = SqlStatementSanitizer.create(true);
    private static final int DEFAULT_SIZE = 1;

    static {
        CodeAttributesGetter<MybatisRequest> codeAttributesGetter =
                ClassAndMethodAttributesGetter.INSTANCE;
        MybatisAttributesGetter mybatisAttributesGetter = new MybatisAttributesGetter();
        INSTRUMENTER =
                Instrumenter.<MybatisRequest, ResultWrapper>builder(
                                GlobalOpenTelemetry.get(),
                                INSTRUMENTATION_NAME,
                                CodeSpanNameExtractor.create(codeAttributesGetter))
                        .addAttributesExtractor(ServerAttributesExtractor.create(new JdbcNetworkAttributesGetter()))
                        .addAttributesExtractor(CodeAttributesExtractor.create(codeAttributesGetter))
                        .addAttributesExtractor(new AttributesExtractor<MybatisRequest, ResultWrapper>() {
                            @Override
                            public void onStart(AttributesBuilder attributes, Context parentContext, MybatisRequest request) {
                                //do nothing
                            }

                            @Override
                            public void onEnd(AttributesBuilder attributes, Context context, MybatisRequest request, @Nullable ResultWrapper resultWrapper, @Nullable Throwable error) {

                                if (resultWrapper == null || resultWrapper.getResult() == null) {
                                    return;
                                }
                                Object result = resultWrapper.getResult();
                                SqlCommandType sqlCommandType = resultWrapper.getSqlCommandType();
                                switch (sqlCommandType) {
                                    case SELECT:
                                        if (result instanceof Collection<?>) {
                                            attributes.put(AttributeKeys.DB_RETURNED_ROWS_SIZE, ((Collection<?>) result).size());
                                        } else {
                                            attributes.put(AttributeKeys.DB_RETURNED_ROWS_SIZE, DEFAULT_SIZE);
                                        }
                                        break;
                                    default:
                                        if (result instanceof Number) {
                                            attributes.put(AttributeKeys.DB_AFFECT_ROWS_SIZE, ((Number) result).longValue());
                                        }
                                        break;
                                }
                                if (request.getSql() != null) {
                                    SqlStatementInfo sanitize = sanitizer.sanitize(request.getSql());
                                    if (sanitize.getFullStatement() != null) {
                                        attributes.put(AttributeKeys.DB_STATEMENT, sanitize.getFullStatement());
                                    }
                                    if (sanitize.getMainIdentifier() != null) {
                                        attributes.put(AttributeKeys.DB_SQL_TABLE, sanitize.getMainIdentifier());
                                    }
                                }
                                if (AgentConfiguration.globalConfiguration().isMybatisEnhancementAsClient()) {
                                    attributes.put(AttributeKeys.DB_OPERATION, sqlCommandType.name());
                                }
                                String dbSystem = mybatisAttributesGetter.getDbSystem(request);
                                if (dbSystem != null) {
                                    attributes.put(AttributeKeys.DB_SYSTEM_NAME, dbSystem);
                                }
                                String dbNamespace = mybatisAttributesGetter.getDbNamespace(request);
                                if (dbNamespace != null) {
                                    attributes.put(AttributeKeys.DB_NAME, dbNamespace);
                                }
                                if (request.getParamSize() != null) {
                                    attributes.put(AttributeKeys.DB_OPERATION_BATCH_SIZE, request.getParamSize());
                                }
                            }
                        })
                        .buildInstrumenter(mybatisRequest -> AgentConfiguration.globalConfiguration().isMybatisEnhancementAsClient() ? SpanKind.CLIENT :
                                SpanKind.INTERNAL);
    }

    public static Instrumenter<MybatisRequest, ResultWrapper> instrumenter() {
        return INSTRUMENTER;
    }

    private MybatisSingletons() {
    }
}
