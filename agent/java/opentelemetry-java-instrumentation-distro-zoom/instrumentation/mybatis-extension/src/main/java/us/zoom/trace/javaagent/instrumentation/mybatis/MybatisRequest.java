package us.zoom.trace.javaagent.instrumentation.mybatis;

import io.opentelemetry.instrumentation.api.incubator.semconv.util.ClassAndMethod;
import org.apache.ibatis.mapping.MappedStatement;

public class MybatisRequest {
    private MappedStatement mappedStatement;
    private String sql;
    private Integer paramSize;
    private ClassAndMethod classAndMethod;
    private DbInfo dbInfo;

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public MappedStatement getMappedStatement() {
        return mappedStatement;
    }

    public void setMappedStatement(MappedStatement mappedStatement) {
        this.mappedStatement = mappedStatement;
    }

    public ClassAndMethod getClassAndMethod() {
        return classAndMethod;
    }

    public void setClassAndMethod(ClassAndMethod classAndMethod) {
        this.classAndMethod = classAndMethod;
    }

    public Integer getParamSize() {
        return paramSize;
    }

    public void setParamSize(Integer paramSize) {
        this.paramSize = paramSize;
    }

    public DbInfo getDbInfo() {
        return dbInfo;
    }

    public void setDbInfo(DbInfo dbInfo) {
        this.dbInfo = dbInfo;
    }
}
