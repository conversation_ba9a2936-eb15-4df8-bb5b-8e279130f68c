package us.zoom.trace.javaagent.instrumentation.mybatis;

import org.apache.ibatis.mapping.SqlCommandType;

public class ResultWrapper {
    private SqlCommandType sqlCommandType;
    private Object result;

    public SqlCommandType getSqlCommandType() {
        return sqlCommandType;
    }

    public void setSqlCommandType(SqlCommandType sqlCommandType) {
        this.sqlCommandType = sqlCommandType;
    }

    public Object getResult() {
        return result;
    }

    public void setResult(Object result) {
        this.result = result;
    }
}
