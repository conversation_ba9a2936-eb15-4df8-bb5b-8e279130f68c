/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.mybatis;

import io.opentelemetry.instrumentation.api.incubator.semconv.code.CodeAttributesGetter;
import io.opentelemetry.instrumentation.api.incubator.semconv.util.ClassAndMethod;

import javax.annotation.Nullable;
import java.util.Optional;

enum ClassAndMethodAttributesGetter implements CodeAttributesGetter<MybatisRequest> {
    INSTANCE;

    @Nullable
    @Override
    public Class<?> getCodeClass(MybatisRequest request) {
        return Optional.ofNullable(request.getClassAndMethod()).map(ClassAndMethod::declaringClass).orElse(null);
    }

    @Nullable
    @Override
    public String getMethodName(MybatisRequest request) {
        return Optional.ofNullable(request.getClassAndMethod()).map(ClassAndMethod::methodName).orElse(null);
    }
}
