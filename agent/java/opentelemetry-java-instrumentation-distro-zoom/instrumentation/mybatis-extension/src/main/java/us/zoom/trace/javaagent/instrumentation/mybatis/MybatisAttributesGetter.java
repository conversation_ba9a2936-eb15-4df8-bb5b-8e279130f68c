/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.mybatis;

import io.opentelemetry.instrumentation.api.incubator.semconv.db.SqlClientAttributesGetter;

import javax.annotation.Nullable;
import java.util.Optional;

/**
 * This class is internal and is hence not for public use. Its APIs are unstable and can change at
 * any time.
 */
public final class MybatisAttributesGetter implements SqlClientAttributesGetter<MybatisRequest> {

    @Nullable
    @Override
    public String getDbSystem(MybatisRequest request) {
        return Optional.ofNullable(request.getDbInfo()).map(DbInfo::getSystem).orElse(null);
    }

    @Deprecated
    @Nullable
    @Override
    public String getUser(MybatisRequest request) {
        return Optional.ofNullable(request.getDbInfo()).map(DbInfo::getUser).orElse(null);
    }

    @Nullable
    @Override
    public String getDbNamespace(MybatisRequest request) {
        DbInfo dbInfo = request.getDbInfo();
        if (dbInfo == null) {
            return null;
        }
        return dbInfo.getName() == null ? dbInfo.getDb() : dbInfo.getName();
    }

    @Deprecated
    @Nullable
    @Override
    public String getConnectionString(MybatisRequest request) {
        return Optional.ofNullable(request.getDbInfo()).map(DbInfo::getShortUrl).orElse(null);
    }

}
