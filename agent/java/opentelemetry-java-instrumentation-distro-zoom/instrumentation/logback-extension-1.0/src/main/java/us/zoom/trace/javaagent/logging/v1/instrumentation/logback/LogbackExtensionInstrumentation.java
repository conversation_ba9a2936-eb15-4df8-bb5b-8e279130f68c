/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.logging.v1.instrumentation.logback;

import static net.bytebuddy.matcher.ElementMatchers.isMethod;
import static net.bytebuddy.matcher.ElementMatchers.isPublic;
import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.takesArgument;
import static net.bytebuddy.matcher.ElementMatchers.takesArguments;
import static us.zoom.trace.javaagent.logging.v1.instrumentation.logback.LogbackSingletons.mapper;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.logs.LoggerProvider;
import io.opentelemetry.javaagent.bootstrap.CallDepth;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;

public class LogbackExtensionInstrumentation implements TypeInstrumentation {

    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return named("ch.qos.logback.classic.Logger");
    }

    @Override
    public void transform(TypeTransformer transformer) {
        transformer.applyAdviceToMethod(
                isMethod()
                        .and(isPublic())
                        .and(named("callAppenders"))
                        .and(takesArguments(1))
                        .and(takesArgument(0, named("ch.qos.logback.classic.spi.ILoggingEvent"))),
                LogbackExtensionInstrumentation.class.getName() + "$CallAppendersAdvice");
    }

    @SuppressWarnings("unused")
    public static class CallAppendersAdvice {

        @Advice.OnMethodEnter(suppress = Throwable.class)
        public static void methodEnter(
                @Advice.Argument(0) ILoggingEvent event,
                @Advice.This Logger logger,
                @Advice.Local("otelCallDepth") CallDepth callDepth) {
            // need to track call depth across all loggers in order to avoid double capture when one
            // logging framework delegates to another
            callDepth = CallDepth.forClass(LoggerProvider.class);
            if (callDepth.getAndIncrement() == 0) {
                mapper().emit(GlobalOpenTelemetry.get().getLogsBridge(), event, logger, Thread.currentThread().getId());
            }
        }

        @Advice.OnMethodExit(onThrowable = Throwable.class, suppress = Throwable.class)
        public static void methodExit(@Advice.Local("otelCallDepth") CallDepth callDepth) {
            callDepth.decrementAndGet();
        }
    }
}
