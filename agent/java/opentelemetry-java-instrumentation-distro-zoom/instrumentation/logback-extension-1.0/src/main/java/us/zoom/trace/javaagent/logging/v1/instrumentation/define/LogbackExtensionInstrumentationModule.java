/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.logging.v1.instrumentation.define;

import com.google.auto.service.AutoService;
import io.opentelemetry.javaagent.extension.instrumentation.InstrumentationModule;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import net.bytebuddy.matcher.ElementMatcher;
import us.zoom.trace.javaagent.logging.v1.instrumentation.logback.LogbackExtensionInstrumentation;

import java.util.ArrayList;
import java.util.List;

import static io.opentelemetry.javaagent.extension.matcher.AgentElementMatchers.hasClassesNamed;

/**
 * for asyncmq
 */
@AutoService(InstrumentationModule.class)
public final class LogbackExtensionInstrumentationModule extends InstrumentationModule {

    public LogbackExtensionInstrumentationModule() {
        super("logback-extension", "logback-extension-1.0");
    }

    @Override
    public List<TypeInstrumentation> typeInstrumentations() {
        return new ArrayList<>() {{
            add(new LogbackExtensionInstrumentation());
        }};
    }

    @Override
    public ElementMatcher.Junction<ClassLoader> classLoaderMatcher() {
        return hasClassesNamed("ch.qos.logback.classic.Logger");
    }

    @Override
    public boolean isHelperClass(String className) {
        return className.startsWith("us.zoom.trace.javaagent.common") || className.startsWith("io.opentelemetry.javaagent.extension") ||
                className.startsWith("us.zoom.trace.javaagent.logging.v1.instrumentation");
    }
}
