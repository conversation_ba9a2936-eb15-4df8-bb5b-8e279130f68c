/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package org.springframework.web.servlet.v3_1;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanContext;
import io.opentelemetry.context.Context;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import us.zoom.trace.javaagent.bootstrap.AgentConfiguration;
import us.zoom.trace.javaagent.common.ZoomTrackingContext;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static us.zoom.trace.javaagent.common.AttributeKeys.SHORT_KEY_TRACKING_ID;
import static us.zoom.trace.javaagent.common.AttributeKeys.ZM_TRACKING_ID;

public class ZoomTraceFilter implements Filter, Ordered {

    private static final String TRACE_ID_KEY = "zm-cube-traceId";

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        Span span = Span.current();
        try {
            if (AgentConfiguration.globalConfiguration().replaceTrackingIdEnabled()) {
                String trackingId = MDC.get(ZM_TRACKING_ID.getKey());
                if (trackingId != null && !trackingId.isEmpty()) {
                    span.setAttribute(SHORT_KEY_TRACKING_ID.getKey(), ZoomTrackingContext.fromField(trackingId).getTrackingId());
                    //update context
                    ZoomTrackingContext zoomTrackingContext = Context.current().get(ZoomTrackingContext.CONTEXT_KEY);
                    if (zoomTrackingContext != null) {
                        zoomTrackingContext.setTrackingId(trackingId);
                    }
                }
            }
            if (response instanceof HttpServletResponse) {
                HttpServletResponse httpResponse = (HttpServletResponse) response;
                if (!httpResponse.isCommitted()) {
                    SpanContext context = span.getSpanContext();
                    if (context != null) {
                        //prevent override user's traceId
                        httpResponse.setHeader(TRACE_ID_KEY, context.getTraceId());
                    }
                }
            }
        } catch (Throwable e) {
            // ignore
        }
        filterChain.doFilter(request, response);
    }

    @Override
    public void destroy() {
    }


    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
