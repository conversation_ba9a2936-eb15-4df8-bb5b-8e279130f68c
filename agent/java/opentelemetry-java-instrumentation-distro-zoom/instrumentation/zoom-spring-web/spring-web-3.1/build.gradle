apply from: "$rootDir/gradle/instrumentation.gradle"

muzzle {
    pass {
        group.set("org.springframework")
        module.set("spring-web")
        versions.set("[3.1.0.RELEASE,6)")
        // these versions depend on javax.faces:jsf-api:1.1 which was released as pom only
        skip("1.2.1", "1.2.2", "1.2.3", "1.2.4")
        assertInverse.set(true)
    }
    pass {
        group.set("org.springframework")
        module.set("spring-webmvc")
        versions.set("[3.1.0.RELEASE,6)")
        skip("1.2.1", "1.2.2", "1.2.3", "1.2.4")
        skip("3.2.1.RELEASE")
        extraDependency("javax.servlet:javax.servlet-api:3.0.1")
        assertInverse.set(true)
    }
    pass {
        group.set("org.slf4j")
        module.set("slf4j-api")
        versions.set("[1.4.1,)")
        assertInverse.set(true)
    }
}

dependencies {
    implementation(project(":common"))
    compileOnly project(":bootstrap")
    compileOnly("org.springframework:spring-web:3.1.0.RELEASE")
    compileOnly("org.springframework:spring-webmvc:3.1.0.RELEASE")
    compileOnly("javax.servlet:javax.servlet-api:3.1.0")
    compileOnly("org.slf4j:slf4j-api:1.4.1")
}
