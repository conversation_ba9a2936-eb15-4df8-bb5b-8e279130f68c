/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.springweb.v6_0;

import com.google.auto.service.AutoService;
import io.opentelemetry.javaagent.extension.instrumentation.InstrumentationModule;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import net.bytebuddy.matcher.ElementMatcher;

import java.util.List;

import static io.opentelemetry.javaagent.extension.matcher.AgentElementMatchers.hasClassesNamed;
import static java.util.Collections.singletonList;

@AutoService(InstrumentationModule.class)
public class SpringWebInstrumentationModule extends InstrumentationModule {

    public SpringWebInstrumentationModule() {
        super("zoom-spring-web", "spring-web-6.0");
    }

    @Override
    public ElementMatcher.Junction<ClassLoader> classLoaderMatcher() {
        // class added in 6.0
        return hasClassesNamed("org.springframework.web.ErrorResponse")
                .and(hasClassesNamed("jakarta.servlet.Filter"));
    }

    @Override
    public boolean isHelperClass(String className) {
        return className.startsWith("org.springframework.web.servlet.v6_0.ZoomTraceFilter") ||
                className.startsWith("us.zoom.trace.javaagent.common");
    }

    @Override
    public List<TypeInstrumentation> typeInstrumentations() {
        return singletonList(new WebApplicationContextInstrumentation());
    }
}
