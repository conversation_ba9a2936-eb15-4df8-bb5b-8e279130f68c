/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.csms.v0_6_0;

import com.google.auto.service.AutoService;
import io.opentelemetry.javaagent.extension.instrumentation.InstrumentationModule;
import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import net.bytebuddy.matcher.ElementMatcher;

import java.util.List;

import static io.opentelemetry.javaagent.extension.matcher.AgentElementMatchers.hasClassesNamed;
import static java.util.Collections.singletonList;

@AutoService(InstrumentationModule.class)
public class CsmsSdkInstrumentationModule extends InstrumentationModule {

    public CsmsSdkInstrumentationModule() {
        super("csms-sdk", "csms-sdk-0.6.0");
    }

    @Override
    public ElementMatcher.Junction<ClassLoader> classLoaderMatcher() {
        // class added in 6.0
        return hasClassesNamed("us.zoom.cloud.secrets.provider.CSMSServiceProvider");
    }

    @Override
    public List<TypeInstrumentation> typeInstrumentations() {
        return singletonList(new CsmsSdkInstrumentation());
    }
}
