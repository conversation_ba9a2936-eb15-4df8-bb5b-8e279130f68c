/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package us.zoom.trace.javaagent.instrumentation.csms.v0_6_0;

import io.opentelemetry.javaagent.extension.instrumentation.TypeInstrumentation;
import io.opentelemetry.javaagent.extension.instrumentation.TypeTransformer;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.ElementMatcher;

import static io.opentelemetry.javaagent.extension.matcher.AgentElementMatchers.hasClassesNamed;
import static net.bytebuddy.matcher.ElementMatchers.named;

/**
 * <EMAIL>
 */
public class CsmsSdkInstrumentation implements TypeInstrumentation {

    private static ClassLoader userspaceClassLoader = null;

    @Override
    public ElementMatcher<ClassLoader> classLoaderOptimization() {
        return hasClassesNamed(
                "us.zoom.cloud.secrets.provider.CSMSServiceProvider");
    }

    @Override
    public ElementMatcher<TypeDescription> typeMatcher() {
        return named("us.zoom.cloud.secrets.provider.CSMSServiceProvider");
    }

    @Override
    public void transform(TypeTransformer transformer) {
        transformer.applyTransformer((builder, typeDescription, classLoader, javaModule, protectionDomain) -> {
            userspaceClassLoader = classLoader;
            return builder;
        });
    }

    public static ClassLoader getUserspaceClassLoader() {
        return userspaceClassLoader;
    }
}
