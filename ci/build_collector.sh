#!/usr/bin/env sh

set -e

export JAVA_HOME=${JAVA_HOME_17}
export PATH="${JAVA_HOME}/bin:$PATH"

# version.txt and create output directory
mkdir -p collector/src/main/resources/static/
mkdir -p output/report

if [ -f "version.txt" ]; then
  cp version.txt collector/src/main/resources/static/version.txt
fi

# build executable jar and move results into 'output'
mvn clean install -f ./collector/pom.xml -Dmaven.test.skip=true -ntp -Dartifactory.publish.artifacts=false
cp collector/target/cube-trace-collector.jar output/cube-trace-collector-"${VERSION}"."${BUILD_NUMBER}".jar

docker build -t cube-trace-collector -f collector/ci/Dockerfile .