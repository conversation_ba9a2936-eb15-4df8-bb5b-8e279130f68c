#!/usr/bin/env sh
set -e
export JAVA_HOME=${JAVA_HOME_17}
export PATH=${JAVA_HOME}/bin:$PATH

export GRADLE_HOME=${GRADLE_HOME_84}
gradle -v

CUR_DIR=$(pwd)
case "$SUB_MODULE_NAME" in
  "agent" )
    cd "$CUR_DIR"/agent/java/opentelemetry-java-instrumentation-distro-zoom
    if [ "$JDK_VERSION" = "8" ]; then
      # Commands for JDK 8
      gradle :agent:artifactoryPublish -PVERSION="$RELEASE_VERSION"_jdk_8 -PjavaVersion=8 -PdisableInstrumentations=asyncmq-2,spring-web-6.0,logback-extension -x test --no-scan
    else
      # Default commands
      gradle :agent:artifactoryPublish -PVERSION=$RELEASE_VERSION -x test --no-scan
    fi
    ;;
  *)
    echo "$SUB_MODULE_NAME is not in the list"
    ;;
esac

