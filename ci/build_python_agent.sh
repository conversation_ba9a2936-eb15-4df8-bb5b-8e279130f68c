#!/usr/bin/env sh

WORKDIR=$(pwd)
mkdir -p "${WORKDIR}"/output
## Building
## Moving package to output folder
echo "Packages's name needs follow zoom-<packageName> pattern,such as: zoom-xxx.whl ."
echo "Building ..."
pip install hatch
case "$SUB_MODULE_NAME" in
  "distro" )
    cd "$WORKDIR"/agent/python/opentelemetry-python-contrib/zoom-opentelemetry-distro
    hatch build
    mv dist/* "${WORKDIR}"/output
    ;;
  "asyncmq-plugin" )
    cd "$WORKDIR"/agent/python/opentelemetry-python-contrib/instrumentation/opentelemetry-instrumentation-asyncmq-python
    hatch build
    mv dist/* "${WORKDIR}"/output
    ;;
  "csms-plugin" )
    cd "$WORKDIR"/agent/python/opentelemetry-python-contrib/instrumentation/opentelemetry-instrumentation-csms-2.0-python
    hatch build
    mv dist/* "${WORKDIR}"/output
    cd "$WORKDIR"/agent/python/opentelemetry-python-contrib/instrumentation/opentelemetry-instrumentation-csms-3.0-python
    hatch build
    mv dist/* "${WORKDIR}"/output
    ;;
  *)
    echo "$SUB_MODULE_NAME is not in the list"
    exit 0
    ;;
esac