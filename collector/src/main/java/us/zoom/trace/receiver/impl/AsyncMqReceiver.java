package us.zoom.trace.receiver.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.KeyValue;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.ResourceSpans;
import jakarta.annotation.PostConstruct;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.mq.common.entity.TaskEntity;
import us.zoom.mq.common.util.GzipUtil;
import us.zoom.trace.asyncmq.HandleStatus;
import us.zoom.trace.common.Constants;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.SystemParamObserver;
import us.zoom.trace.common.TraceCommonAttributeKey;
import us.zoom.trace.common.config.TraceSystemParamCache;
import us.zoom.trace.common.model.PushGatewayPBMsg;
import us.zoom.trace.monitor.MetricReporterManager;
import us.zoom.trace.monitor.PayloadSizeMetric;
import us.zoom.trace.pipeline.PipelineManager;
import us.zoom.trace.receiver.Receiver;
import us.zoom.trace.util.DateUtils;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.LogRateLimiter;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static us.zoom.trace.common.Constants.RESOURCE_ATTRIBUTE_BLACK_LIST;
import static us.zoom.trace.pipeline.PipelineType.TRACE;
import static us.zoom.trace.receiver.ReceiverType.ASYNCMQ;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
@Component("asyncMqReceiver")
public class AsyncMqReceiver implements Receiver<TaskEntity<byte[]>, HandleStatus>, SystemParamObserver {

    private static final Logger logger = LoggerFactory.getLogger(AsyncMqReceiver.class);

    private static final String TRACE_DATA_KEY = "message";
    private static final String PROTO_KEY = "proto";
    private static final String ENCODING_KEY = "encoding";
    private static final String PROTOBUF = "protobuf";
    private static final String GZIP = "gzip";
    private static final String LABEL_LIST_KEY = "labelList";
    private static final String APP_CLUSTER_KEY = "appcluster";
    private static final String LOG_STREAM_KEY = "logstream";
    private static final String REGION_ID_KEY = "regionId";
    private static final int STANDARD_LOG_STREAM_SIZE = 4;
    @Autowired
    private PipelineManager pipelineManager;
    @Autowired
    private LogRateLimiter logRateLimiter;

    private static Map<String, String> serviceMap = Maps.newHashMap();

    @Value("${static.payload.size.enabled:false}")
    private boolean staticPayloadSize;

    @PostConstruct
    public void init() {
        TraceSystemParamCache.addObserver(this);
    }


    @Override
    public HandleStatus receive(TaskEntity<byte[]> taskEntity) {
        try {
            List<ResourceSpans> traceDataList = parseTraceMessage(taskEntity)
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            Context context = new Context(ASYNCMQ, TRACE, taskEntity.getTaskContext().getTopicName(), taskEntity.getTaskContext().getProduceTime());
            pipelineManager.process(context, traceDataList);
            if (staticPayloadSize) {
                long periodBeginTime = DateUtils.getPeriodBeginTime(taskEntity.getTaskContext().getProduceTime(), 30);
                PayloadSizeMetric build = PayloadSizeMetric.builder()
                        .periodTime(periodBeginTime)
                        .topicName(taskEntity.getTaskContext().getTopicName())
                        .sizeCounter(new AtomicLong())
                        .delaySeconds(30)
                        .payloadType(isJson(taskEntity.getPayload()) ? "json" : PROTOBUF)
                        .build();
                build = MetricReporterManager.registerDelayMetricIfAbsent(build);
                build.update(m -> {
                    PayloadSizeMetric payloadSizeMetric = (PayloadSizeMetric) m;
                    payloadSizeMetric.getSizeCounter().addAndGet(taskEntity.getPayload().length);
                });
            }
        } catch (Throwable e) {
            if (logRateLimiter.getPermitByAsyncMqContext(taskEntity.getTaskContext())) {
                logger.error("message process error", e);
            }
        }
        return HandleStatus.SUCCESS;
    }

    private static List<ResourceSpans> parseTraceMessage(TaskEntity<byte[]> taskEntity) throws IOException, DecoderException {
        Map<String, Object> extraInfo = Optional.ofNullable(taskEntity.getTaskContext().getExtraInfo()).orElse(Collections.emptyMap());
        byte[] payload = taskEntity.getPayload();
        String proto = (String) extraInfo.get(PROTO_KEY);
        if (StringUtils.equals(proto, PROTOBUF) || !isJson(payload)) {
            PushGatewayPBMsg.Builder pushGatewayPBMsg = PushGatewayPBMsg.newBuilder().mergeFrom(payload);
            String encoding = (String) extraInfo.get(ENCODING_KEY);
            byte[] data = pushGatewayPBMsg.getMsg().toByteArray();
            if (StringUtils.equals(encoding, GZIP)) {
                data = GzipUtil.unzip(data);
            }
            ExportTraceServiceRequest request;
            request = ExportTraceServiceRequest.newBuilder()
                    .mergeFrom(data).build();
            return request.getResourceSpansList().stream().map(resourceSpans -> {
                return rebuildResourceSpans(resourceSpans, pushGatewayPBMsg.getLabelListMap(), null, null);
            }).toList();
        } else {
            String message = new String(payload);
            List<ResourceSpans> deserializeMessage = Lists.newLinkedList();
            if (JsonUtils.isJsonObject(message)) {
                Map<String, Object> map = JsonUtils.parse(message, new TypeReference<>() {
                });
                deserializeMessage.add(buildSpans(map));
            } else if (JsonUtils.isJsonArray(message)) {
                List<Map<String, Object>> listMap = JsonUtils.parse(message, new TypeReference<>() {
                });
                for (Map<String, Object> map : listMap) {
                    deserializeMessage.add(buildSpans(map));
                }
            }
            return deserializeMessage;
        }
    }


    private static ResourceSpans buildSpans(Map<String, Object> messageMap) {
        String jsonData = (String) messageMap.get(TRACE_DATA_KEY);
        ResourceSpans resourceSpans = JsonUtils.toObject(jsonData, ResourceSpans.class);
        return rebuildResourceSpans(resourceSpans, (Map<String, String>) messageMap.get(LABEL_LIST_KEY),
                (String) messageMap.get(LOG_STREAM_KEY), (String) messageMap.get(APP_CLUSTER_KEY));
    }

    private static ResourceSpans rebuildResourceSpans(ResourceSpans resourceSpans,
                                                      Map<String, String> labelList, String logStream,
                                                      String appCluster) {
        ResourceSpans.Builder builder = resourceSpans.toBuilder();
        List<KeyValue> resourceAttributes = builder.getResource().getAttributesList();
        Resource.Builder resourceBuilder = builder.getResourceBuilder().clearAttributes();
        Map<String, KeyValue.Builder> map = Maps.newHashMap();
        for (int i = 0; i < resourceAttributes.size(); i++) {
            KeyValue keyValue = resourceAttributes.get(i);
            if (RESOURCE_ATTRIBUTE_BLACK_LIST.contains(keyValue.getKey())) {
                continue;
            }
            if (StringUtils.equals(keyValue.getKey(), TraceCommonAttributeKey.SERVICE_NAME.attributeName())) {
                String mappingService = serviceMap.get(keyValue.getValue().getStringValue());
                if (mappingService != null) {
                    resourceBuilder.addAttributes(KeyValue.newBuilder()
                            .setKey(TraceCommonAttributeKey.SERVICE_NAME.attributeName())
                            .setValue(AnyValue.newBuilder()
                                    .setStringValue(mappingService)));
                }
            }
            KeyValue.Builder keyValueBuilder = keyValue.toBuilder();
            resourceBuilder.addAttributes(keyValueBuilder);
            map.put(keyValue.getKey(), keyValueBuilder);
        }

        if (MapUtils.isNotEmpty(labelList)) {
            for (Map.Entry<String, String> entry : labelList.entrySet()) {
                KeyValue.Builder otelAttribute = map.get(entry.getKey());
                if (!hasAttribute(otelAttribute)) {
                    resourceBuilder.addAttributes(KeyValue.newBuilder()
                            .setKey(entry.getKey())
                            .setValue(AnyValue.newBuilder()
                                    .setStringValue(entry.getValue())));
                }
            }
        }

        if (logStream != null) {
            String[] split = logStream.split(Constants.COMMON_SEPARATOR);
            if (split.length == STANDARD_LOG_STREAM_SIZE) {
                KeyValue.Builder otelAttribute = map.get(REGION_ID_KEY);
                if (!hasAttribute(otelAttribute)) {
                    resourceBuilder.addAttributes(
                            KeyValue.newBuilder()
                                    .setKey(REGION_ID_KEY)
                                    .setValue(AnyValue.newBuilder()
                                            .setStringValue(split[STANDARD_LOG_STREAM_SIZE - 1]))
                    );
                }
            }
        }
        if (appCluster != null) {
            resourceBuilder.addAttributes(KeyValue.newBuilder()
                    .setKey(APP_CLUSTER_KEY)
                    .setValue(AnyValue.newBuilder()
                            .setStringValue(appCluster)));
        }
        return builder.build();
    }

    private static boolean hasAttribute(KeyValue.Builder attribute) {
        return attribute != null && attribute.hasValue();
    }

    private static boolean isJson(byte[] payload) {
        if (payload == null || payload.length == 0) {
            return false;
        }

        int i = 0;
        while (i < payload.length && isWhitespace(payload[i])) {
            i++;
        }
        if (i >= payload.length) {
            return false;
        }
        byte first = payload[i];
        return first == '[' || first == '{';
    }


    private static boolean isWhitespace(byte b) {
        return b == ' ' || b == '\n' || b == '\r' || b == '\t';
    }

    @Override
    public String getObservedKey() {
        return "service_mapping";
    }

    @Override
    public void onChange(String param) {
        serviceMap = JsonUtils.toObjectByTypeRef(param, new TypeReference<>() {
        });
    }
}