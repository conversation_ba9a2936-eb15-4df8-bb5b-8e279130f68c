package us.zoom.trace.biz.metric.meta.extractor;

import com.google.common.collect.Lists;
import io.opentelemetry.proto.trace.v1.Span;
import us.zoom.trace.common.BaseREDMetrics;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.TraceCommonAttributeKey;
import us.zoom.trace.exporter.clickhouse.model.SpanIndex;
import us.zoom.trace.util.DateUtils;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public abstract class MetricExtractor<T extends BaseREDMetrics> {

    private static final long DEFAULT_REPORT_DELAY_SECONDS = 60;

    private static final int DEFAULT_AGG_PERIOD_SECONDS = 15;

    public T extract(SpanIndex spanIndex, Context context) {
        T baseMetrics = doExtract(spanIndex);
        if (baseMetrics == null) {
            return null;
        }
        long periodTime = DateUtils.getPeriodBeginTime(TimeUnit.NANOSECONDS.toMillis(spanIndex.getStartTimeUnixNano()), DEFAULT_AGG_PERIOD_SECONDS);
        baseMetrics.setTs(periodTime);
        baseMetrics.setDelaySeconds(DEFAULT_REPORT_DELAY_SECONDS);
        baseMetrics.setDurationList(Lists.newCopyOnWriteArrayList());
        baseMetrics.setStatusList(Lists.newCopyOnWriteArrayList());
        baseMetrics.setServiceName(spanIndex.getServiceName());
        Map<String, String> stringTagMap = spanIndex.getStringTagMap();
        baseMetrics.setRegion(getRegion(stringTagMap));
        baseMetrics.setCluster(getCluster(stringTagMap));
        return baseMetrics;
    }

    protected abstract T doExtract(SpanIndex spanIndex);

    public abstract Set<Span.SpanKind> spanKindTypes();

    public abstract boolean shouldExtract(SpanIndex spanIndex);

    private String getRegion(Map<String, String> stringTagMap) {
        String region = stringTagMap.get(TraceCommonAttributeKey.REGION.attributeName());
        if (region == null) {
            region = stringTagMap.get(TraceCommonAttributeKey.REGION_ID.attributeName());
        }
        return region;
    }

    private String getCluster(Map<String, String> stringTagMap) {
        String cluster = stringTagMap.get(TraceCommonAttributeKey.CLUSTER.attributeName());
        if (cluster == null) {
            cluster = stringTagMap.get(TraceCommonAttributeKey.APP_CLUSTER.attributeName());
        }
        return cluster;
    }

}
