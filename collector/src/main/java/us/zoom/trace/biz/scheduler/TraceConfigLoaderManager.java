package us.zoom.trace.biz.scheduler;

import jakarta.annotation.PostConstruct;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.trace.util.DateUtils;
import us.zoom.trace.util.MonitorLogUtils;

import java.time.Instant;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static us.zoom.trace.common.Constants.CUBE_METRIC_LOG_VERSION;
import static us.zoom.trace.common.Constants.MEASURE_LOAD_TRACE_SYSTEM_CONFIG;
import static us.zoom.trace.util.ExceptionStackUtils.parseExceptionStackToString;

/**
 * @author: eason.jia
 * @date: 2024/8/5
 */
@Component
public class TraceConfigLoaderManager {

    private static final Logger logger = LoggerFactory.getLogger(TraceConfigLoaderManager.class);
    private static final String MONITOR_LOAD_SYSTEM_CONFIG_SUCCESS = "loadSystemConfigSuccess";
    private static final String MONITOR_LOAD_SYSTEM_CONFIG_FAIL = "loadSystemConfigFail";

    @Autowired
    private List<TraceConfigLoader> traceConfigLoaders;

    @PostConstruct
    public void init() {
        if (CollectionUtils.isNotEmpty(traceConfigLoaders)) {
            traceConfigLoaders.sort(Comparator.comparingInt(TraceConfigLoader::order));
        }
    }

    @Scheduled(timeUnit = TimeUnit.SECONDS, initialDelay = 0, fixedRate = 60)
    public void loadConfigs() {
        long start = System.currentTimeMillis();
        for (TraceConfigLoader traceConfigLoader : traceConfigLoaders) {
            String simpleName = traceConfigLoader.getClass().getSimpleName();
            try {
                traceConfigLoader.loadConfig();
                printConfigLoadMonitorLog(MONITOR_LOAD_SYSTEM_CONFIG_SUCCESS, simpleName, System.currentTimeMillis() - start);
            } catch (Throwable e) {
                logger.error("failed to load config, loaderName: {}", simpleName, e);
                printConfigLoadMonitorLog(MONITOR_LOAD_SYSTEM_CONFIG_FAIL, simpleName, System.currentTimeMillis() - start, e.getMessage(), parseExceptionStackToString(e));
            }
        }
    }

    private void printConfigLoadMonitorLog(String phase, String loaderName, long cost) {
        printConfigLoadMonitorLog(phase, loaderName, cost, "", "");
    }

    private void printConfigLoadMonitorLog(String phase, String loaderName, long cost, String message, String stack) {
        long now = Instant.now().toEpochMilli();
        MonitorLogUtils.print(MonitorLog.builder()
                .withCubeVer(CUBE_METRIC_LOG_VERSION)
                .withMeasure(MEASURE_LOAD_TRACE_SYSTEM_CONFIG)
                .withTs(now)
                .addTag("phase", phase)
                .addTag("loaderName", loaderName)
                .addField("cost", cost)
                .addField("msg", message)
                .addField("stack", stack)
                .addField("readableTs", DateUtils.format(new Date(now), DateUtils.FORMART11))
                .build());
    }
}
