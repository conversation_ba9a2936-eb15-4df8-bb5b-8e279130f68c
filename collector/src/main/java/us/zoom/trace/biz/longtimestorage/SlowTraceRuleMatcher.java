package us.zoom.trace.biz.longtimestorage;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.trace.v1.Span;
import jakarta.annotation.PostConstruct;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.SystemParamObserver;
import us.zoom.trace.common.config.TraceSystemParamCache;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.OtelUtils;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @author: eason.jia
 * @date: 2024/9/8
 */
@Order(2)
@Component("slowTraceRuleMatcher")
@ConditionalOnExpression("'${cube.trace.consume.delay.enable:false}'.equals('false')")
public class SlowTraceRuleMatcher implements LongTimeStorageRuleMatcher, SystemParamObserver {

    private static final Logger logger = LoggerFactory.getLogger(SlowTraceRuleMatcher.class);
    private static final String OBSERVED_SYSTEM_PARAM_KEY = "slowTraceRuleMatcher";
    private static final String HTTP_THRESHOLD = "http_threshold";
    private static final String MYSQL_THRESHOLD = "mysql_threshold";
    private static final String REDIS_THRESHOLD = "redis_threshold";

    private int httpThresholdMs = 3000;
    private int mysqlThresholdMs = 2000;
    private int redisThresholdMs = 50;

    @PostConstruct
    public void init() {
        TraceSystemParamCache.addObserver(this);
    }

    @Override
    public boolean isMatch(Context context, Span otelSpan, Map<String, AnyValue> attributeMap) {
        if (CollectionUtils.isEmpty(otelSpan.getAttributesList())) {
            return false;
        }
        long durationNano = otelSpan.getEndTimeUnixNano() - otelSpan.getStartTimeUnixNano();
        long durationMs = TimeUnit.NANOSECONDS.toMillis(durationNano);
        for (Map.Entry<String, AnyValue> keyValue : attributeMap.entrySet()) {
            if (StringUtils.containsIgnoreCase(keyValue.getKey(), "http")) {
                return durationMs >= httpThresholdMs;
            }
            if (StringUtils.containsIgnoreCase(keyValue.getKey(), "db.system")) {
                Object dbSystem = OtelUtils.getActualValue(keyValue.getValue());
                if (StringUtils.equalsAnyIgnoreCase(String.valueOf(dbSystem), "mysql")) {
                    return durationMs >= mysqlThresholdMs;
                }
                if (StringUtils.equalsAnyIgnoreCase(String.valueOf(dbSystem), "redis")) {
                    return durationMs >= redisThresholdMs;
                }
            }
        }
        return false;
    }

    @Override
    public String getObservedKey() {
        return OBSERVED_SYSTEM_PARAM_KEY;
    }

    @Override
    public void onChange(String param) {
        try {
            Map<String, Object> paramMap = JsonUtils.parse(param, new TypeReference<>() {
            });
            if (paramMap.containsKey(HTTP_THRESHOLD)) {
                httpThresholdMs = Integer.parseInt(paramMap.get(HTTP_THRESHOLD).toString());
                logger.info("http threshold set to {}", httpThresholdMs);
            }
            if (paramMap.containsKey(MYSQL_THRESHOLD)) {
                mysqlThresholdMs = Integer.parseInt(paramMap.get(MYSQL_THRESHOLD).toString());
                logger.info("mysql threshold set to {}", mysqlThresholdMs);
            }
            if (paramMap.containsKey(REDIS_THRESHOLD)) {
                redisThresholdMs = Integer.parseInt(paramMap.get(REDIS_THRESHOLD).toString());
                logger.info("redis threshold set to {}", redisThresholdMs);
            }
        } catch (JsonProcessingException e) {
            logger.error("failed to parse json string: {}", param, e);
        }
    }
}
