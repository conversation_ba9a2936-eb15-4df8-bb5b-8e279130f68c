package us.zoom.trace.biz.metric.meta.extractor.impl;

import com.google.common.collect.Sets;
import io.opentelemetry.proto.trace.v1.Span;
import org.apache.commons.lang3.StringUtils;
import us.zoom.trace.biz.metric.meta.extractor.MetricExtractor;
import us.zoom.trace.common.DbClientMetrics;
import us.zoom.trace.common.TraceCommonAttributeKey;
import us.zoom.trace.common.TraceCommonAttributeValue;
import us.zoom.trace.exporter.clickhouse.model.SpanIndex;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class DbClientMetricExtractor extends MetricExtractor<DbClientMetrics> {

    private static final Set<Span.SpanKind> types = Sets.newHashSet(Span.SpanKind.SPAN_KIND_CLIENT);

    private static final Set<String> RECORD_SQL_OPERATIONS = Sets.newHashSet("db.Query", "db.Exec", "stmt.Query", "stmt.Exec",
            "tx.Exec", "tx.Query", "tx.Commit", "tx.Rollback");

    @Override
    protected DbClientMetrics doExtract(SpanIndex spanIndex) {
        DbClientMetrics.DbClientMetricsBuilder builder = DbClientMetrics.builder()
                .dbSystem(spanIndex.getDbSystem());
        Map<String, String> stringTagMap = spanIndex.getStringTagMap();
        String dbOperation;
        if (StringUtils.isNotBlank(spanIndex.getDbOperation())) {
            dbOperation = spanIndex.getDbOperation();
        } else {
            dbOperation = spanIndex.getName();
        }
        builder.dbOperation(dbOperation);
        if (StringUtils.isNotBlank(spanIndex.getHttpHost())) {
            builder.host(spanIndex.getHttpHost());
        } else if (stringTagMap.containsKey(TraceCommonAttributeKey.NETWORK_PEER_ADDRESS.attributeName())) {
            builder.host(stringTagMap.get(TraceCommonAttributeKey.NETWORK_PEER_ADDRESS.attributeName()));
        }
        String statement = stringTagMap.get(TraceCommonAttributeKey.DB_STATEMENT.attributeName());
        if (StringUtils.isNotBlank(statement) &&
                (StringUtils.equals(spanIndex.getDbSystem(), TraceCommonAttributeValue.MYSQL) || RECORD_SQL_OPERATIONS.contains(dbOperation))) {
            String cleanedStatement = getCleanedStatement(statement);
            stringTagMap.put(TraceCommonAttributeKey.DB_STATEMENT.attributeName(), cleanedStatement);
            builder.dbStatement(cleanedStatement);
            builder.dbSystem(TraceCommonAttributeValue.MYSQL);
        } else {
            builder.dbStatement(dbOperation);
        }
        if (stringTagMap.containsKey(TraceCommonAttributeKey.DB_SQL_TABLE.attributeName())) {
            builder.tableName(stringTagMap.get(TraceCommonAttributeKey.DB_SQL_TABLE.attributeName()));
        }
        if (stringTagMap.containsKey(TraceCommonAttributeKey.ROOT_OPERATION.attributeName())) {
            builder.rootOperation(stringTagMap.get(TraceCommonAttributeKey.ROOT_OPERATION.attributeName()));
        }
        return builder.build();
    }

    @Override
    public Set<Span.SpanKind> spanKindTypes() {
        return types;
    }

    @Override
    public boolean shouldExtract(SpanIndex spanIndex) {
        return StringUtils.isNotBlank(spanIndex.getDbSystem()) ||
                Optional.ofNullable(spanIndex.getStringTagMap()).map(map -> map.get(TraceCommonAttributeKey.DB_STATEMENT.attributeName())).isPresent();
    }

    private String getCleanedStatement(String statement) {
        statement = statement.trim();
        if (statement.startsWith("/*")) {
            int startIndex = 0;
            for (int i = 2; i < statement.length(); i++) {
                if (statement.charAt(i) == '*' && (i + 1 < statement.length()) && statement.charAt(i + 1) == '/') {
                    startIndex = i + 2;
                }
            }
            if (startIndex < statement.length()) {
                return statement.substring(startIndex).trim();
            }
        } else if (statement.endsWith("*/")) {
            int endIndex = statement.length();
            for (int i = statement.length() - 3; i >= 0; i--) {
                if (statement.charAt(i) == '*' && (i - 1 >= 0) && statement.charAt(i - 1) == '/') {
                    endIndex = i - 1;
                }
            }
            if (endIndex > 0) {
                return statement.substring(0, endIndex).trim();
            }
        }
        return statement.trim();
    }
}
