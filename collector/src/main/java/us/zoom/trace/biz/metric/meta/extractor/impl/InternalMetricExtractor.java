package us.zoom.trace.biz.metric.meta.extractor.impl;

import com.google.common.collect.Sets;
import io.opentelemetry.proto.trace.v1.Span;
import us.zoom.trace.biz.metric.meta.extractor.MetricExtractor;
import us.zoom.trace.common.InternalMetrics;
import us.zoom.trace.exporter.clickhouse.model.SpanIndex;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class InternalMetricExtractor extends MetricExtractor<InternalMetrics> {

    private static final Set<Span.SpanKind> types = Sets.newHashSet(Span.SpanKind.SPAN_KIND_INTERNAL);

    @Override
    protected InternalMetrics doExtract(SpanIndex spanIndex) {
        return null;
    }

    @Override
    public Set<Span.SpanKind> spanKindTypes() {
        return types;
    }

    @Override
    public boolean shouldExtract(SpanIndex spanIndex) {
        return true;
    }
}
