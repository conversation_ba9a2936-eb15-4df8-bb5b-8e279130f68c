package us.zoom.trace.biz.longtimestorage;

import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.trace.v1.Span;
import io.opentelemetry.proto.trace.v1.Status;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.trace.common.Context;

import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/9/8
 */
@Order(1)
@Component("errorTraceRuleMatcher")
@ConditionalOnExpression("'${cube.trace.consume.delay.enable:false}'.equals('false')")
public class ErrorTraceRuleMatcher implements LongTimeStorageRuleMatcher {

    @Override
    public boolean isMatch(Context context, Span otelSpan, Map<String, AnyValue> attributeMap) {
        return otelSpan.getStatus().getCodeValue() == Status.StatusCode.STATUS_CODE_ERROR_VALUE;
    }
}
