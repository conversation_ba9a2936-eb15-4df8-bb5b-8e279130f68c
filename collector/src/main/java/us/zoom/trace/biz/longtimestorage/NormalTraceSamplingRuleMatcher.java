package us.zoom.trace.biz.longtimestorage;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.trace.v1.Span;
import jakarta.annotation.PostConstruct;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.SystemParamObserver;
import us.zoom.trace.common.config.TraceSystemParamCache;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.ProbabilityUtils;

import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/9/8
 */
@Order(Ordered.LOWEST_PRECEDENCE)
@Component("normalTraceSamplingRuleMatcher")
@ConditionalOnExpression("'${cube.trace.consume.delay.enable:false}'.equals('false')")
public class NormalTraceSamplingRuleMatcher implements LongTimeStorageRuleMatcher, SystemParamObserver {

    private static final Logger logger = LoggerFactory.getLogger(NormalTraceSamplingRuleMatcher.class);
    private static final String OBSERVED_SYSTEM_PARAM_KEY = "normalTraceSamplingRuleMatcher";
    private static final String SAMPLING_PROBABILITY = "sampling_probability";

    /**
     * default 1/10000
     */
    @Setter
    private double samplingProbability = 0.0001;

    @PostConstruct
    public void init() {
        TraceSystemParamCache.addObserver(this);
    }

    @Override
    public boolean isMatch(Context context, Span otelSpan, Map<String, AnyValue> attributeMap) {
        return ProbabilityUtils.checkProbability(samplingProbability);
    }

    @Override
    public String getObservedKey() {
        return OBSERVED_SYSTEM_PARAM_KEY;
    }

    @Override
    public void onChange(String param) {
        try {
            Map<String, Object> paramMap = JsonUtils.parse(param, new TypeReference<>() {
            });
            if (paramMap.containsKey(SAMPLING_PROBABILITY)) {
                samplingProbability = Double.parseDouble(paramMap.get(SAMPLING_PROBABILITY).toString());
                logger.info("samplingProbability set to {}", samplingProbability);
            }
        } catch (JsonProcessingException e) {
            logger.error("failed to parse json string: {}", param, e);
        }
    }
}
