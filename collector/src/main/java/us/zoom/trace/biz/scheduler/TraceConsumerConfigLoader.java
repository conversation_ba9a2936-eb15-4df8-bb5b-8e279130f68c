package us.zoom.trace.biz.scheduler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.cube.lib.trace.TraceConfig;
import us.zoom.trace.asyncmq.AsyncMqInitializer;
import us.zoom.trace.common.config.TraceConfigCache;
import us.zoom.trace.util.JsonUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class TraceConsumerConfigLoader implements TraceConfigLoader {

    private static final Logger logger = LoggerFactory.getLogger(TraceConsumerConfigLoader.class);

    @Autowired
    private ConfigApi configApi;

    @Autowired
    private AsyncMqInitializer asyncMqInitializer;

    @Value("${unit.tag.name}")
    private String unitTag;

    @Override
    public void loadConfig() {
        List<TraceConfig> traceConfigs = configApi.getTraceConfig(unitTag);
        logger.info("load trace configs from cube-config: {}", JsonUtils.toJsonStringIgnoreException(traceConfigs));
        // update asyncmq consumer
        TraceConfigCache.refresh(traceConfigs);
        // doRefresh config cache
        asyncMqInitializer.refreshTraceConfig(unitTag);
    }

    @Override
    public int order() {
        return Integer.MAX_VALUE;
    }
}
