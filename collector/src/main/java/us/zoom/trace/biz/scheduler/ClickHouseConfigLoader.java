package us.zoom.trace.biz.scheduler;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.infra.clickhouse.ClickhouseEnvProxy;
import us.zoom.infra.clickhouse.ClickhouseWriter;
import us.zoom.infra.dao.model.ClickhouseClusterDO;
import us.zoom.infra.dao.model.EnvironmentDO;
import us.zoom.infra.utils.RSAUtils;

import java.util.List;
import java.util.stream.Collectors;

import static us.zoom.trace.common.Constants.LOCAL_ENV;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ClickHouseConfigLoader implements TraceConfigLoader {

    @Autowired
    private ConfigApi configApi;

    @Autowired
    private ClickhouseEnvProxy clickhouseEnvProxy;

    @Autowired
    private ClickhouseWriter clickhouseWriter;

    @SecretValue("common_private_key")
    private String commonPrivateKey;

    @SecretValue("common_public_key")
    private String commonPublicKey;

    @Override
    public void loadConfig() {
        try {
            List<ClickhouseClusterDO> clusters = configApi.getAllClickhouseClusters();
            if (StringUtils.equals(clickhouseEnvProxy.getEnvironment(), LOCAL_ENV)) {
                clusters = clusters.stream()
                        .filter(u -> LOCAL_ENV.equals(u.getEnvironment())).peek(u -> u.setIsDefault(true))
                        .collect(Collectors.toList());
            } else {
                clusters = clusters.stream()
                        .filter(u -> !LOCAL_ENV.equals(u.getEnvironment()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(clusters)) {
                log.warn("No cluster has been defined");
                return;
            }
            List<ClickhouseClusterDO> copyList = Lists.newArrayList();
            for (ClickhouseClusterDO source : clusters) {
                ClickhouseClusterDO copy = new ClickhouseClusterDO();
                try {
                    BeanUtils.copyProperties(source, copy);
                    copy.setUsername(RSAUtils.decryptByPrivateKey(copy.getUsername(), commonPrivateKey));
                    copy.setPassword(RSAUtils.decryptByPrivateKey(copy.getPassword(), commonPrivateKey));
                } catch (Exception e) {
                    log.error("Decrypt error for cluster {} {}", copy.getId(), copy.getName(), e);
                }
                copyList.add(copy);
            }
            clickhouseEnvProxy.refresh(copyList, configApi.getAllClickhouseClusterRelations(), configApi.getAllTenants());
            clickhouseWriter.setAllEnvs(configApi.getAllEnvironments().stream().map(EnvironmentDO::getName).collect(Collectors.toSet()));
            log.info("Done load clickhouse handlers,size ={}", clickhouseEnvProxy.getAll().size());
        } catch (Exception e) {
            log.error("Load Clickhouse error! ", e);
        }
    }

    @Override
    public int order() {
        return Integer.MAX_VALUE;
    }
}
