package us.zoom.trace.biz.metric.meta.extractor.impl;

import com.google.common.collect.Sets;
import io.opentelemetry.proto.trace.v1.Span;
import org.apache.commons.lang3.StringUtils;
import us.zoom.trace.biz.metric.meta.extractor.MetricExtractor;
import us.zoom.trace.common.HttpClientMetrics;
import us.zoom.trace.common.TraceCommonAttributeKey;
import us.zoom.trace.exporter.clickhouse.model.SpanIndex;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class HttpClientMetricExtractor extends MetricExtractor<HttpClientMetrics> {

    private static final Set<Span.SpanKind> types = Sets.newHashSet(Span.SpanKind.SPAN_KIND_CLIENT);

    @Override
    public HttpClientMetrics doExtract(SpanIndex spanIndex) {
        Map<String, String> stringTagMap = spanIndex.getStringTagMap();
        String rootOperation = Optional.ofNullable(stringTagMap).map(map -> map.get(TraceCommonAttributeKey.ROOT_OPERATION.attributeName()))
                .orElse(null);
        return HttpClientMetrics.builder()
                .httpMethod(spanIndex.getHttpMethod())
                .host(spanIndex.getHttpHost())
                .responseCode(spanIndex.getResponseStatusCode())
                .rootOperation(rootOperation)
                .build();
    }

    @Override
    public Set<Span.SpanKind> spanKindTypes() {
        return types;
    }

    @Override
    public boolean shouldExtract(SpanIndex spanIndex) {
        return StringUtils.isNotBlank(spanIndex.getHttpMethod()) && StringUtils.isBlank(spanIndex.getDbSystem());
    }
}
