package us.zoom.trace.biz.longtimestorage;

import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.trace.v1.Span;
import us.zoom.trace.common.Context;

import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/9/8
 */
public interface LongTimeStorageRuleMatcher {

    /**
     * determine whether the span needs to be stored for a long time
     *
     * @param context
     * @param otelSpan
     * @param attributeMap
     * @return
     */
    boolean isMatch(Context context, Span otelSpan, Map<String, AnyValue> attributeMap);
}