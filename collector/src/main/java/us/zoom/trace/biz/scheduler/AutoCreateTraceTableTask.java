package us.zoom.trace.biz.scheduler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.infra.clickhouse.ClickHouseDataSourceProxy;
import us.zoom.infra.clickhouse.ClickhouseEnvProxy;
import us.zoom.infra.clickhouse.ClickhouseHandler;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.trace.common.SystemParamObserver;
import us.zoom.trace.common.TraceCommonAttributeKey;
import us.zoom.trace.common.config.TraceSystemParamCache;
import us.zoom.trace.monitor.CubeStandardMetric;
import us.zoom.trace.util.IpUtils;
import us.zoom.trace.util.RedisService;
import us.zoom.trace.util.ThreadPoolExecutorUtils;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static us.zoom.trace.common.Constants.ALL_APPLICATION_MARK;
import static us.zoom.trace.common.Constants.AUTO_CREATE_TABLE_UNIT_KEY;
import static us.zoom.trace.common.Constants.DATABASE_NAME;
import static us.zoom.trace.common.Constants.TRACE_DDL_SQL_PARAM;
import static us.zoom.trace.common.Constants.TRACE_WHITE_LIST_PARAM;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class AutoCreateTraceTableTask {

    private static final int DEFAULT_EXPIRE_TIME_SECONDS = 120;
    private static final String REDIS_LOCK_KEY = "auto_create_trace_table_lock";
    private static final String GET_ALL_TABLE_NAMES = "SELECT name as tableName FROM system.tables WHERE database = '{database}'";
    private static final String MONITOR_METRIC_NAME = "auto_create_trace_table";
    private static final String QUERY_SERVICE_NAME_SQL = "query_service_name_sql";
    private static final String CREATE_DATABASE_DDL = "CREATE DATABASE IF NOT EXISTS \"%s\"";
    private static final int DATABASE_NOT_EXIST_CODE = 81;

    @Value("${unit.tag.name}")
    private String unitTag;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ClickhouseEnvProxy clickhouseEnvProxy;

    @Autowired
    private ConfigApi configApi;


    private final String hostname = IpUtils.getHost();

    @Scheduled(cron = "0 0/1 * * * ?")
    public void autoCreateTraceTable() {
        try {
            String autoCreateTraceTableUnit = TraceSystemParamCache.getSystemParam(AUTO_CREATE_TABLE_UNIT_KEY);
            if (!StringUtils.equals(autoCreateTraceTableUnit, unitTag)) {
                return;
            }
            Boolean success = redisService.setExIfAbsent(REDIS_LOCK_KEY, hostname, DEFAULT_EXPIRE_TIME_SECONDS, TimeUnit.SECONDS);
            if (!Boolean.TRUE.equals(success)) {
                return;
            }
            long start = System.currentTimeMillis();
            initTraceTable();
            new CubeStandardMetric(MONITOR_METRIC_NAME)
                    .withTag("host", hostname)
                    .withTag("unit", unitTag)
                    .withField("cost", System.currentTimeMillis() - start).report();
        } catch (Throwable e) {
            log.error("auto create apm table failed", e);
        }
    }

    private void initTraceTable() {
        ClickhouseHandler cubeTrace = clickhouseEnvProxy.getHandlerByEnv().getByServiceName(DATABASE_NAME);
        List<MetricDDL> DDLs = TraceSystemParamCache.getSystemParamOrDefault(TRACE_DDL_SQL_PARAM, new TypeReference<>() {
        }, Collections.emptyList());
        if (DDLs.isEmpty()) {
            log.error("can't find default trace table ddl");
            return;
        }
        List<String> services;
        String querySql = TraceSystemParamCache.getSystemParam(QUERY_SERVICE_NAME_SQL);
        if (StringUtils.isEmpty(querySql)) {
            List<Map<String, Object>> results = clickhouseEnvProxy.query(DATABASE_NAME, querySql);
            if (CollectionUtils.isNotEmpty(results)) {
                services = results.stream().map(row -> (String) row.get(TraceCommonAttributeKey.SERVICE_NAME.alias()))
                        .collect(Collectors.toList());
            } else {
                services = Collections.emptyList();
            }
        } else {
            services = configApi.getAllTraceServices();
        }
        List<CompletableFuture<Void>> futures = Lists.newArrayList();
        Set<String> whiteList = TraceSystemParamCache.getSystemParamOrDefault(TRACE_WHITE_LIST_PARAM, new TypeReference<>() {
        }, Sets.newHashSet());
        if (whiteList.isEmpty()) {
            log.error("can't find apm white list service parameter");
            return;
        }
        List<MetricDDL> independentDDLs = Lists.newArrayList();
        List<MetricDDL> selfDDLs = DDLs.stream().filter(metricDDL -> {
            if (Boolean.TRUE.equals(metricDDL.getIsSelf())) {
                return true;
            } else {
                independentDDLs.add(metricDDL);
                return false;
            }
        }).toList();
        for (String service : services) {
            boolean whiteListed = whiteList.contains(service) || whiteList.contains(ALL_APPLICATION_MARK);
            ClickhouseHandler handler = clickhouseEnvProxy.getHandlerByEnv().getByServiceName(service);
            String database = ClickhouseSqlUtil.encodeClickhouseName(service);
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                if (whiteListed && handler != null) {
                    handler.getEnableDataSources().forEach(dataSource -> {
                        Set<String> tableNameSet = Sets.newHashSet(querySingleFieldSql(GET_ALL_TABLE_NAMES.replaceAll("\\{database}", database) + " and tableName like '%cube_trace%'",
                                dataSource, "tableName"));
                        executeSql(independentDDLs, tableNameSet, database, dataSource);
                    });
                }
                cubeTrace.getEnableDataSources().forEach(dataSource -> {
                    Set<String> traceTables = Sets.newHashSet(querySingleFieldSql(GET_ALL_TABLE_NAMES.replaceAll("\\{database}", "cube_trace") +
                                    " and tableName like '%" + database + "'",
                            cubeTrace.getDataSource(), "tableName"));
                    executeSql(selfDDLs, traceTables, database, dataSource);
                });
            }, ThreadPoolExecutorUtils.getCommonThreadPoolExecutor());
            futures.add(completableFuture);
        }

        futures.forEach(CompletableFuture::join);
    }

    private void executeSql(List<MetricDDL> DDLs, Set<String> existsTableNameSet, String database, ClickHouseDataSourceProxy dataSource) {
        if (CollectionUtils.isEmpty(DDLs)) {
            return;
        }
        DDLs.forEach(metricDDL -> {
            String actualTableName = metricDDL.getActualTableName(database);
            if (actualTableName == null || existsTableNameSet.contains(actualTableName)) {
                return;
            }
            String ddl = metricDDL.getDDL().replaceAll("\\{database}", database).trim();
            executeSql(ddl, dataSource, database, false);
        });
    }

    private void executeSql(String sql, ClickHouseDataSourceProxy dataSource, String database, boolean ignoredRetryable) {
        try (Connection conn = dataSource.getConnection()) {
            Statement stmt = conn.createStatement();
            stmt.executeUpdate(sql);
            log.info("execute sql {} successfully", sql);
        } catch (SQLException sqlException) {
            int errorCode = sqlException.getErrorCode();
            if (DATABASE_NOT_EXIST_CODE == errorCode && !ignoredRetryable) {
                String createDBSql = String.format(CREATE_DATABASE_DDL, database);
                try (Connection conn = dataSource.getConnection()) {
                    Statement stmt = conn.createStatement();
                    stmt.executeUpdate(createDBSql);
                    log.info("create database {} successfully", database);
                    executeSql(sql, dataSource, database, true);
                } catch (Throwable e) {
                    log.error("create database {} failed", database, e);
                }
            } else {
                log.error("execute sql {} in {} error", sql, dataSource.getUrl(), sqlException);
            }

        }
    }

    private List<String> querySingleFieldSql(String sql, ClickHouseDataSourceProxy dataSource, String key) {
        List<String> result = Lists.newArrayList();
        try (Connection conn = dataSource.getConnection()) {
            Statement stmt = conn.createStatement();
            ResultSet resultSet = stmt.executeQuery(sql);
            while (resultSet.next()) {
                String serviceName = resultSet.getString(key);
                result.add(serviceName);
            }
        } catch (Exception e) {
            log.error("query table {} in {} error", sql, dataSource.getUrl(), e);
        }
        return result;
    }

    @Data
    private static class MetricDDL {
        private String tableName;
        private String DDL;
        private int order;
        private Boolean isSelf;
        private String tableNamePrefix;

        public String getActualTableName(String database) {
            if (StringUtils.isNotBlank(tableName)) {
                return tableName;
            } else if (StringUtils.isNotBlank(tableNamePrefix)) {
                return tableNamePrefix + database;
            }
            return null;
        }
    }

}
