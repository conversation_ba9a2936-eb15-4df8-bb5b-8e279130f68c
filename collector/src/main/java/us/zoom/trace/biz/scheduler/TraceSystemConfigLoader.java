package us.zoom.trace.biz.scheduler;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.trace.common.config.TraceSystemParamCache;

import java.util.List;

import static us.zoom.trace.common.Constants.SYSTEM_PARAM_TYPE;

/**
 * <AUTHOR>
 */
@Component
public class TraceSystemConfigLoader implements TraceConfigLoader {

    @Autowired
    private ConfigApi configApi;
    @Autowired
    private TraceSystemParamCache traceSystemParamCache;

    @Override
    public void loadConfig() {
        List<SysParaDO> sysParaByTypes = configApi.getSysParaByTypes(Lists.newArrayList(SYSTEM_PARAM_TYPE));
        traceSystemParamCache.refresh(sysParaByTypes);
    }

    @Override
    public int order() {
        return Integer.MIN_VALUE;
    }
}
