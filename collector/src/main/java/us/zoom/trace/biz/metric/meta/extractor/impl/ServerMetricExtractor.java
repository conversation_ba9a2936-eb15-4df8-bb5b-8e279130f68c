package us.zoom.trace.biz.metric.meta.extractor.impl;

import com.google.common.collect.Sets;
import io.opentelemetry.proto.trace.v1.Span;
import us.zoom.trace.biz.metric.meta.extractor.MetricExtractor;
import us.zoom.trace.common.ServerMetrics;
import us.zoom.trace.common.TraceCommonAttributeKey;
import us.zoom.trace.exporter.clickhouse.model.SpanIndex;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class ServerMetricExtractor extends MetricExtractor<ServerMetrics> {

    private static final Set<Span.SpanKind> types = Sets.newHashSet(Span.SpanKind.SPAN_KIND_SERVER);

    @Override
    protected ServerMetrics doExtract(SpanIndex spanIndex) {
        ServerMetrics build = ServerMetrics.builder()
                .httpURI(spanIndex.getHttpRoute())
                .httpMethod(spanIndex.getHttpMethod())
                .responseCode(spanIndex.getResponseStatusCode()).build();
        Map<String, String> stringTagMap = spanIndex.getStringTagMap();
        if (stringTagMap.containsKey(TraceCommonAttributeKey.ZM_TRACE_UPSTREAM.attributeName())) {
            build.setUpstream(stringTagMap.get(TraceCommonAttributeKey.ZM_TRACE_UPSTREAM.attributeName()));
        }
        return build;
    }

    @Override
    public Set<Span.SpanKind> spanKindTypes() {
        return types;
    }

    @Override
    public boolean shouldExtract(SpanIndex spanIndex) {
        return true;
    }
}
