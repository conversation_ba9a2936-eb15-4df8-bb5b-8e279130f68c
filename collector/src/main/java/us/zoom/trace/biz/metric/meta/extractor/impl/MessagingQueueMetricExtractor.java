package us.zoom.trace.biz.metric.meta.extractor.impl;

import com.google.common.collect.Sets;
import io.opentelemetry.proto.trace.v1.Span;
import us.zoom.trace.biz.metric.meta.extractor.MetricExtractor;
import us.zoom.trace.common.MqMetrics;
import us.zoom.trace.common.TraceCommonAttributeKey;
import us.zoom.trace.exporter.clickhouse.model.SpanIndex;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class MessagingQueueMetricExtractor extends MetricExtractor<MqMetrics> {

    private static final Set<Span.SpanKind> types = Sets.newHashSet(Span.SpanKind.SPAN_KIND_PRODUCER, Span.SpanKind.SPAN_KIND_CONSUMER);

    private static final String MQ_OPERATION_PRODUCER_TYPE = "produce";
    private static final String MQ_OPERATION_CONSUMER_TYPE = "consume";


    @Override
    protected MqMetrics doExtract(SpanIndex spanIndex) {
        Map<String, String> stringTagMap = spanIndex.getStringTagMap();
        String msgOperation =  Span.SpanKind.SPAN_KIND_CONSUMER.name().equals(spanIndex.getSpanKind()) ? MQ_OPERATION_CONSUMER_TYPE : MQ_OPERATION_PRODUCER_TYPE;
        return MqMetrics.builder()
                .topicName(stringTagMap.get(TraceCommonAttributeKey.TOPIC_NAME.attributeName()))
                .msgOperation(msgOperation)
                .upstreamService(stringTagMap.get(TraceCommonAttributeKey.ZM_TRACE_UPSTREAM.attributeName()))
                .rootOperation(stringTagMap.get(TraceCommonAttributeKey.ROOT_OPERATION.attributeName()))
                .build();
    }

    @Override
    public Set<Span.SpanKind> spanKindTypes() {
        return types;
    }

    @Override
    public boolean shouldExtract(SpanIndex spanIndex) {
        return true;
    }
}
