package us.zoom.trace.biz.metric.meta.extractor;

import cn.hutool.core.lang.ClassScanner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.opentelemetry.proto.trace.v1.Span;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import us.zoom.trace.common.BaseREDMetrics;
import us.zoom.trace.common.Context;
import us.zoom.trace.exporter.clickhouse.model.SpanIndex;

import java.lang.reflect.Constructor;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CombinedMetricExtractor extends MetricExtractor {


    private static final String PACKAGE = "us.zoom.trace.biz.metric.meta.extractor.impl";

    private final Map<String, List<MetricExtractor>> extractorMap = Maps.newHashMap();


    @PostConstruct
    public void init() {
        Set<Class<?>> classes = ClassScanner.scanAllPackageBySuper(PACKAGE, MetricExtractor.class);
        classes.forEach(clazz -> {
            try {
                Constructor<?> constructor = clazz.getConstructor();
                MetricExtractor extractor = (MetricExtractor) constructor.newInstance();
                Set<Span.SpanKind> kinds = extractor.spanKindTypes();
                for (Span.SpanKind kind : kinds) {
                    extractorMap.computeIfAbsent(kind.name(), n -> Lists.newArrayList()).add(extractor);
                }
            } catch (Throwable e) {
                log.error("initialize metricExtractor failed", e);
                throw new RuntimeException(e);
            }
        });
    }


    @Override
    public BaseREDMetrics extract(SpanIndex spanIndex, Context context) {
        List<MetricExtractor> metricExtractor = extractorMap.get(spanIndex.getSpanKind());
        if (metricExtractor == null) {
            return null;
        }
        BaseREDMetrics baseREDMetrics = null;
        for (MetricExtractor extractor : metricExtractor) {
            if (!extractor.shouldExtract(spanIndex)) {
                continue;
            }
            baseREDMetrics = extractor.extract(spanIndex, context);
            //todo add custom script to extract extra tags
            break;
        }
        return baseREDMetrics;
    }

    @Override
    protected BaseREDMetrics doExtract(SpanIndex spanIndex) {
        throw new UnsupportedOperationException("Not supported for CombinedMetricExtractor");
    }

    @Override
    public Set<Span.SpanKind> spanKindTypes() {
        return Collections.emptySet();
    }

    @Override
    public boolean shouldExtract(SpanIndex spanIndex) {
        return false;
    }
}
