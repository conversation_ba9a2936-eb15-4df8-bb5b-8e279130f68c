package us.zoom.trace.biz.longtimestorage;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import us.zoom.mq.common.util.GzipUtil;
import us.zoom.trace.monitor.CubeStandardMetric;
import us.zoom.trace.monitor.MetricType;
import us.zoom.trace.util.DateUtils;
import us.zoom.trace.util.IpUtils;
import us.zoom.trace.util.RedisService;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static java.nio.charset.StandardCharsets.UTF_8;
import static us.zoom.trace.common.config.LuaScript.REDIS_SCRIPT_GET_BLOOM_FILTER;
import static us.zoom.trace.common.config.LuaScript.REDIS_SCRIPT_SET_BLOOM_FILTER;

/**
 * @author: eason.jia
 * @date: 2024/9/9
 */
@Component
@DependsOn("redisTemplate")
public class LongTimeStorageTraceIdCache {

    private static final int DEFAULT_CACHE_SIZE = 300;

    private static final int DEFAULT_CACHE_EXPIRE_TIME_SECONDS = 2400;

    private static final int DEFAULT_REDIS_CACHE_EXPIRE_TIME_SECONDS = DEFAULT_CACHE_EXPIRE_TIME_SECONDS * 6;

    private static final int DEFAULT_INTERVAL_SECONDS = 20 * 60;

    private static final int DEFAULT_INIT_LOAD_FILTER_COUNT = 12;

    private static final int DEFAULT_SCHEDULER_TASK_INTERVAL_MILLISECONDS = 60 * 1000;

    private static final int DEFAULT_EXPECTED_NUM = 1000000;
    private static final double DEFAULT_FPP = 0.001;
    private static final Logger log = LoggerFactory.getLogger(LongTimeStorageTraceIdCache.class);

    private static final String KEY_PREFIX = "longTimeStorageSampling_";

    private static final BloomFilter<byte[]> DEFAULT_BLOOM_FILTER = BloomFilter.create(Funnels.byteArrayFunnel(), DEFAULT_EXPECTED_NUM, DEFAULT_FPP);

    private Cache<Long, BloomFilter<byte[]>> bloomFilterCache = Caffeine.newBuilder()
            .initialCapacity(DEFAULT_CACHE_SIZE)
            .maximumSize(DEFAULT_CACHE_SIZE)
            .expireAfterAccess(DEFAULT_CACHE_EXPIRE_TIME_SECONDS, TimeUnit.SECONDS)
            .recordStats()
            .build();

    private final RedisService redisService;

    @Value("${cube.trace.consume.delay.enable:false}")
    private boolean delayConsume;

    private final String hostname = IpUtils.getHost();

    private final ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();

    @Autowired
    public LongTimeStorageTraceIdCache(RedisService redisService) {
        this.redisService = redisService;
    }

    @PostConstruct
    public void init() {
        if (delayConsume) {
            fetchFromRedis(true);
            long interval = DEFAULT_SCHEDULER_TASK_INTERVAL_MILLISECONDS * 2;
            scheduledExecutorService.scheduleAtFixedRate(this::fetchFromRedis, interval, interval, TimeUnit.MILLISECONDS);
        } else {
            scheduledExecutorService.scheduleAtFixedRate(this::batchSetToRedis, new Random().nextInt(DEFAULT_SCHEDULER_TASK_INTERVAL_MILLISECONDS),
                    DEFAULT_SCHEDULER_TASK_INTERVAL_MILLISECONDS, TimeUnit.MILLISECONDS);
        }
    }

    @PreDestroy
    public void onShutdown() {
        if (delayConsume) {
            return;
        }
        try {
            long startTime = System.currentTimeMillis();
            log.info("LongTimeStorageTraceIdCache final flush start");
            batchSetToRedis();
            log.info("LongTimeStorageTraceIdCache final flush end, cost: {}", System.currentTimeMillis() - startTime);
        } catch (Throwable e) {
            log.error("LongTimeStorageTraceIdCache final flush error", e);
        }
    }

    private void fetchFromRedis() {
        fetchFromRedis(false);
    }

    private void fetchFromRedis(boolean init) {
        Map<Long, BloomFilter<byte[]>> newBloomFilters = Maps.newHashMap();
        if (!init) {
            long currentTimeMillis = System.currentTimeMillis();
            long currentPosition = -1;
            for (Map.Entry<Long, BloomFilter<byte[]>> entry : bloomFilterCache.asMap().entrySet()) {
                Long key = entry.getKey();
                currentPosition = Math.max(currentPosition, key);
                buildBloomFilterCache(key, entry.getValue(), newBloomFilters);
            }
            while ((currentPosition = currentPosition + DEFAULT_INTERVAL_SECONDS * 1000) < currentTimeMillis) {
                buildBloomFilterCache(currentPosition,  null, newBloomFilters);
            }
        } else {
            long periodBeginTime = DateUtils.getPeriodBeginTime(System.currentTimeMillis(), DEFAULT_INTERVAL_SECONDS);
            for (int i = 0; i < DEFAULT_INIT_LOAD_FILTER_COUNT; i++) {
                long key = periodBeginTime - i * DEFAULT_INTERVAL_SECONDS * 1000;
                buildBloomFilterCache(key, null, newBloomFilters);
            }
        }
        bloomFilterCache.putAll(newBloomFilters);
    }

    private void buildBloomFilterCache(Long key, BloomFilter<byte[]> currentBloomFilter, Map<Long, BloomFilter<byte[]>> newBloomFilters) {
        BloomFilter<byte[]> remote = getAllRemoteBloomFilterAndMerge(key);
        if (remote == null || remote.equals(currentBloomFilter)) {
            return;
        }
        newBloomFilters.put(key, remote);
    }

    private void batchSetToRedis() {
        for (Map.Entry<Long, BloomFilter<byte[]>> entry : bloomFilterCache.asMap().entrySet()) {
            long startTime = System.currentTimeMillis();
            String key = KEY_PREFIX + entry.getKey();
            boolean success = true;
            long size = 0;
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                entry.getValue().writeTo(outputStream);
                byte[] encode = Base64.getEncoder().encode(GzipUtil.zip(outputStream.toByteArray()));
                size = encode.length;
                redisService.eval(REDIS_SCRIPT_SET_BLOOM_FILTER, Lists.newArrayList(key), hostname, new String(encode, UTF_8), DEFAULT_REDIS_CACHE_EXPIRE_TIME_SECONDS);
            } catch (Throwable e) {
                log.error("update bloomFilter error for key: {}", key, e);
                success = false;
            }
            new CubeStandardMetric(MetricType.write_bloom_filter_metric.name())
                    .withTag("redisKey", key)
                    .withTag("success", success)
                    .withField("cost", System.currentTimeMillis() - startTime)
                    .withField("filterSize", size).report();
        }
    }

    public long addTraceId(byte[] traceId, long nanoTime) {
        long periodBeginTime = DateUtils.getPeriodBeginTime(TimeUnit.NANOSECONDS.toMillis(nanoTime), DEFAULT_INTERVAL_SECONDS);
        BloomFilter<byte[]> bloomFilter = bloomFilterCache.get(periodBeginTime, t -> {
            return BloomFilter.create(Funnels.byteArrayFunnel(), DEFAULT_EXPECTED_NUM, DEFAULT_FPP);
        });
        bloomFilter.put(traceId);
        return periodBeginTime;
    }

    private BloomFilter<byte[]> getAllRemoteBloomFilterAndMerge(Long key) {
        boolean exist = true;
        boolean success = true;
        long getStartTime = System.currentTimeMillis();
        String redisKey = KEY_PREFIX + key;
        BloomFilter<byte[]> mergedBloomFilter = null;
        try {
            List base64StrList = redisService.eval(REDIS_SCRIPT_GET_BLOOM_FILTER, Lists.newArrayList(redisKey), DEFAULT_REDIS_CACHE_EXPIRE_TIME_SECONDS);
            if (CollectionUtils.isEmpty(base64StrList)) {
                exist = false;
            } else {
                for (Object base64Str : base64StrList) {
                    BloomFilter<byte[]> remoteBloomFilter = getBloomFilterFromBase64Str((String) base64Str);
                    if (remoteBloomFilter == null) {
                        continue;
                    }
                    if (mergedBloomFilter == null) {
                        mergedBloomFilter = remoteBloomFilter;
                    } else {
                        mergedBloomFilter.putAll(remoteBloomFilter);
                    }
                }
            }
        } catch (Throwable e) {
            success = false;
            log.error("getAllRemoteBloomFilterAndMerge error", e);
        }
        new CubeStandardMetric(MetricType.get_bloom_filter_metric.name())
                .withTag("redisKey", redisKey)
                .withTag("exist", exist)
                .withTag("success", success)
                .withField("estimatedSize", Optional.ofNullable(mergedBloomFilter).map(BloomFilter::approximateElementCount).orElse(0L))
                .withField("cost", System.currentTimeMillis() - getStartTime).report();
        return mergedBloomFilter;
    }


    public BloomFilter<byte[]> getBloomFilter(long nanoTime) {
        long millis = TimeUnit.NANOSECONDS.toMillis(nanoTime);
        return bloomFilterCache.get(DateUtils.getPeriodBeginTime(millis, DEFAULT_INTERVAL_SECONDS), k -> {
            BloomFilter<byte[]> filter = getAllRemoteBloomFilterAndMerge(k);
            return filter == null ? DEFAULT_BLOOM_FILTER : filter;
        });
    }

    private BloomFilter<byte[]> getBloomFilterFromBase64Str(String base64EncodedString) {
        byte[] unzip = GzipUtil.unzip(Base64.getDecoder().decode(base64EncodedString));
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(unzip)) {
            return BloomFilter.readFrom(inputStream, Funnels.byteArrayFunnel());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
