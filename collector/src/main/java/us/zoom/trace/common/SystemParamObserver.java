package us.zoom.trace.common;

import static us.zoom.trace.common.Constants.SYSTEM_PARAM_TYPE;

/**
 * @author: eason.jia
 * @date: 2024/9/9
 */
public interface SystemParamObserver {

    /**
     * type of system param
     *
     * @return
     */
    default String getObservedType() {
        return SYSTEM_PARAM_TYPE;
    }

    /**
     * key of system param
     *
     * @return
     */
    String getObservedKey();

    /**
     * will be notify when params changed
     *
     * @param param
     */
    void onChange(String param);
}
