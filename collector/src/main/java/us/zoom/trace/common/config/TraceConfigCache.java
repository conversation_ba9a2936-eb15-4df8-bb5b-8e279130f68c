package us.zoom.trace.common.config;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cube.lib.trace.TraceConfig;

import java.util.List;
import java.util.Map;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: eason.jia
 * @date: 2024/8/7
 */
public class TraceConfigCache {

    private static final Logger logger = LoggerFactory.getLogger(TraceSystemParamCache.class);
    @Getter
    private static Map<String/**topic name*/, TraceConfig> topicConfigMapping = Maps.newHashMap();

    public static void refresh(List<TraceConfig> traceConfigList) {
        if (CollectionUtils.isEmpty(traceConfigList)) {
            return;
        }
        topicConfigMapping = traceConfigList.stream()
                .collect(Collectors.toMap(TraceConfig::getTopic, Function.identity(), mergeFunction()));
    }

    private static BinaryOperator<TraceConfig> mergeFunction() {
        return (traceConfig1, traceConfig2) -> {
            try {
                return traceConfig1.getGmtModify().after(traceConfig2.getGmtModify()) ? traceConfig1 : traceConfig2;
            } catch (Exception e) {
                logger.error("there's something went wrong while merging trace configs", e);
                return traceConfig1;
            }
        };
    }

    public static TraceConfig getConfigByTopic(String topic) {
        return topicConfigMapping.get(topic);
    }
}
