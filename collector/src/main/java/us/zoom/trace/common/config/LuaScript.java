package us.zoom.trace.common.config;

import org.springframework.data.redis.core.script.RedisScript;

import java.util.List;

/**
 * @author: eason.jia
 * @date: 2024/9/9
 */
public class LuaScript {

    public static final String LUA_SCRIPT_GET_AND_REMOVE_TRACE_ID_LIST =
            """
                            -- get params. The first is the key of zset, the second is the given score value(timestamp), and the third is the number of elements to be taken out and deleted.
                            local zsetKey = KEYS[1]
                            local timestamp = tonumber(ARGV[1])
                            local numElements = tonumber(ARGV[2])
                                                        
                            -- get elements whose score value is less than the given timestamp
                            local elements = redis.call('ZRANGEBYSCORE', zsetKey, '-inf', timestamp, 'LIMIT', 0, numElements)
                                                
                            -- delete elements
                            if next(elements) ~= nil then
                                redis.call('ZREM', zsetKey, unpack(elements))
                            end
                                                
                            return elements
                    """;
    public static final String LUA_SCRIPT_SET_BLOOM_FILTER =
            """
                            local hashKey = KEYS[1]
                            local field = ARGV[1]
                            local value = ARGV[2]
                            local expireTime = tonumber(ARGV[3])
                            local result = redis.call("HSET", hashKey, field, value)
                            redis.call("EXPIRE", hashKey, expireTime)
                            return result
                    """;


    public static final String LUA_SCRIPT_GET_BLOOM_FILTER =
            """
                            local hashKey = KEYS[1]
                            local expireTime = tonumber(ARGV[1])
                            local values = redis.call("HVALS", hashKey)
                            redis.call("EXPIRE", hashKey, expireTime)
                            return values
                    """;

    public static final RedisScript<Long> REDIS_SCRIPT_SET_BLOOM_FILTER = RedisScript.of(LUA_SCRIPT_SET_BLOOM_FILTER, Long.class);
    public static final RedisScript<List> REDIS_SCRIPT_GET_BLOOM_FILTER = RedisScript.of(LUA_SCRIPT_GET_BLOOM_FILTER, List.class);
}
