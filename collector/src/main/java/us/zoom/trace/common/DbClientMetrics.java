package us.zoom.trace.common;

import com.google.common.base.Joiner;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class DbClientMetrics extends BaseREDMetrics {
    private String dbSystem;
    private String dbOperation;
    private String dbStatement;
    private String tableName;
    private String host;
    private String rootOperation;

    @Builder
    public DbClientMetrics(String dbSystem, String dbOperation, String dbStatement, String tableName, String host, String rootOperation) {
        this.dbSystem = dbSystem;
        this.dbOperation = dbOperation;
        this.dbStatement = dbStatement;
        this.tableName = tableName;
        this.host = host;
        this.rootOperation = rootOperation;
    }


    @Override
    public List<Object> buildKeyGroup() {
        List<Object> group = super.buildKeyGroup();
        group.add(dbSystem);
        group.add(dbOperation);
        group.add(dbStatement);
        group.add(host);
        group.add(tableName);
        group.add(rootOperation);
        return group;
    }

    @Override
    public String metricType() {
        return "cube_trace_db_client_apm_metrics";
    }
}