package us.zoom.trace.common;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ServerMetrics extends HttpMetrics {

    private String upstream;

    @Builder
    public ServerMetrics(String httpURI, String httpMethod, String responseCode, String upstream) {
        super(httpURI, httpMethod, responseCode);
        this.upstream = upstream;
    }

    @Override
    public String metricType() {
        return "cube_trace_server_apm_metrics";
    }

    @Override
    public List<Object> buildKeyGroup() {
        List<Object> keyGroup = super.buildKeyGroup();
        if (upstream != null) {
            keyGroup.add(upstream);
        }
        return keyGroup;
    }
}