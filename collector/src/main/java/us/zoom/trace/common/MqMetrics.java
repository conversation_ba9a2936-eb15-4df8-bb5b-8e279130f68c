package us.zoom.trace.common;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class MqMetrics extends BaseREDMetrics {
    private String topicName;
    private String msgOperation;
    private String rootOperation;
    private String upstreamService;


    @Override
    public List<Object> buildKeyGroup() {
        List<Object> group = super.buildKeyGroup();
        group.add(topicName);
        group.add(msgOperation);
        if (rootOperation != null) {
            group.add(rootOperation);
        }
        if (upstreamService != null) {
            group.add(upstreamService);
        }
        return group;
    }

    @Override
    public String metricType() {
        return "cube_trace_messaging_queue_apm_metrics";
    }
}
