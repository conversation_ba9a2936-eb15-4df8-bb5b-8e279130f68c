package us.zoom.trace.common;

import com.google.common.base.Joiner;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class HttpMetrics extends BaseREDMetrics {
    private String httpURI;
    private String httpMethod;
    private String responseCode;

    public HttpMetrics(String httpURI, String httpMethod, String responseCode) {
        this.httpURI = httpURI;
        this.httpMethod = httpMethod;
        this.responseCode = responseCode;
    }

    public HttpMetrics() {
    }

    @Override
    public List<Object> buildKeyGroup() {
        List<Object> group = super.buildKeyGroup();
        if (httpURI != null) {
            group.add(httpURI);
        }
        if (httpMethod != null) {
            group.add(httpMethod);
        }
        if (responseCode != null) {
            group.add(responseCode);
        }
        return group;
    }
}
