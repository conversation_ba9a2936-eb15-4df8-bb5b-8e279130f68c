package us.zoom.trace.common;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HttpClientMetrics extends HttpMetrics {

    private String host;
    private String rootOperation;

    @Builder
    public HttpClientMetrics(String httpURI, String httpMethod, String responseCode, String host, String rootOperation) {
        super(httpURI, httpMethod, responseCode);
        this.host = host;
        this.rootOperation = rootOperation;
    }

    @Override
    public List<Object> buildKeyGroup() {
        List<Object> group = super.buildKeyGroup();
        if (host != null) {
            group.add(host);
        }
        return group;
    }

    @Override
    public String metricType() {
        return "cube_trace_http_client_apm_metrics";
    }
}
