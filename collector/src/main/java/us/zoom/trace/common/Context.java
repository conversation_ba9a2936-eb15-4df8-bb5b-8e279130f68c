package us.zoom.trace.common;

import lombok.Getter;
import lombok.Setter;
import us.zoom.trace.pipeline.PipelineType;
import us.zoom.trace.receiver.ReceiverType;
import us.zoom.trace.util.TimeDuration;

/**
 * @author: eason.jia
 * @date: 2024/8/2
 */
public class Context {

    @Getter
    private ReceiverType receiverType;
    @Getter
    private PipelineType pipelineType;
    @Getter
    private String topicName;
    @Getter
    @Setter
    private boolean needLongTimeStorage;

    @Getter
    @Setter
    private long taskProduceTime;

    private TimeDuration pipelineDuration;

    public Context(ReceiverType receiverType, PipelineType pipelineType, String topicName, long taskProduceTime) {
        this.receiverType = receiverType;
        this.pipelineType = pipelineType;
        this.topicName = topicName;
        this.taskProduceTime = taskProduceTime;
    }

    public Context() {
    }

    public void setPipelineStartTime(long startTime) {
        pipelineDuration = new TimeDuration(startTime);
    }

    public void setPipelineEndTime(long endTime) {
        pipelineDuration.setEndTime(endTime);
    }

    public long getPipelineDuration() {
        return pipelineDuration.duration();
    }
}