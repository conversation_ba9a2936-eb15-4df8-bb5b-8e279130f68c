package us.zoom.trace.common;

import com.google.common.collect.ImmutableSet;

import java.util.Set;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
public interface Constants {

    // monitor log
    String MONITOR_LOGGER_NAME = "Monitor";
    String RED_MONITOR_LOGGER_NAME = "REDMonitor";
    String CUBE_METRIC_LOG_VERSION = "1.0.0";
    String MEASURE_MESSAGE_BATCH_CONSUME_METRIC = "message_batch_consume_metric";
    String MEASURE_TOPIC_RATE_LIMIT_METRIC = "topic_rate_limit_metric";
    String MEASURE_THREAD_POOL_METRIC = "thread_pool_metric";
    String MEASURE_LONG_TIME_STORAGE_TRACE_NUMBER = "long_time_storage_trace_number";
    String MEASURE_LONG_TIME_STORAGE_SET_REDIS = "long_time_storage_set_redis";
    String BUSINESS_RED_METRICS = "business_red_metrics";
    String MEASURE_LOAD_TRACE_CONFIG = "load_trace_config";
    String MEASURE_LOAD_TRACE_SYSTEM_CONFIG = "load_trace_system_config";

    // jwt
    String JWT_ISSUER = "cube-trace";

    // system parameter from cube-trace
    String SYSTEM_PARAM_TYPE = "trace";

    String LOCAL_ENV = "local";

    Set<String> RESOURCE_ATTRIBUTE_BLACK_LIST = ImmutableSet.<String>builder()
            .add("host.arch")
            .add("os.description")
            .add("os.type")
            .add("process.runtime.description")
            .add("process.runtime.name")
            .add("process.runtime.version")
            .add("telemetry.distro.name")
            .add("telemetry.sdk.language")
            .add("telemetry.sdk.name")
            .add("service.instance.id")
            .add("container.id")
            .build();

    // clickhouse
    String DATABASE_NAME = "cube_trace";
    String TABLE_TRACE_INDEX = "distributed_trace_index";
    String TABLE_SPAN = "distributed_trace_spans";
    String TABLE_TRACE_ERROR_INDEX = "distributed_trace_error_index";
    String TABLE_SPAN_ATTRIBUTES_KEYS = "distributed_span_attributes_keys";
    String TABLE_SPAN_ATTRIBUTES = "distributed_span_attributes";

    String LONG_TIME_STORAGE_TABLE_SUFFIX = "_cold";

    // redis
    String LONG_TIME_STORAGE_TRACE_ID_REDIS_KEY = "cube_trace_long_time_storage_trace_id_zset";

    String COMMON_SEPARATOR = "_";

    String COMPONENT_KEY = "component";

    long DEFAULT_METRIC_REPORT_DELAY_SECONDS = 30;

    String TRACE_DDL_SQL_PARAM = "trace_table_ddl";
    String TRACE_WHITE_LIST_PARAM = "trace_service_white_list";
    String AUTO_CREATE_TABLE_UNIT_KEY = "auto_create_trace_table_unit";
    String ALL_APPLICATION_MARK = "all";
    String TRACE_IGNORE_ATTRIBUTE_VALUE_KEYS = "trace_ignore_attribute_value_keys";

    char UNIT_SEPARATOR = '\u001F';
}