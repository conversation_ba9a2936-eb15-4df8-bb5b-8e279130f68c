package us.zoom.trace.common;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class InternalMetrics extends BaseREDMetrics {

    private String functionName;


    public InternalMetrics(String functionName) {
        this.functionName = functionName;
    }


    @Override
    public String metricType() {
        return "cube_trace_internal_apm_metrics";
    }

    public String getFunctionName() {
        return functionName;
    }

    public void setFunctionName(String functionName) {
        this.functionName = functionName;
    }
}
