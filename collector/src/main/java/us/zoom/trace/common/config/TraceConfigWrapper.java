package us.zoom.trace.common.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import us.zoom.cube.lib.trace.TraceConfig;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceConfigWrapper {
    String status;
    String operId;
    String message;
    String operCode;
    List<TraceConfig> data;
}
