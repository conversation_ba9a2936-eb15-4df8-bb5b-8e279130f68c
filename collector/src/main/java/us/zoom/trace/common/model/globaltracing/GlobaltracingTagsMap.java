package us.zoom.trace.common.model.globaltracing;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class GlobaltracingTagsMap {

    private static final Map<String, String> tagsMapping = Maps.newHashMap();

    public static final String COMPONENT_TAG_KEY = "component";
    public static final String ERROR_TAG_KEY = "error";

    static {
        tagsMapping.put("User-Agent", "User-Agent");
        tagsMapping.put("cluster", "cluster");
        tagsMapping.put("host_name", "host_name");
        tagsMapping.put("region_id", "region_id");
        tagsMapping.put("request.method", "http.method");
        tagsMapping.put("request.remote_ip", "http.remote_ip");
        tagsMapping.put("request.url", "http.url");
        tagsMapping.put("response.status_code", "http.status_code");
        tagsMapping.put("x-zm-trackingid", "x-zm-trackingid");
        tagsMapping.put("remote_peer", "remote_peer");
        tagsMapping.put("db.type", "db.system");
        tagsMapping.put("db.instance", "db.name");
    }

    public static String getTagMapping(String originalTag) {
        return tagsMapping.getOrDefault(originalTag, originalTag);
    }

}
