package us.zoom.trace.common.enums;

/**
 * <AUTHOR>
 */
public enum BasicAttribute {
    HTTP_ROUTE("http.route"),
    HTTP_METHOD("http.method"),
    HTTP_URL("http.url"),
    URL_FULL("url.full"),
    HTTP_REQUEST_METHOD("http.request.method"),
    SERVER_ADDRESS("server.address"),
    SERVER_PORT("server.port"),
    SDK_LANGUAGE("telemetry.sdk.language"),
    COMPONENT("component"),
    DB_CONNECTION_STRING("db.connection_string"),
    DB_SYSTEM("db.system");


    private String keyName;

    BasicAttribute(String keyName) {
        this.keyName = keyName;
    }

    public String getKeyName() {
        return keyName;
    }
}
