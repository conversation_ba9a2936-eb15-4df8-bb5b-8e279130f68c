package us.zoom.trace.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.clickhouse.ClickhouseUtil;
import us.zoom.infra.clickhouse.ClickhouseWriter;
import us.zoom.trace.monitor.AbstractDelayMetric;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.SpringBeanUtils;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 */
public abstract class BaseREDMetrics extends AbstractDelayMetric {

    private String cluster;
    private String region;
    private Collection<Long> durationList;
    private Collection<Byte> statusList;
    @JsonIgnore
    private String serviceName;
    @JsonIgnore
    private Map<String, Object> extraTags;

    private static ClickhouseWriter clickhouseWriter;

    static {
        clickhouseWriter = SpringBeanUtils.getBean(ClickhouseWriter.class);
    }

    @Override
    public void doRefresh() {
        durationList = new ConcurrentLinkedQueue<>();
        statusList = new ConcurrentLinkedQueue<>();
    }

    @Override
    public List<Object> buildKeyGroup() {
        return Lists.newArrayList(ts, cluster, region, serviceName);
    }

    @Override
    public void report() {
        Map<String, Object> attributes = JsonUtils.beanToMap(this);
        if (extraTags != null) {
            attributes.putAll(extraTags);
        }

        if (StringUtils.isBlank(metricType()) || StringUtils.isBlank(serviceName) || getTs() == 0) {
            return;
        }

        clickhouseWriter.write(ClickhouseSqlUtil.encodeClickhouseName(serviceName), toActualTableName(), attributes, new Timestamp(getTs()), 100, false, false);
    }

    public String getCluster() {
        return cluster;
    }

    public void setCluster(String cluster) {
        this.cluster = cluster;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Collection<Long> getDurationList() {
        return durationList;
    }

    public void setDurationList(List<Long> durationList) {
        this.durationList = durationList;
    }

    public Collection<Byte> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Byte> statusList) {
        this.statusList = statusList;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public Map<String, Object> getExtraTags() {
        return extraTags;
    }

    public void setExtraTags(Map<String, Object> extraTags) {
        this.extraTags = extraTags;
    }

    private String toActualTableName() {
        return "d_" + metricType() + "_origin_click_local";
    }
}
