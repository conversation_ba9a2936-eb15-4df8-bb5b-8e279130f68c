package us.zoom.trace.common;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum TraceCommonAttributeKey {
    TOPIC_NAME("messaging.destination.name", "topicName"),
    CLUSTER("cluster", "cluster"),
    APP_CLUSTER("appcluster", "cluster"),
    REGION("region", "regionId"),
    REGION_ID("regionId", "region"),
    USER_AGENT("user_agent.original", "agent"),
    COMPONENT("component", "component"),
    DB_SYSTEM("db.system", "dbSystem"),
    URL_FULL("url.full", "fullUrl"),
    SERVER_ADDRESS("server.address", "address"),
    HTTP_URL("http.url", "httpUrl"),
    RPC_SYSTEM("rpc.system", "rpcSystem"),
    RPC_SERVICE("rpc.service", "rpcService"),
    DB_STATEMENT("db.statement", "dbStatement"),
    SERVICE_NAME("service.name", "serviceName"),
    NETWORK_PEER_ADDRESS("network.peer.address", "address"),
    ROOT_OPERATION("root.operation", "rootOperation"),
    ZM_TRACE_UPSTREAM("zm-trace-upstream", "upstreamService"),
    DB_SQL_TABLE("db.sql.table", "tableName");

    private static final Map<String, TraceCommonAttributeKey> map = new HashMap<>();

    static {
        for (TraceCommonAttributeKey key : TraceCommonAttributeKey.values()) {
            map.put(key.attributeName(), key);
        }
    }

    private String attributeName;
    private String alias;

    TraceCommonAttributeKey(String attributeName, String alias) {
        this.attributeName = attributeName;
        this.alias = alias;
    }

    public String attributeName() {
        return attributeName;
    }

    public String alias() {
        return alias;
    }

    public static TraceCommonAttributeKey fromKey(String key) {
        return map.get(key);
    }

    public String getValue(Map<String, String> tagMap) {
        return tagMap.getOrDefault(attributeName, tagMap.get(alias));
    }

    public String getValueOrDefault(Map<String, String> tagMap, String defaultValue) {
        return tagMap.getOrDefault(attributeName, tagMap.getOrDefault(alias, defaultValue));
    }
}