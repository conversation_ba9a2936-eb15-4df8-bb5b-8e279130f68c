package us.zoom.trace.common.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.trace.common.SystemParamObserver;
import us.zoom.trace.util.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/5
 */
@Component
public class TraceSystemParamCache {

    private static final Logger logger = LoggerFactory.getLogger(TraceSystemParamCache.class);

    private static final Table<String, String, List<SystemParamObserver>> observerTable = HashBasedTable.create();
    private static Map<String, String> systemParamCache = Maps.newHashMap();

    public static void addObserver(SystemParamObserver observer) {
        List<SystemParamObserver> systemParamObservers = observerTable.get(observer.getObservedType(), observer.getObservedKey());
        if (systemParamObservers == null) {
            systemParamObservers = new ArrayList<>();
            observerTable.put(observer.getObservedType(), observer.getObservedKey(), systemParamObservers);
        }
        systemParamObservers.add(observer);
    }

    public void refresh(List<SysParaDO> systemParamList) {
        if (CollectionUtils.isEmpty(systemParamList)) {
            return;
        }

        Map<String, String> sysParaMap = Maps.newHashMap();
        for (SysParaDO sysParaDO : systemParamList) {
            sysParaMap.putIfAbsent(sysParaDO.getParaKey(), sysParaDO.getValue());
            try {
                List<SystemParamObserver> systemParamObservers = observerTable.get(sysParaDO.getType(), sysParaDO.getParaKey());
                if (CollectionUtils.isEmpty(systemParamObservers)) {
                    continue;
                }
                logger.info("doRefresh systemParam: {}", sysParaDO);
                for (SystemParamObserver systemParamObserver : systemParamObservers) {
                    systemParamObserver.onChange(sysParaDO.getValue());
                }
            } catch (Exception e) {
                logger.error("Failed to deserialize the system param: {}", sysParaDO, e);
            }
        }
        systemParamCache = sysParaMap;
    }

    public static <T> T getSystemParamOrDefault(String key, TypeReference<T> typeReference, T defaultValue) {
        try {
            String systemParamOrDefault = getSystemParam(key);
            if (systemParamOrDefault == null) {
                return defaultValue;
            }
            return JsonUtils.parse(systemParamOrDefault, typeReference);
        } catch (Throwable e) {
            logger.error("Failed to deserialize the system param: {}", key, e);
            return defaultValue;
        }
    }

    public static String getSystemParam(String key) {
        return systemParamCache.get(key);
    }
}