package us.zoom.trace.common.enums;

/**
 * <AUTHOR>
 */
public enum SdkLanguage {
    PYTHON("python"),
    JAVA("java"),
    GOLANG("golang"),
    UNKNOWN("unknown");

    private String language;

    SdkLanguage(String language) {
        this.language = language;
    }

    public String getLanguage() {
        return language;
    }

    public static SdkLanguage fromLanguage(String language) {
        for (SdkLanguage value : SdkLanguage.values()) {
            if (value.getLanguage().equals(language)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
