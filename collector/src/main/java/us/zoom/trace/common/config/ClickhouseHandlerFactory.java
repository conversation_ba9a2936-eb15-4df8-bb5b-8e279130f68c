package us.zoom.trace.common.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import us.zoom.infra.clickhouse.ClickhouseEnvProxy;
import us.zoom.infra.clickhouse.ClickhouseWriter;
import us.zoom.infra.clickhouse.HighSpeedWriter;

/**
 * <AUTHOR>
 * @date 2022-01-13 10:11
 */
@Getter
@Slf4j
@Component
public class ClickhouseHandlerFactory implements EnvironmentAware {

    private Environment environment;

    private ClickhouseWriter clickhouseWriter;
    private ClickhouseWriter highSpeedWriter;
    private ClickhouseEnvProxy clickhouseEnvProxy;

    private long flushMajorIntervalMillSecond;
    private long flushMajorIntervalForHighSpeedMillSecond;
    private long flushMinorIntervalMillSecond;
    private int flushMaxRecordCount;
    private int flushMinRecordCountPerTable;
    private boolean runOnLocal = false;

    private int threadPoolSize;
    private int queueMax;
    private int flushParallelism;
    private long queueMemoryLimit;
    private long singleTableMemoryLimit;


    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
        runOnLocal = environment.getProperty("spring.profiles.active", "").endsWith("_local");
        flushMajorIntervalMillSecond = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.major.interval.millsecond", "10000"));
        flushMajorIntervalForHighSpeedMillSecond = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.major.interval.high.speed.millsecond", "3000"));
        flushMinorIntervalMillSecond = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.minor.interval.millsecond", "5000"));
        flushMaxRecordCount = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.max.batch.size", "100000"));
        flushMinRecordCountPerTable = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.min.batch.size", "100"));
        threadPoolSize = Integer.parseInt(environment.getProperty("cube.clickhouse.thread.pool.size", "2"));
        flushParallelism = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.parallelism", "4"));
        //queueMemoryLimit default 5GB
        queueMemoryLimit = Long.parseLong(environment.getProperty("cube.clickhouse.queue.memory.limit", "5368709120"));
        //singleTableMemoryLimit default 200MB
        singleTableMemoryLimit = Long.parseLong(environment.getProperty("cube.clickhouse.single.table.limit", "209715200"));
        queueMax = Integer.parseInt(environment.getProperty("cube.clickhouse.queue.size.limit", "1500000"));

        clickhouseEnvProxy = new ClickhouseEnvProxy();
        clickhouseWriter = new ClickhouseWriter(this.clickhouseEnvProxy);
        clickhouseWriter.setFlushMajorIntervalMillSecond(flushMajorIntervalMillSecond).setFlushMinorIntervalMillSecond(flushMinorIntervalMillSecond)
                .setFlushMaxRecordCount(flushMaxRecordCount).setFlushMinRecordCountPerTable(flushMinRecordCountPerTable)
                .setThreadPoolSize(threadPoolSize).setFlushParallelism(flushParallelism)
                .setQueueMemoryLimit(queueMemoryLimit).setSingleTableMemoryLimit(singleTableMemoryLimit)
                .setQueueSizeMax(queueMax)
                .setQueueSize(queueMax);
        clickhouseWriter.start();

        highSpeedWriter = new HighSpeedWriter(this.clickhouseEnvProxy);
        highSpeedWriter.setFlushMajorIntervalMillSecond(flushMajorIntervalForHighSpeedMillSecond)
                .setFlushMinorIntervalMillSecond(flushMajorIntervalForHighSpeedMillSecond)
                .setFlushMaxRecordCount(flushMaxRecordCount / 10)
                .setThreadPoolSize(threadPoolSize)
                .setQueueMemoryLimit(queueMemoryLimit / 10).setFlushParallelism(flushParallelism)
                .setQueueSizeMax(queueMax)
                .setQueueSize(queueMax / 10);
        highSpeedWriter.start();
    }
}
