package us.zoom.trace.common.exception;

/**
 * Thrown by processor that indicates that the processor in pipeline fails to execution
 *
 * @author: eason.jia
 * @date: 2024/5/19
 */
public class ProcessException extends Exception {

    /**
     * Creates a new instance of <code>ProcessException</code> without detail message.
     */
    public ProcessException() {
    }

    /**
     * Constructs an instance of <code>ProcessException</code> with the specified detail message.
     *
     * @param msg the detail message.
     */
    public ProcessException(String msg) {
        super(msg);
    }

    /**
     * Constructs an instance of <code>ProcessException</code> with the specified detail message.
     *
     * @param msg the detail message.
     */
    public ProcessException(String msg, Throwable cause) {
        super(msg, cause);
    }

    /**
     * Constructs an instance of <code>ProcessException</code> with the specified detail message.
     *
     * @param cause the detail message.
     */
    public ProcessException(Throwable cause) {
        super(cause);
    }
}
