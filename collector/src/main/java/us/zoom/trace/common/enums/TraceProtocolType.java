package us.zoom.trace.common.enums;

/**
 * @author: eason.jia
 * @date: 2024/8/2
 */
public enum TraceProtocolType {

    /**
     * opentelemetry
     */
    OPENTELEMETRY("opentelemetry"),

    /**
     * opentelemetry protobuf
     */
    OPENTELEMETRY_PROTOBUF("opentelemetry_protobuf"),

    /**
     * GlobalTracing
     */
    GLOBALTRACING("globaltracing"),

    /**
     * SkyWalking
     */
    SKYWALKING("skywalking"),

    ;

    private String name;

    TraceProtocolType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
