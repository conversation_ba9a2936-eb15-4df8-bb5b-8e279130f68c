package us.zoom.trace.exporter.clickhouse.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceModel {
    private String traceId;
    private String spanId;
    private String name;
    private long durationNano;
    private long startTimeUnixNano;
    private String serviceName;
    private int kind;
    private String spanKind;
    private List<SpanRef> references;
    private int statusCode;
    private Map<String, String> tagMap;
    @JsonProperty("event")
    private List<String> events;
    private boolean hasError;
    private String statusMessage;
    private String statusCodeString;
}