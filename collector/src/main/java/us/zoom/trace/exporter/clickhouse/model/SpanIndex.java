package us.zoom.trace.exporter.clickhouse.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpanIndex {
    private String traceId;
    private String spanId;
    private String parentSpanId;
    private String name;
    private long durationNano;
    private long startTimeUnixNano;
    private String serviceName;
    private int kind;
    private String spanKind;
    private int statusCode;
    private String httpUrl;
    private String httpMethod;
    private String externalHttpUrl;
    private String externalHttpMethod;
    private String httpHost;
    private String httpRoute;
    private String msgSystem;
    private String msgOperation;
    private String dbSystem;
    private String dbName;
    private String dbOperation;
    private String peerService;
    private List<String> events;
    private Event errorEvent;
    private String errorID;
    private String errorGroupID;
    private Map<String, String> stringTagMap;
    private Map<String, Double> numberTagMap;
    private Map<String, Boolean> boolTagMap;
    private Map<String, String> resourceTagsMap;
    private boolean hasError;
    private String statusMessage;
    private String statusCodeString;
    private String isRemote;
    private TraceModel traceModel;
    private String rpcSystem;
    private String rpcService;
    private String rpcMethod;
    private String responseStatusCode;
    private String tenant;
}