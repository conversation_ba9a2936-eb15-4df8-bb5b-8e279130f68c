package us.zoom.trace.exporter.clickhouse.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @author: eason.jia
 * @date: 2024/8/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpanAttributeDO {
    private Timestamp time;
    private Timestamp timestamp;
    private String tagKey;
    private Integer tagType;
    private Integer dataType;
    private String stringTagValue;
    private Double float64TagValue;
    private Boolean isColumn;
}