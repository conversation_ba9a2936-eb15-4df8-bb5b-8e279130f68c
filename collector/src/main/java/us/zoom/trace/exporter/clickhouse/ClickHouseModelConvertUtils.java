package us.zoom.trace.exporter.clickhouse;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.trace.common.Constants;
import us.zoom.trace.common.SystemParamObserver;
import us.zoom.trace.common.TraceCommonAttributeKey;
import us.zoom.trace.common.config.TraceSystemParamCache;
import us.zoom.trace.exporter.clickhouse.dataobject.SpanAttributeDO;
import us.zoom.trace.exporter.clickhouse.dataobject.SpanAttributeKeyDO;
import us.zoom.trace.exporter.clickhouse.dataobject.SpanDO;
import us.zoom.trace.exporter.clickhouse.dataobject.TraceErrorIndexDO;
import us.zoom.trace.exporter.clickhouse.dataobject.TraceIndexDO;
import us.zoom.trace.exporter.clickhouse.model.Event;
import us.zoom.trace.exporter.clickhouse.model.SpanIndex;
import us.zoom.trace.exporter.clickhouse.model.TraceAttributeBloomFilter;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.MonitorLogUtils;

import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.invoke.MethodType;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static java.nio.charset.StandardCharsets.UTF_8;
import static us.zoom.trace.common.Constants.CUBE_METRIC_LOG_VERSION;
import static us.zoom.trace.common.Constants.UNIT_SEPARATOR;

/**
 * @author: eason.jia
 * @date: 2024/8/6
 */
@Component
@Slf4j
public class ClickHouseModelConvertUtils implements SystemParamObserver {

    private static final String SPAN_ATTRIBUTE_CACHE_MEASURE = "span_attribute_cache";
    private static final String OBSERVED_SYSTEM_PARAM_KEY = "clickhouse_convertor";
    private static final int DEFAULT_EXPECTED_NUM = 1000;
    private static final double DEFAULT_FPP = 0.001;
    private static final double DEFAULT_MAX_COUNT_P = 0.8;

    private static final String SPAN_ATTRIBUTE_CACHE_NAME = "spanAttributeCache";
    private static final Cache<String, TraceAttributeBloomFilter> SPAN_ATTRIBUTE_CACHE = Caffeine.newBuilder()
            .maximumSize(100000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .recordStats()
            .build();

    private static final Cache<String, Long> LARGE_CARDINALITY_KEY_CACHE = Caffeine.newBuilder()
            .maximumSize(100000)
            .expireAfterAccess(15, TimeUnit.MINUTES)
            .recordStats()
            .build();

    private static final Map<String, List<Pair<String, MethodHandle>>> OBJECT_TO_METHOD_HANDLES = Maps.newConcurrentMap();


    private static AttrConfig attrConfig = new AttrConfig();

    @PostConstruct
    public void init() {
        TraceSystemParamCache.addObserver(this);
        Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(() -> {
            buildCacheMetrics(SPAN_ATTRIBUTE_CACHE, SPAN_ATTRIBUTE_CACHE_NAME);
        }, 15, 15, TimeUnit.SECONDS);
    }

    private static void buildCacheMetrics(Cache<String, TraceAttributeBloomFilter> cache, String cacheName) {
        CacheStats stats = cache.stats();
        MonitorLogUtils.print(MonitorLog.builder()
                .withCubeVer(CUBE_METRIC_LOG_VERSION)
                .withMeasure(SPAN_ATTRIBUTE_CACHE_MEASURE)
                .addTag("cacheName", cacheName)
                .addField("hitCount", stats.hitCount())
                .addField("hitRate", stats.hitRate())
                .addField("loadCount", stats.loadCount())
                .addField("missCount", stats.missCount())
                .addField("evictionCount", stats.evictionCount())
                .addField("size", cache.estimatedSize())
                .withTs(System.currentTimeMillis())
                .build());
    }

    public static Map<String, Object> toTraceIndexRow(SpanIndex spanIndex) {
        TraceIndexDO traceIndexDO = TraceIndexDO.builder()
                .time(new Timestamp(TimeUnit.NANOSECONDS.toMillis(spanIndex.getStartTimeUnixNano())))
                .timestamp(spanIndex.getStartTimeUnixNano())
                .traceID(spanIndex.getTraceId())
                .spanID(spanIndex.getSpanId())
                .parentSpanID(spanIndex.getParentSpanId())
                .serviceName(spanIndex.getServiceName())
                .name(spanIndex.getName())
                .kind(spanIndex.getKind())
                .durationNano(spanIndex.getDurationNano())
                .statusCode(spanIndex.getStatusCode())
                .dbSystem(spanIndex.getDbSystem())
                .dbName(spanIndex.getDbName())
                .dbOperation(spanIndex.getDbOperation())
                .peerService(spanIndex.getPeerService())
                .events(spanIndex.getEvents())
                .httpMethod(spanIndex.getHttpMethod())
                .httpUrl(spanIndex.getHttpUrl())
                .httpRoute(spanIndex.getHttpRoute())
                .httpHost(spanIndex.getHttpHost())
                .msgSystem(spanIndex.getMsgSystem())
                .msgOperation(spanIndex.getMsgOperation())
                .hasError(spanIndex.isHasError())
                .rpcSystem(spanIndex.getRpcSystem())
                .rpcService(spanIndex.getRpcService())
                .rpcMethod(spanIndex.getRpcMethod())
                .responseStatusCode(spanIndex.getResponseStatusCode())
                .stringTagMap(spanIndex.getStringTagMap())
                .numberTagMap(spanIndex.getNumberTagMap())
                .boolTagMap(spanIndex.getBoolTagMap())
                .spanKind(spanIndex.getSpanKind())
                .statusCodeString(spanIndex.getStatusCodeString())
                .build();
        return beanToMapWithReflect(traceIndexDO);
    }

    public static Map<String, Object> toTraceIndexErrorRow(SpanIndex spanIndex) {
        Event errorEvent = spanIndex.getErrorEvent();
        if (errorEvent == null || !errorEvent.isError()) {
            return Maps.newHashMap();
        }
        Map<String, String> stringTagMap = spanIndex.getStringTagMap();
        String cluster = TraceCommonAttributeKey.CLUSTER.getValueOrDefault(stringTagMap, StringUtils.EMPTY);
        String region = TraceCommonAttributeKey.REGION.getValueOrDefault(stringTagMap, StringUtils.EMPTY);
        TraceErrorIndexDO traceErrorIndexDO = TraceErrorIndexDO.builder()
                .time(new Timestamp(TimeUnit.NANOSECONDS.toMillis(errorEvent.getTimeUnixNano())))
                .timestamp(errorEvent.getTimeUnixNano())
                .errorID(spanIndex.getErrorID())
                .groupID(spanIndex.getErrorGroupID())
                .traceID(spanIndex.getTraceId())
                .spanID(spanIndex.getSpanId())
                .cluster(cluster)
                .region(region)
                .exceptionType(errorEvent.getAttributeMap().get("exception.type"))
                .exceptionMessage(errorEvent.getAttributeMap().get("exception.message"))
                .exceptionStacktrace(errorEvent.getAttributeMap().get("exception.stacktrace"))
                .exceptionEscaped(Boolean.parseBoolean(errorEvent.getAttributeMap().get("exception.escaped")))
                .build();
        return beanToMapWithReflect(traceErrorIndexDO);
    }

    public static Map<String, Object> toSpanRow(SpanIndex span) {
        SpanDO spanDO = SpanDO.builder()
                .time(new Timestamp(TimeUnit.NANOSECONDS.toMillis(span.getStartTimeUnixNano())))
                .timestamp(span.getStartTimeUnixNano())
                .traceID(span.getTraceId())
                .model(JsonUtils.toJsonStringIgnoreException(span.getTraceModel()))
                .build();
        return beanToMapWithReflect(spanDO);
    }

    public static List<Map<String/** table name */, Map<String, Object>>> toSpanAttributeKeyAndValueRow(SpanIndex spanIndex) {
        List<Map<String/** table name */, Map<String, Object>>> ret = new LinkedList<>();

        spanIndex.getStringTagMap().forEach((key, value) -> {
            buildAttributeKeyAndValue(key, value, DataType.STRING, false, spanIndex.getServiceName(), spanIndex.getStartTimeUnixNano(), ret);
        });
        spanIndex.getNumberTagMap().forEach((key, value) -> {
            buildAttributeKeyAndValue(key, value, DataType.FLOAT64, false, spanIndex.getServiceName(), spanIndex.getStartTimeUnixNano(), ret);
        });
        spanIndex.getBoolTagMap().forEach((key, value) -> {
            buildAttributeKeyAndValue(key, value, DataType.BOOL, false, spanIndex.getServiceName(), spanIndex.getStartTimeUnixNano(), ret);
        });
        SpanBuilder.extractSpanAttributes(spanIndex).forEach(spanAttribute -> {
            DataType dataType = DataType.of(spanAttribute.getDataType());
            buildAttributeKeyAndValue(spanAttribute.getKey(), DataType.STRING.equals(dataType) ? spanAttribute.getStringValue() : spanAttribute.getNumberValue(),
                    dataType, spanAttribute.isColumn(), spanIndex.getServiceName(), spanIndex.getStartTimeUnixNano(), ret);
        });
        return ret;
    }

    private static void buildAttributeKeyAndValue(String key, Object value, DataType dataType, boolean isColumn, String serviceName, long startTimeUnixNano,
                                                  List<Map<String, Map<String, Object>>> ret) {
        if (key == null || attrConfig.getKeysToIgnore().contains(key)) {
            return;
        }
        Map<String/** table name */, Map<String, Object>> row = Maps.newHashMap();
        // span attribute key

        String keyUniqId = Joiner.on(UNIT_SEPARATOR).join(serviceName, key, isColumn, dataType.getCode());
        Long largeCardinalityFlag = LARGE_CARDINALITY_KEY_CACHE.getIfPresent(keyUniqId);
        TraceAttributeBloomFilter bloomFilter = SPAN_ATTRIBUTE_CACHE.getIfPresent(keyUniqId);
        if (bloomFilter == null) {
            bloomFilter = SPAN_ATTRIBUTE_CACHE.get(keyUniqId, k -> {
                SpanAttributeKeyDO spanAttributeKeyDO = buildSpanAttributeKeyDO(key, TagType.TAG.getCode(), dataType.getCode(), isColumn, false);
                BloomFilter<String> filter;
                if (largeCardinalityFlag != null || attrConfig.getKeysToIgnoreValue().contains(key)) {
                    filter = null;
                    spanAttributeKeyDO.setIsLargeCardinality(true);
                } else {
                    filter = BloomFilter.create(Funnels.stringFunnel(UTF_8), DEFAULT_EXPECTED_NUM, DEFAULT_FPP);
                }
                row.put(Constants.TABLE_SPAN_ATTRIBUTES_KEYS, beanToMapWithReflect(spanAttributeKeyDO));
                return TraceAttributeBloomFilter.builder()
                        .filter(filter)
                        .startTime(TimeUnit.NANOSECONDS.toMillis(startTimeUnixNano))
                        .expectedSize(DEFAULT_EXPECTED_NUM)
                        .build();
            });
        }
        BloomFilter<String> filter = bloomFilter.getFilter();
        if (filter == null || ((double) filter.approximateElementCount() / bloomFilter.getExpectedSize()) >= DEFAULT_MAX_COUNT_P) {
            LARGE_CARDINALITY_KEY_CACHE.get(keyUniqId, k -> {
                SPAN_ATTRIBUTE_CACHE.invalidate(keyUniqId);
                return System.currentTimeMillis();
            });
        } else if (value != null && !DataType.BOOL.equals(dataType) && filter.put(value.toString())) {
            SpanAttributeDO spanAttributeDO = buildSpanAttributeDO(startTimeUnixNano, key, TagType.TAG.getCode(),
                    dataType.getCode(), false, value);
            row.put(Constants.TABLE_SPAN_ATTRIBUTES, beanToMapWithReflect(spanAttributeDO));
        }
        if (row.isEmpty()) {
            return;
        }
        ret.add(row);
    }

    private static SpanAttributeDO buildSpanAttributeDO(long startTimeUnixNano, String key, int tagType,
                                                        int dataType, boolean isColumn, Object value) {
        SpanAttributeDO spanAttributeDO = SpanAttributeDO.builder()
                .time(new Timestamp(TimeUnit.NANOSECONDS.toMillis(startTimeUnixNano)))
                .timestamp(new Timestamp(TimeUnit.NANOSECONDS.toMillis(startTimeUnixNano)))
                .tagKey(key)
                .tagType(tagType)
                .dataType(dataType)
                .isColumn(isColumn)
                .build();

        if (DataType.STRING.getCode() == dataType) {
            spanAttributeDO.setStringTagValue(value.toString());
        } else if (DataType.FLOAT64.getCode() == dataType) {
            spanAttributeDO.setFloat64TagValue((double) value);
        } else if (DataType.BOOL.getCode() == dataType) {
            // do nothing
        }
        return spanAttributeDO;
    }

    private static SpanAttributeKeyDO buildSpanAttributeKeyDO(String key, int tagType, int dataType, boolean isColumn, boolean isLargeCardinality) {
        return SpanAttributeKeyDO.builder()
                .tagKey(key)
                .tagType(tagType)
                .dataType(dataType)
                .isColumn(isColumn)
                .isLargeCardinality(isLargeCardinality)
                .build();
    }

    // maybe performance is better
    private static Map<String, Object> beanToMapWithReflect(Object object) {
        Class<?> clazz = object.getClass();
        List<Pair<String, MethodHandle>> methodHandles = OBJECT_TO_METHOD_HANDLES.get(clazz.getName());
        if (methodHandles == null) {
            methodHandles = OBJECT_TO_METHOD_HANDLES.computeIfAbsent(clazz.getName(), k -> {
                List<Pair<String, MethodHandle>> handles = Lists.newArrayList();
                Field[] declaredFields = clazz.getDeclaredFields();
                for (Field field : declaredFields) {
                    String fieldName = field.getName();
                    Class<?> fieldType = field.getType();
                    String methodName;
                    if (fieldType == boolean.class) {
                        String suffix;
                        if (fieldName.startsWith("is") && fieldName.length() > 2) {
                            suffix = capitalize(fieldName.substring(2));
                        } else {
                            suffix = capitalize(fieldName);
                        }
                        methodName = "is" + suffix; // isXxx for primitive boolean
                    } else {
                        methodName = "get" + capitalize(fieldName); // getXxx for everything else
                    }
                    MethodHandle handle = findAccessorOrNull(clazz, methodName, fieldType);
                    if (handle != null) {
                        handles.add(Pair.of(fieldName, handle));
                    }
                }
                return handles;
            });
        }
        Map<String, Object> map = Maps.newHashMap();
        for (Pair<String, MethodHandle> pair : methodHandles) {
            Object value;
            try {
                value = pair.getValue().invoke(object);
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
            map.put(pair.getKey(), value);
        }
        return map;
    }

    private static String capitalize(String s) {
        if (s == null || s.isEmpty()) return s;
        return Character.toUpperCase(s.charAt(0)) + s.substring(1);
    }

    private static MethodHandle findAccessorOrNull(Class<?> clz, String methodName, Class<?> rtClazz) {
        try {
            return MethodHandles.publicLookup()
                    .findVirtual(clz, methodName, MethodType.methodType(rtClazz));
        } catch (Throwable t) {
            log.error("Can't find method {} in class {}, rtClazz is {}", methodName, clz.getName(), rtClazz.getName(), t);
            return null;
        }
    }

    @Override
    public String getObservedKey() {
        return OBSERVED_SYSTEM_PARAM_KEY;
    }

    @Override
    public void onChange(String param) {
        if (StringUtils.isBlank(param)) {
            return;
        }
        try {
            attrConfig = JsonUtils.parse(param, new TypeReference<AttrConfig>() {
            });
        } catch (Throwable throwable) {
            log.error("failed to parse attribute blacklist: {}", param, throwable);
        }
    }

    @Data
    private static class AttrConfig {
        private Set<String> commonKeys = Sets.newHashSet();
        private Set<String> keysToIgnoreValue = Sets.newHashSet();
        private Set<String> keysToIgnore = Sets.newHashSet();
    }
}