package us.zoom.trace.exporter.clickhouse;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.hash.HashCode;
import com.google.common.hash.Hashing;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.common.v1.KeyValue;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.Span;
import io.opentelemetry.proto.trace.v1.Status;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.trace.common.Constants;
import us.zoom.trace.common.TraceCommonAttributeKey;
import us.zoom.trace.exporter.clickhouse.model.ColumData;
import us.zoom.trace.exporter.clickhouse.model.Event;
import us.zoom.trace.exporter.clickhouse.model.SpanAttribute;
import us.zoom.trace.exporter.clickhouse.model.SpanIndex;
import us.zoom.trace.exporter.clickhouse.model.SpanRef;
import us.zoom.trace.exporter.clickhouse.model.TraceModel;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.OtelUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.EMPTY;
import static us.zoom.trace.exporter.clickhouse.RefType.CHILD_OF;
import static us.zoom.trace.exporter.clickhouse.RefType.FOLLOWS_FROM;


/**
 * @author: eason.jia
 * @date: 2024/8/7
 */
public class SpanBuilder {

    private static final Logger logger = LoggerFactory.getLogger(SpanBuilder.class);

    private static final int HAS_IS_REMOTE_MASK = 0x00000100;
    private static final int IS_REMOTE_MASK = 0x00000200;

    public static SpanIndex buildSpan(Span span, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource resource) {
        return buildSpan(span, attributeMap, resource, otelScope, true);
    }

    public static SpanIndex buildSpan(Span span, Map<String, AnyValue> attributeMap, Resource resource, InstrumentationScope otelScope, boolean lowCardinalExceptionGrouping) {
        Map<String, String> tagMap = new HashMap<>(){{
            put(Constants.COMPONENT_KEY, otelScope.getName());
        }};
        Map<String, String> stringTagMap = new HashMap<>() {{
            put(Constants.COMPONENT_KEY, otelScope.getName());
        }};
        Map<String, Double> numberTagMap = new HashMap<>();
        Map<String, Boolean> boolTagMap = new HashMap<>();
        // spanIndex
        SpanIndex spanIndex = buildSpan0(span, resource, stringTagMap, numberTagMap, boolTagMap, tagMap);
        // populate fields
        Map<String, AnyValue> resourceAttributeMap = resource.getAttributesList().stream().collect(Collectors.toMap(KeyValue::getKey, KeyValue::getValue, (newOne, oldOne) -> newOne));
        buildSpanAttributes(attributeMap, resourceAttributeMap, tagMap, stringTagMap, numberTagMap, boolTagMap);
        populateOtherDimensions(tagMap, spanIndex);
        populateEvents(spanIndex, span.getEventsList(), lowCardinalExceptionGrouping);
        populateTraceModel(spanIndex);
        return spanIndex;
    }

    private static SpanIndex buildSpan0(Span span,
                                        Resource resource,
                                        Map<String, String> stringTagMap,
                                        Map<String, Double> numberTagMap,
                                        Map<String, Boolean> boolTagMap,
                                        Map<String, String> tagMap) {
        long durationNano = span.getEndTimeUnixNano() - span.getStartTimeUnixNano();
        SpanIndex spanIndex = new SpanIndex();
        spanIndex.setTraceId(Hex.encodeHexString(span.getTraceId().toByteArray()));
        spanIndex.setSpanId(Hex.encodeHexString(span.getSpanId().toByteArray()));
        spanIndex.setParentSpanId(Hex.encodeHexString(span.getParentSpanId().toByteArray()));
        spanIndex.setName(span.getName());
        spanIndex.setStartTimeUnixNano(span.getStartTimeUnixNano());
        spanIndex.setDurationNano(durationNano);
        spanIndex.setKind(span.getKind().getNumber());
        spanIndex.setSpanKind(span.getKind().name());
        spanIndex.setStatusCode(span.getStatus().getCodeValue());
        spanIndex.setStringTagMap(stringTagMap);
        spanIndex.setNumberTagMap(numberTagMap);
        spanIndex.setBoolTagMap(boolTagMap);
        spanIndex.setStatusMessage(span.getStatus().getMessage());
        spanIndex.setStatusCodeString(span.getStatus().getCode().name());
        spanIndex.setTenant(getTenantNameFromResource(resource));
        spanIndex.setIsRemote(isRemote(span.getFlags()));
        spanIndex.setHasError(span.getStatus().getCodeValue() == Status.StatusCode.STATUS_CODE_ERROR.getNumber());
        TraceModel traceModel = buildTraceModel(spanIndex, durationNano, tagMap, span.getLinksList());
        spanIndex.setTraceModel(traceModel);
        return spanIndex;
    }

    private static void buildSpanAttributes(Map<String, AnyValue> attributeMap,
                                            Map<String, AnyValue> resourceAttributeMap,
                                            Map<String, String> tagMap,
                                            Map<String, String> stringTagMap,
                                            Map<String, Double> numberTagMap,
                                            Map<String, Boolean> boolTagMap) {

        attributeMap.forEach((k, v) -> {
            setAttribute(k, v, tagMap, stringTagMap, numberTagMap, boolTagMap);
        });
        resourceAttributeMap.forEach((k, v) -> {
            setAttribute(k, v, tagMap, stringTagMap, numberTagMap, boolTagMap);
        });
    }

    private static String isRemote(int flags) {
        String isRemote = "unknown";
        if ((flags & HAS_IS_REMOTE_MASK) != 0) {
            isRemote = "no";
            if ((flags & IS_REMOTE_MASK) != 0) {
                isRemote = "yes";
            }
        }
        return isRemote;
    }

    private static void setAttribute(String key,
                                     AnyValue value,
                                     Map<String, String> tagMap,
                                     Map<String, String> stringTagMap,
                                     Map<String, Double> numberTagMap,
                                     Map<String, Boolean> boolTagMap) {
        Object actualValue = OtelUtils.getActualValue(value);
        if (actualValue == null) {
            return;
        }
        tagMap.put(key, actualValue.toString());
        if (actualValue instanceof Number) {
            double doubleValue = ((Number) actualValue).doubleValue();
            numberTagMap.put(key, doubleValue);
        } else if (actualValue instanceof Boolean) {
            boolTagMap.put(key, (Boolean) actualValue);
        } else {
            stringTagMap.put(key, actualValue.toString());
        }
    }

    private static TraceModel buildTraceModel(SpanIndex spanIndex, long durationNano,
                                              Map<String, String> tagMap, List<Span.Link> linksList) {
        TraceModel traceModel = new TraceModel();
        traceModel.setTraceId(spanIndex.getTraceId());
        traceModel.setSpanId(spanIndex.getSpanId());
        traceModel.setName(spanIndex.getName());
        traceModel.setDurationNano(durationNano);
        traceModel.setStartTimeUnixNano(spanIndex.getStartTimeUnixNano());
        traceModel.setKind(spanIndex.getKind());
        traceModel.setSpanKind(spanIndex.getSpanKind());
        traceModel.setReferences(buildReferences(spanIndex.getTraceId(), spanIndex.getParentSpanId(), linksList));
        traceModel.setTagMap(tagMap);
        traceModel.setHasError(false);
        traceModel.setStatusMessage(spanIndex.getStatusMessage());
        traceModel.setStatusCodeString(spanIndex.getStatusCodeString());
        return traceModel;
    }

    private static List<SpanRef> buildReferences(String traceID, String parentSpanId, List<Span.Link> linksList) {
        List<SpanRef> spanRefs = new LinkedList<>();

        spanRefs.add(SpanRef.builder()
                .traceId(traceID)
                .spanId(Optional.ofNullable(parentSpanId).orElse(EMPTY))
                .refType(CHILD_OF.getType())
                .build());

        if (CollectionUtils.isNotEmpty(linksList)) {
            for (Span.Link link : linksList) {
                spanRefs.add(SpanRef.builder()
                        .traceId(Optional.of(link.getTraceId())
                                .map(byteString -> Hex.encodeHexString(byteString.toByteArray())).orElse(EMPTY))
                        .spanId(Optional.of(link.getSpanId()).map(byteString -> Hex.encodeHexString(byteString.toByteArray())).orElse(EMPTY))
                        .refType(FOLLOWS_FROM.getType())
                        .build());
            }
        }
        return spanRefs;
    }

    private static String getServiceNameForResource(Resource resource) {
        return Optional.of(resource.getAttributesList())
                .orElse(new ArrayList<>())
                .stream()
                .filter(otelAttribute -> StringUtils.equals(otelAttribute.getKey(), "service.name"))
                .findFirst()
                .map(otelAttribute -> otelAttribute.getValue().getStringValue())
                .orElse("<nil-service-name>");
    }

    private static String getTenantNameFromResource(Resource resource) {
        return Optional.of(resource.getAttributesList())
                .orElse(new ArrayList<>())
                .stream()
                .filter(otelAttribute -> StringUtils.equals(otelAttribute.getKey(), "tenant"))
                .findFirst()
                .map(otelAttribute -> otelAttribute.getValue().getStringValue())
                .orElse("default");
    }

    private static void populateOtherDimensions(Map<String, String> tagMap, SpanIndex spanIndex) {
        if (MapUtils.isEmpty(tagMap)) {
            return;
        }
        for (ColumData value : ColumData.values()) {
            value.mapSourceToTarget(tagMap, spanIndex);
        }
    }


    private static void populateEvents(SpanIndex spanIndex, List<Span.Event> otelEvents, boolean lowCardinalExceptionGrouping) {
        if (CollectionUtils.isEmpty(otelEvents)) {
            return;
        }
        spanIndex.setEvents(Lists.newArrayList());
        Map<String, String> stringTagMap = spanIndex.getStringTagMap();
        String cluster = TraceCommonAttributeKey.CLUSTER.getValueOrDefault(stringTagMap, EMPTY);
        String region = TraceCommonAttributeKey.REGION.getValueOrDefault(stringTagMap, EMPTY);
        for (Span.Event otelEvent : otelEvents) {
            Event event = new Event();
            event.setName(otelEvent.getName());
            event.setTimeUnixNano(otelEvent.getTimeUnixNano());
            // attribute
            Map<String, String> attributeMap = Maps.newHashMap();
            otelEvent.getAttributesList().forEach(attribute -> {
                attributeMap.put(attribute.getKey(), String.valueOf(OtelUtils.getActualValue(attribute.getValue())));
            });
            event.setAttributeMap(attributeMap);
            event.setError(false);

            if (StringUtils.equals(event.getName(), "exception")) {
                event.setError(true);
                spanIndex.setErrorEvent(event);
                // errorID
                UUID uuidWithHyphen = UUID.randomUUID();
                String uuid = uuidWithHyphen.toString().replaceAll("-", "");
                spanIndex.setErrorID(uuid);

                byte[] hashData;
                if (lowCardinalExceptionGrouping) {
                    hashData = Hashing.md5()
                            .hashBytes((cluster + region + event.getAttributeMap().get("exception.type")).getBytes())
                            .asBytes();
                } else {
                    hashData = Hashing.md5()
                            .hashBytes((cluster + region + event.getAttributeMap().get("exception.type") + event.getAttributeMap().get("exception.message")).getBytes())
                            .asBytes();
                }

                spanIndex.setErrorGroupID(HashCode.fromBytes(hashData).toString());
            }

            spanIndex.getEvents()
                    .add(Optional.ofNullable(JsonUtils.toJsonStringIgnoreException(event)).orElse(EMPTY));
        }
    }

    private static void populateTraceModel(SpanIndex spanIndex) {
        spanIndex.getTraceModel().setEvents(spanIndex.getEvents());
        spanIndex.getTraceModel().setHasError(spanIndex.isHasError());
    }

    public static List<SpanAttribute> extractSpanAttributes(SpanIndex spanIndex) {
        List<SpanAttribute> spanAttributes = new ArrayList<>();
        spanAttributes.add(new SpanAttribute(
                "traceID",
                "tag",
                "string",
                spanIndex.getTraceId(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "spanID",
                "tag",
                "string",
                spanIndex.getSpanId(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "parentSpanID",
                "tag",
                "string",
                spanIndex.getParentSpanId(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "name",
                "tag",
                "string",
                spanIndex.getName(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "serviceName",
                "tag",
                "string",
                spanIndex.getServiceName(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "spanKind",
                "tag",
                "string",
                spanIndex.getSpanKind(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "durationNano",
                "tag",
                "float64",
                "",
                (double) spanIndex.getDurationNano(),
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "statusCode",
                "tag",
                "float64",
                "",
                (double) spanIndex.getStatusCode(),
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "hasError",
                "tag",
                "bool",
                "",
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "statusMessage",
                "tag",
                "string",
                spanIndex.getStatusMessage(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "statusCodeString",
                "tag",
                "string",
                spanIndex.getStatusCodeString(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "externalHttpMethod",
                "tag",
                "string",
                spanIndex.getExternalHttpMethod(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "externalHttpUrl",
                "tag",
                "string",
                spanIndex.getExternalHttpUrl(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "dbSystem",
                "tag",
                "string",
                spanIndex.getDbSystem(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "dbName",
                "tag",
                "string",
                spanIndex.getDbName(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "dbOperation",
                "tag",
                "string",
                spanIndex.getDbOperation(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "peerService",
                "tag",
                "string",
                spanIndex.getPeerService(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "httpMethod",
                "tag",
                "string",
                spanIndex.getHttpMethod(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "httpUrl",
                "tag",
                "string",
                spanIndex.getHttpUrl(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "httpRoute",
                "tag",
                "string",
                spanIndex.getHttpRoute(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "httpHost",
                "tag",
                "string",
                spanIndex.getHttpHost(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "msgSystem",
                "tag",
                "string",
                spanIndex.getMsgSystem(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "msgOperation",
                "tag",
                "string",
                spanIndex.getMsgOperation(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "rpcSystem",
                "tag",
                "string",
                spanIndex.getRpcSystem(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "rpcService",
                "tag",
                "string",
                spanIndex.getRpcService(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "rpcMethod",
                "tag",
                "string",
                spanIndex.getRpcMethod(),
                0.0,
                true
        ));
        spanAttributes.add(new SpanAttribute(
                "responseStatusCode",
                "tag",
                "string",
                spanIndex.getResponseStatusCode(),
                0.0,
                true
        ));
        return spanAttributes;
    }
}
