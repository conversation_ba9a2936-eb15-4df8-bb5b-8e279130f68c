package us.zoom.trace.exporter.clickhouse.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceErrorIndexDO {
    private Timestamp time;
    private Long timestamp;
    private String errorID;
    private String groupID;
    private String traceID;
    private String cluster;
    private String region;
    private String spanID;
    private String exceptionType;
    private String exceptionMessage;
    private String exceptionStacktrace;
    private Boolean exceptionEscaped;
}