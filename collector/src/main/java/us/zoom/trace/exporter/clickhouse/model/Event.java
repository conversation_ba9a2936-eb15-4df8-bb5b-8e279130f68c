package us.zoom.trace.exporter.clickhouse.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Event {
    private String name;
    private long timeUnixNano;
    private Map<String, String> attributeMap;
    private boolean isError;
}