package us.zoom.trace.exporter;

import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.Span;
import us.zoom.trace.common.Context;

import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
public interface Exporter {

    boolean export(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource);
}
