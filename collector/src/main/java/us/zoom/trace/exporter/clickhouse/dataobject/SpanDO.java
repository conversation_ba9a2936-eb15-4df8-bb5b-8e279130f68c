package us.zoom.trace.exporter.clickhouse.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Date;

/**
 * @author: eason.jia
 * @date: 2024/8/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpanDO {
    private Timestamp time;
    private Long timestamp;
    private String traceID;
    private String model;
}