package us.zoom.trace.exporter.clickhouse;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: eason.jia
 * @date: 2024/8/7
 */
public enum DataType {

    STRING(1, "string"),
    BOOL(2, "bool"),
    FLOAT64(3, "float64"),
    ;

    int code;
    String desc;

    DataType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DataType of(String desc) {
        for (DataType dataType : DataType.values()) {
            if (StringUtils.equals(dataType.getDesc(), desc)) {
                return dataType;
            }
        }
        return null;
    }
}
