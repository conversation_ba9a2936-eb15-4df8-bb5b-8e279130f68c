package us.zoom.trace.exporter.clickhouse.model;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 */
public enum ColumData {
    responseStatusCode(Lists.newArrayList("http.status_code", "http.response.status_code","rpc.grpc.status_code","rpc.jsonrpc.error_code"), (val, spanIndex) -> spanIndex.setResponseStatusCode(val)),
    httpUrl(Lists.newArrayList("http.url", "url.full"), (val, spanIndex) -> spanIndex.setHttpUrl(val)),
    httpMethod(Lists.newArrayList("http.method", "http.request.method"), (val, spanIndex) -> spanIndex.setHttpMethod(val)),
    httpHost(Lists.newArrayList("http.host", "server.address", "client.address", "http.request.header.host"), (val, spanIndex) -> spanIndex.setHttpHost(val)),
    httpRoute(Lists.newArrayList("http.route"), (val, spanIndex) -> spanIndex.setHttpRoute(val)),
    msgSystem(Lists.newArrayList("messaging.system"), (val, spanIndex) -> spanIndex.setMsgSystem(val)),
    msgOperation(Lists.newArrayList("messaging.operation"), (val, spanIndex) -> spanIndex.setMsgOperation(val)),
    dbSystem(Lists.newArrayList("db.system"), (val, spanIndex) -> spanIndex.setDbSystem(val)),
    dbName(Lists.newArrayList("db.name"), (val, spanIndex) -> spanIndex.setDbName(val)),
    dbOperation(Lists.newArrayList("db.operation"), (val, spanIndex) -> spanIndex.setDbOperation(val)),
    peerService(Lists.newArrayList("peer.service"), (val, spanIndex) -> spanIndex.setPeerService(val)),
    rpcSystem(Lists.newArrayList("rpc.system"), (val, spanIndex) -> spanIndex.setRpcSystem(val)),
    rpcService(Lists.newArrayList("rpc.service"), (val, spanIndex) -> spanIndex.setRpcService(val)),
    rpcMethod(Lists.newArrayList("rpc.method"), (val, spanIndex) -> spanIndex.setRpcMethod(val)),
    serviceName(Lists.newArrayList("service.name"), (val, spanIndex) -> {
        spanIndex.setServiceName(val);
        spanIndex.getTraceModel().setServiceName(val);
    });

    private final List<String> sourceFields;
    private final BiConsumer<String, SpanIndex> mappingConsumer;

    ColumData(List<String> sourceFields, BiConsumer<String, SpanIndex> mappingConsumer) {
        this.sourceFields = sourceFields;
        this.mappingConsumer = mappingConsumer;
    }

    public void mapSourceToTarget(Map<String, String> tagMap, SpanIndex spanIndex) {
        for (String sourceField : sourceFields) {
            String value = tagMap.get(sourceField);
            if (value != null) {
                mappingConsumer.accept(value, spanIndex);
                String remove = spanIndex.getStringTagMap().remove(sourceField);
                if (remove == null) {
                    spanIndex.getNumberTagMap().remove(sourceField);
                }
                break;
            }
        }
    }
}
