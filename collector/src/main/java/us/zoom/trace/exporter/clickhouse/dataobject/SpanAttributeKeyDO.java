package us.zoom.trace.exporter.clickhouse.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @author: eason.jia
 * @date: 2024/8/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpanAttributeKeyDO {
    private Timestamp time;
    private String tagKey;
    private Integer tagType;
    private Integer dataType;
    private Boolean isColumn;
    private Boolean isLargeCardinality;
}