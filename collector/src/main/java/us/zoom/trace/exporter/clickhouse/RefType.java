package us.zoom.trace.exporter.clickhouse;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: eason.jia
 * @date: 2024/8/7
 */
public enum RefType {

    CHILD_OF("CHILD_OF"),
    FOLLOWS_FROM("FOLLOWS_FROM"),
    ;

    String type;

    RefType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static RefType of(String type) {
        for (RefType refType : RefType.values()) {
            if (StringUtils.equals(refType.getType(), type)) {
                return refType;
            }
        }
        return null;
    }
}
