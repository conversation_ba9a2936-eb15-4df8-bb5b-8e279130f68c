package us.zoom.trace.exporter.clickhouse.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceIndexDO {
    private Timestamp time;
    private Long timestamp;
    private String traceID;
    private String spanID;
    private String parentSpanID;
    private String serviceName;
    private String name;
    private Integer kind;
    private Long durationNano;
    private Integer statusCode;
    private String component;
    private String dbSystem;
    private String dbName;
    private String dbOperation;
    private String peerService;
    private List<String> events;
    private String httpMethod;
    private String httpUrl;
    private String externalHttpUrl;
    private String externalHttpMethod;
    private String httpRoute;
    private String httpHost;
    private String msgSystem;
    private String msgOperation;
    private Boolean hasError;
    private String rpcSystem;
    private String rpcService;
    private String rpcMethod;
    private String responseStatusCode;
    private String statusCodeString;
    private String spanKind;
    private Map<String, String> stringTagMap;
    private Map<String, Double> numberTagMap;
    private Map<String, Boolean> boolTagMap;
    private Map<String, String> resourceTagsMap;
}