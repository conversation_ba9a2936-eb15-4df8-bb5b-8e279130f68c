package us.zoom.trace.exporter.clickhouse.model;

import com.google.common.hash.BloomFilter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TraceAttributeBloomFilter {
    private BloomFilter<String> filter;
    private long startTime;
    private long endTime;
    private int expectedSize;
}
