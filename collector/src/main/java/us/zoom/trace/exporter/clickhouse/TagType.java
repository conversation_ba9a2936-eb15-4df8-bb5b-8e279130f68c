package us.zoom.trace.exporter.clickhouse;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: eason.jia
 * @date: 2024/8/7
 */
public enum TagType {

    TAG(1, "tag"),
    RESOURCE(2, "resource"),
    ;

    int code;
    String desc;

    TagType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static TagType of(String desc) {
        for (TagType tagType : TagType.values()) {
            if (StringUtils.equals(tagType.getDesc(), desc)) {
                return tagType;
            }
        }
        return null;
    }
}
