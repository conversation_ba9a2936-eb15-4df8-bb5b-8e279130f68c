package us.zoom.trace.exporter.clickhouse;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.Span;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.clickhouse.ClickhouseWriter;
import us.zoom.trace.biz.metric.meta.extractor.CombinedMetricExtractor;
import us.zoom.trace.common.BaseREDMetrics;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.SystemParamObserver;
import us.zoom.trace.common.config.TraceSystemParamCache;
import us.zoom.trace.exporter.Exporter;
import us.zoom.trace.exporter.clickhouse.model.SpanIndex;
import us.zoom.trace.monitor.MetricReporterManager;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.LogRateLimiter;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static us.zoom.trace.common.Constants.ALL_APPLICATION_MARK;
import static us.zoom.trace.common.Constants.DATABASE_NAME;
import static us.zoom.trace.common.Constants.LONG_TIME_STORAGE_TABLE_SUFFIX;
import static us.zoom.trace.common.Constants.TABLE_SPAN;
import static us.zoom.trace.common.Constants.TABLE_SPAN_ATTRIBUTES;
import static us.zoom.trace.common.Constants.TABLE_SPAN_ATTRIBUTES_KEYS;
import static us.zoom.trace.common.Constants.TABLE_TRACE_ERROR_INDEX;
import static us.zoom.trace.common.Constants.TABLE_TRACE_INDEX;
import static us.zoom.trace.common.Constants.TRACE_WHITE_LIST_PARAM;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
@Order(1)
@Component("clickHouseExporter")
public class ClickHouseExporter implements Exporter, SystemParamObserver {

    private static final Logger logger = LoggerFactory.getLogger(ClickHouseExporter.class);

    @Autowired
    private ClickhouseWriter clickhouseWriter;

    @Autowired
    private LogRateLimiter logRateLimiter;

    @Value("${cube.trace.consume.delay.enable:false}")
    private boolean delayConsume;

    @Autowired
    private CombinedMetricExtractor metricExtractor;

    private String tableSuffix = StringUtils.EMPTY;

    private Set<String> tableWhiteList = Sets.newHashSet(ALL_APPLICATION_MARK);

    @PostConstruct
    public void init() {
        if (delayConsume) {
            tableSuffix = LONG_TIME_STORAGE_TABLE_SUFFIX;
        }
        TraceSystemParamCache.addObserver(this);
    }

    @Override
    public boolean export(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) {
        try {
            SpanIndex spanIndex = SpanBuilder.buildSpan(otelSpan, attributeMap, otelScope, otelResource);
            Map<String, Object> traceIndexRow = ClickHouseModelConvertUtils.toTraceIndexRow(spanIndex);
            writeToClickHouseWithDefaultArgs(TABLE_TRACE_INDEX + tableSuffix, traceIndexRow, spanIndex.getStartTimeUnixNano());
            // spanIndex
            Map<String, Object> spanRow = ClickHouseModelConvertUtils.toSpanRow(spanIndex);
            // can't store in different tables
            //todo use custom id generator
            writeToClickHouseWithDefaultArgs(TABLE_SPAN + tableSuffix, spanRow, spanIndex.getStartTimeUnixNano());
            if (delayConsume) {
                return true;
            }
            reportREDMetrics(spanIndex, context);
            // error_index
            String encodeClickhouseName = ClickhouseSqlUtil.encodeClickhouseName(spanIndex.getServiceName());
            Map<String, Object> traceIndexErrorRow = ClickHouseModelConvertUtils.toTraceIndexErrorRow(spanIndex);
            writeToClickHouseWithDefaultArgs(TABLE_TRACE_ERROR_INDEX + "_" + encodeClickhouseName, traceIndexErrorRow, spanIndex.getStartTimeUnixNano());
            List<Map<String, Map<String, Object>>> spanAttributeKeyAndValueRowList = ClickHouseModelConvertUtils.toSpanAttributeKeyAndValueRow(spanIndex);
            // attribute key and value
            for (Map<String, Map<String, Object>> spanAttributeKeyAndValueMap : spanAttributeKeyAndValueRowList) {
                writeToClickHouseWithDefaultArgs(TABLE_SPAN_ATTRIBUTES_KEYS + "_" + encodeClickhouseName, spanAttributeKeyAndValueMap.get(TABLE_SPAN_ATTRIBUTES_KEYS), spanIndex.getStartTimeUnixNano());
                writeToClickHouseWithDefaultArgs(TABLE_SPAN_ATTRIBUTES + "_" + encodeClickhouseName, spanAttributeKeyAndValueMap.get(TABLE_SPAN_ATTRIBUTES), spanIndex.getStartTimeUnixNano());
            }
            return true;
        } catch (Throwable throwable) {
            if (logRateLimiter.getPermitByKey(this.getClass().getName())) {
                logger.error("failed to process data: {}", JsonUtils.toJsonStringIgnoreException(otelSpan), throwable);
            }
            return false;
        }
    }

    private void writeToClickHouseWithDefaultArgs(String table, Map<String, Object> row, long nanoTime) {
        if (row == null || row.isEmpty()) {
            return;
        }
        clickhouseWriter.write(DATABASE_NAME, table, row, nanoToTimestamp(nanoTime), 100, false, false);
    }

    private Timestamp nanoToTimestamp(long nanoTime) {
        return new Timestamp(TimeUnit.NANOSECONDS.toMillis(nanoTime));
    }

    private void reportREDMetrics(SpanIndex spanIndex, Context context) {
        if (!tableWhiteList.contains(spanIndex.getServiceName()) && !tableWhiteList.contains(ALL_APPLICATION_MARK)) {
            return;
        }
        BaseREDMetrics extract = metricExtractor.extract(spanIndex, context);
        if (extract != null) {
            BaseREDMetrics redMetrics = MetricReporterManager.registerDelayMetricIfAbsent(extract);
            long durationNanoTime = spanIndex.getDurationNano();
            byte errorStatus = spanIndex.isHasError() ? (byte) 1 : (byte) 0;
            redMetrics.update(metric -> {
                BaseREDMetrics m = (BaseREDMetrics) metric;
                m.getDurationList().add(durationNanoTime);
                m.getStatusList().add(errorStatus);
            });
        }
    }

    @Override
    public String getObservedKey() {
        return TRACE_WHITE_LIST_PARAM;
    }

    @Override
    public void onChange(String param) {
        tableWhiteList = JsonUtils.parseWithIgnoreError(param, new TypeReference<Set<String>>() {
        }, Collections.emptySet());
    }
}
