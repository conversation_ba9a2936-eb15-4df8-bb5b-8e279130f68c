package us.zoom.trace.exporter.clickhouse;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import us.zoom.infra.clickhouse.ClickhouseEnvProxy;
import us.zoom.infra.clickhouse.ClickhouseWriter;

/**
 * <AUTHOR>
 */
@Configuration
public class ClickHouseConfiguration {

    private static final String UNIT_PREFIX = "trace_";

    @Value("${cube.trace.environment}")
    private String environmentName;

    @Value("${cube.clickhouse.flush.major.interval.millsecond:10000}")
    private long flushMajorIntervalMillSecond;

    @Value("${cube.clickhouse.flush.major.interval.high.speed.millsecond:3000}")
    private long flushMajorIntervalForHighSpeedMillSecond;

    @Value("${cube.clickhouse.flush.minor.interval.millsecond:5000}")
    private long flushMinorIntervalMillSecond;

    @Value("${cube.clickhouse.flush.max.batch.size:100000}")
    private int flushMaxRecordCount;

    @Value("${cube.clickhouse.flush.min.batch.size:100}")
    private int flushMinRecordCountPerTable;

    @Value("${cube.clickhouse.thread.pool.size:2}")
    private int threadPoolSize;

    @Value("${cube.clickhouse.flush.parallelism:4}")
    private int flushParallelism;

    @Value("${cube.clickhouse.queue.memory.limit:5368709120}")
    private long queueMemoryLimit;

    @Value("${cube.clickhouse.single.table.limit:209715200}")
    private long singleTableMemoryLimit;

    @Value("${cube.clickhouse.queue.size.limit:1500000}")
    private int queueMax;

    @Bean
    public ClickhouseEnvProxy clickhouseEnvProxy() {
        return new ClickhouseEnvProxy(environmentName);
    }

    @Bean
    public ClickhouseWriter clickhouseWriter(ClickhouseEnvProxy clickhouseEnvProxy) {
        ClickhouseWriter.setUnitTag(UNIT_PREFIX + environmentName);
        return new ClickhouseWriter(clickhouseEnvProxy)
                .setFlushMajorIntervalMillSecond(flushMajorIntervalMillSecond)
                .setFlushMinorIntervalMillSecond(flushMinorIntervalMillSecond)
                .setFlushMaxRecordCount(flushMaxRecordCount)
                .setFlushMinRecordCountPerTable(flushMinRecordCountPerTable)
                .setThreadPoolSize(threadPoolSize)
                .setFlushParallelism(flushParallelism)
                .setQueueMemoryLimit(queueMemoryLimit)
                .setSingleTableMemoryLimit(singleTableMemoryLimit)
                .setQueueSizeMax(queueMax)
                .start();
    }
}
