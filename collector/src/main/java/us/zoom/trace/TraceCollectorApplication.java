package us.zoom.trace;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
import us.zoom.cloud.secrets.spring.context.CSMSBeanDefinitionRegistrar;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
@EnableScheduling
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@Import({CSMSBeanDefinitionRegistrar.class})
public class TraceCollectorApplication {
    public static void main(String[] args) {
        SpringApplication.run(TraceCollectorApplication.class, args);
    }
}