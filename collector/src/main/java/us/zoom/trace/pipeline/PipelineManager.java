package us.zoom.trace.pipeline;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import io.opentelemetry.proto.trace.v1.ResourceSpans;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.trace.common.Context;
import us.zoom.trace.exporter.Exporter;
import us.zoom.trace.processor.Processor;
import us.zoom.trace.receiver.ReceiverType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static us.zoom.trace.pipeline.PipelineType.TRACE;
import static us.zoom.trace.receiver.ReceiverType.ASYNCMQ;

/**
 * @author: eason.jia
 * @date: 2024/8/2
 */
@Component
public class PipelineManager {

    private static final Logger log = LoggerFactory.getLogger(PipelineManager.class);

    private Table<ReceiverType, PipelineType, Pipeline> pipelineTable = HashBasedTable.create();

    private Pipeline tracePipeline;

    /**
     * when constructing the pipeline, the receivers and exporters are already determined
     */
    @Autowired
    public PipelineManager(List<Processor> processors, List<Exporter> exporters) {
        // default pipeline, maybe is the only pipeline
        Pipeline.PipelineBuilder builder = Pipeline.builder()
                .setPipelineType(TRACE)
                .addReceiverType(ASYNCMQ);
        processors.forEach(builder::addProcessor);
        exporters.forEach(builder::addExporter);
        tracePipeline = builder.build();
        pipelineTable.put(ASYNCMQ, tracePipeline.getPipelineType(), tracePipeline);
    }

    public void process(Context context, List<ResourceSpans> traceDataList) {
        List<Pipeline> pipeline = getPipeline(context.getReceiverType());
        traceDataList.forEach(traceData ->
                pipeline.forEach(pp -> processOne(traceData, pp, context)));
    }

    private void processOne(ResourceSpans traceData, Pipeline pipeline, Context context) {
        try {
            pipeline.process(context, traceData);
        } catch (Throwable e) {
            log.error("transform trace data failed", e);
        }
    }

    public List<Pipeline> getPipeline(ReceiverType receiverType) {
        if (receiverType == null) {
            throw new IllegalArgumentException("receiverType is null");
        }

        return Optional.ofNullable(pipelineTable.row(receiverType))
                .map(Map::values)
                .map(Lists::newArrayList)
                .orElseGet(ArrayList::new);
    }

    public Pipeline getPipeline(PipelineType pipelineType) {
        if (pipelineType == null) {
            throw new IllegalArgumentException("pipelineType is null");
        }

        return Optional.ofNullable(pipelineTable.column(pipelineType))
                .map(Map::values)
                .map(collection -> collection.stream().findFirst().orElse(null))
                .orElse(null);
//        return switch (pipelineType) {
//            case TRACE -> tracePipeline;
//        };
    }
}