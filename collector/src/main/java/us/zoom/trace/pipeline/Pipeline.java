package us.zoom.trace.pipeline;

import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.common.v1.KeyValue;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.ResourceSpans;
import io.opentelemetry.proto.trace.v1.ScopeSpans;
import io.opentelemetry.proto.trace.v1.Span;
import lombok.Getter;
import us.zoom.trace.common.Context;
import us.zoom.trace.exporter.Exporter;
import us.zoom.trace.processor.Processor;
import us.zoom.trace.receiver.ReceiverType;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;

/**
 * processor chain, one by one
 *
 * @author: eason.jia
 * @date: 2024/7/31
 */
public class Pipeline {

    @Getter
    private PipelineType pipelineType;
    @Getter
    private Set<ReceiverType> receiverTypes;
    @Getter
    private List<Exporter> exporters;
    @Getter
    private Queue<Processor> processors;

    public Pipeline(PipelineType pipelineType, Set<ReceiverType> receiverTypes, List<Exporter> exporters, Queue<Processor> processors) {
        this.pipelineType = pipelineType;
        this.receiverTypes = receiverTypes;
        this.exporters = exporters;
        this.processors = processors;
    }

    public static PipelineBuilder builder() {
        return new PipelineBuilder();
    }

    public Pipeline addLast(Processor handler) {
        processors.offer(handler);
        return this;
    }

    public int size() {
        return processors.size();
    }

    protected void preProcess(Context context, ResourceSpans traceData) {
        context.setPipelineStartTime(Instant.now().toEpochMilli());
    }

    public void process(Context context, ResourceSpans traceData) {
        preProcess(context, traceData);
        // start processing
        Resource resource = traceData.getResource();
        List<ScopeSpans> scopeSpans = traceData.getScopeSpansList();
        scopeSpans.forEach(scopeSpan -> {
            InstrumentationScope scope = scopeSpan.getScope();
            List<Span> spans = scopeSpan.getSpansList();
            spans.forEach(span -> {
                Map<String, AnyValue> attributeMap = new HashMap<>(span.getAttributesCount());
                for (KeyValue keyValue : span.getAttributesList()) {
                    attributeMap.put(keyValue.getKey(), keyValue.getValue());
                }
                for (Processor processor : processors) {
                    boolean success = false;
                    try {
                        processor.preProcess(context, span, attributeMap, scope, resource);
                        success = processor.process(context, span, attributeMap, scope, resource);
                        if (!success) {
                            processor.onFalse(context, span, attributeMap, scope, resource);
                        }
                        processor.postProcess(context, span, attributeMap, scope, resource);
                    } catch (Throwable cause) {
                        processor.onException(context, span, attributeMap, scope, resource, cause);
                    } finally {
                        processor.onFinally(context, span, attributeMap, scope, resource);
                    }
                    if (!success && processor.needSkipWhenFailed()) {
                        return;
                    }
                }
                // export
                exporters.forEach(exporter -> exporter.export(context, span, attributeMap, scope, resource));
            });
        });
        postProcess(context, traceData);
    }

    private void postProcess(Context context, ResourceSpans traceData) {
        context.setPipelineEndTime(Instant.now().toEpochMilli());
    }

    public static class PipelineBuilder {
        private PipelineType pipelineType;
        private Set<ReceiverType> receiverTypes;
        private List<Exporter> exporters;
        private Queue<Processor> processors;

        PipelineBuilder() {
        }

        public PipelineBuilder setPipelineType(PipelineType pipelineType) {
            this.pipelineType = pipelineType;
            return this;
        }

        public PipelineBuilder addReceiverType(ReceiverType receiverType) {
            if (receiverTypes == null) {
                receiverTypes = new HashSet<>();
            }
            receiverTypes.add(receiverType);
            return this;
        }

        public PipelineBuilder addExporter(Exporter exporter) {
            if (exporters == null) {
                exporters = new ArrayList<>();
            }
            exporters.add(exporter);
            return this;
        }

        public PipelineBuilder addProcessor(Processor processor) {
            if (processors == null) {
                processors = new LinkedList<>();
            }
            processors.offer(processor);
            return this;
        }

        public Pipeline build() {
            if (processors == null) {
                processors = new LinkedList<>();
            }
            if (exporters == null) {
                exporters = new LinkedList<>();
            }
            return new Pipeline(pipelineType, receiverTypes, exporters, processors);
        }
    }
}
