package us.zoom.trace.monitor;

import com.google.common.collect.Lists;
import lombok.Builder;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.trace.common.Constants;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.MonitorLogUtils;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static us.zoom.trace.common.Constants.CUBE_METRIC_LOG_VERSION;

/**
 * <AUTHOR>
 */
public class ConsumeMetric extends AbstractDelayMetric {

    private String exceptionType;
    private String topicName;
    private AtomicInteger errorCount;
    private volatile String stackTraceSample;

    @Builder
    public ConsumeMetric(long delaySeconds, String exceptionType, String topicName,
                         AtomicInteger errorCount) {
        setDelaySeconds(delaySeconds);
        this.exceptionType = exceptionType;
        this.topicName = topicName;
        this.errorCount = errorCount;
    }

    @Override
    public void doRefresh() {
        errorCount.set(0);
        stackTraceSample = null;
    }

    @Override
    public String metricType() {
        return MetricType.consume_trace_topic_metric.name();
    }

    @Override
    public void report() {
        MonitorLog.MonitorLogBuilder monitorLogBuilder = MonitorLog.builder()
                .withMeasure(metricType())
                .withCubeVer(CUBE_METRIC_LOG_VERSION)
                .addTag("topicName", topicName)
                .addTag("exceptionType", exceptionType)
                .addField("count", errorCount.get())
                .addField("stackTraceSample", stackTraceSample)
                .withTs(System.currentTimeMillis());
        MonitorLogUtils.getLogger().info(JsonUtils.toJsonStringIgnoreException(monitorLogBuilder.build()));
    }

    @Override
    public List<Object> buildKeyGroup() {
        return Lists.newArrayList(topicName, exceptionType);
    }

    public String getStackTraceSample() {
        return stackTraceSample;
    }

    public void setStackTraceSample(String stackTraceSample) {
        this.stackTraceSample = stackTraceSample;
    }

    public String getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public AtomicInteger getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(AtomicInteger errorCount) {
        this.errorCount = errorCount;
    }
}
