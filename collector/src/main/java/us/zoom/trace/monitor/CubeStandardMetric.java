package us.zoom.trace.monitor;

import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.MonitorLogUtils;

import java.util.HashMap;
import java.util.Map;

import static us.zoom.trace.common.Constants.CUBE_METRIC_LOG_VERSION;

/**
 * <AUTHOR>
 */
public class CubeStandardMetric extends BasicMetric {


    private Map<String, Object> tags;
    private Map<String, Object> fields;
    private String measure;

    public CubeStandardMetric(String measure) {
        super();
        this.measure = measure;
    }

    @Override
    public void report() {
        MonitorLogUtils.getLogger().info(JsonUtils.toJsonStringIgnoreException(MonitorLog.builder()
                .withMeasure(measure)
                .withCubeVer(CUBE_METRIC_LOG_VERSION)
                .withTs(ts)
                .withNewTags(tags)
                .withNewFields(fields)
                .build()));
    }

    public CubeStandardMetric withField(String key, Object value) {
        if (fields == null) {
            fields = new HashMap<>();
        }
        fields.put(key, value);
        return this;
    }


    public CubeStandardMetric withTag(String key, Object value) {
        if (tags == null) {
            tags = new HashMap<>();
        }
        tags.put(key, value);
        return this;
    }
}
