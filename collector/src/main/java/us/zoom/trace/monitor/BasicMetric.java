package us.zoom.trace.monitor;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * <AUTHOR>
 */
public abstract class BasicMetric {

    @JsonIgnore
    protected long ts;

    protected BasicMetric() {
        ts = System.currentTimeMillis();
    }

    public abstract void report();

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }
}
