package us.zoom.trace.monitor;

import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Getter;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.trace.common.Constants;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.MonitorLogUtils;

import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

import static us.zoom.trace.common.Constants.CUBE_METRIC_LOG_VERSION;

/**
 * <AUTHOR>
 */
public class PayloadSizeMetric extends AbstractDelayMetric {

    private long periodTime;
    private String topicName;
    private String payloadType;
    @Getter
    private AtomicLong sizeCounter;

    @Builder
    public PayloadSizeMetric(long periodTime, long delaySeconds, String topicName, AtomicLong sizeCounter, String payloadType) {
        setDelaySeconds(delaySeconds);
        this.topicName = topicName;
        this.periodTime = periodTime;
        this.sizeCounter = sizeCounter;
        this.payloadType = payloadType;
    }

    @Override
    public void doRefresh() {
        sizeCounter.set(0);
    }

    @Override
    public List<Object> buildKeyGroup() {
        return Lists.newArrayList(periodTime, topicName, payloadType);
    }

    @Override
    public String metricType() {
        return "payload_size_metric";
    }

    @Override
    public void report() {
        MonitorLog.MonitorLogBuilder monitorLogBuilder = MonitorLog.builder()
                .withMeasure(metricType())
                .withCubeVer(CUBE_METRIC_LOG_VERSION)
                .addField("periodTime", periodTime)
                .addField("sizeCount", sizeCounter.get())
                .addTag("topicName", topicName)
                .addTag("payloadType", payloadType)
                .withTs(System.currentTimeMillis());
        MonitorLogUtils.getLogger().info(JsonUtils.toJsonStringIgnoreException(monitorLogBuilder.build()));
    }
}
