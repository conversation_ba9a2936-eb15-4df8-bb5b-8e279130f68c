package us.zoom.trace.monitor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import us.zoom.monitor.auto.DefaultServletRequestMetricsFilter;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 06/12/2024 11:18
 * @Description:
 */
@Configuration
public class ApmServletRequestMetricsFilter {

    @Bean
    public DefaultServletRequestMetricsFilter defaultServletRequestMetricsFilter() {
        return new DefaultServletRequestMetricsFilter();
    }
}
