package us.zoom.trace.monitor;

import com.google.common.collect.Lists;
import lombok.Builder;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.trace.common.Constants;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.MonitorLogUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static us.zoom.trace.common.Constants.CUBE_METRIC_LOG_VERSION;

public class RuleMatcherMetric extends AbstractDelayMetric {

    private final Map<String, AtomicInteger> ruleCounters;
    private long periodTime;

    @Builder
    public RuleMatcherMetric(long periodTime, long delaySeconds, Map<String, AtomicInteger> ruleCounters) {
        setDelaySeconds(delaySeconds);
        this.ruleCounters = ruleCounters;
        this.periodTime = periodTime;
    }

    @Override
    public void doRefresh() {
        this.ruleCounters.clear();
    }

    @Override
    public void report() {
        MonitorLog.MonitorLogBuilder monitorLogBuilder = MonitorLog.builder()
                .withMeasure(metricType())
                .withCubeVer(CUBE_METRIC_LOG_VERSION)
                .addField("periodTime", periodTime)
                .withTs(System.currentTimeMillis());
        ruleCounters.forEach((k, v) -> {
            monitorLogBuilder.addField(k + Constants.COMMON_SEPARATOR + "count", v.get());
        });
        MonitorLogUtils.getLogger().info(JsonUtils.toJsonStringIgnoreException(monitorLogBuilder.build()));
    }

    @Override
    public String metricType() {
        return MetricType.long_time_storage_metric.name();
    }

    public Map<String, AtomicInteger> getRuleCounters() {
        return ruleCounters;
    }

    @Override
    public List<Object> buildKeyGroup() {
        return Lists.newArrayList(periodTime);
    }
}