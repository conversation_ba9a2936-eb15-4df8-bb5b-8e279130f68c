package us.zoom.trace.monitor;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.google.common.collect.Maps;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import us.zoom.infra.thread.NamedThreadFactory;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MetricReporterManager {

    private static final long MAX_TERMINATE_TIME = 2 * 60 * 1000;

    private static final int DEFAULT_CACHE_EXPIRE_TIME_SECONDS = 600;
    private static final int DEFAULT_CACHE_STATICS_TIME_SECONDS = 120;

    private static final Map<String, Cache<String, BasicMetric>> metricMap = Maps.newConcurrentMap();

    private static final ScheduledExecutorService scheduledExecutorService = new ScheduledThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2, new NamedThreadFactory("MetricReporterManager"));

    private static final ScheduledExecutorService cacheStatusReporter = new ScheduledThreadPoolExecutor(1, new NamedThreadFactory("MetricCacheStatusReporterService"));

    @PostConstruct
    public void init() {
        cacheStatusReporter.scheduleWithFixedDelay(() -> metricMap.forEach(this::StaticsCacheStats), DEFAULT_CACHE_STATICS_TIME_SECONDS, DEFAULT_CACHE_STATICS_TIME_SECONDS, TimeUnit.SECONDS);
    }

    public static <T extends AbstractDelayMetric> T registerDelayMetricIfAbsent(T metric) {
        Cache<String, BasicMetric> cache = metricMap.get(metric.metricType());
        if (cache == null) {
            cache = metricMap.computeIfAbsent(metric.metricType(), k -> buildMetricCache());
        }
        BasicMetric actualMetric = cache.getIfPresent(metric.getKeyGroup());
        if (actualMetric == null) {
            actualMetric = cache.get(metric.getKeyGroup(), k -> {
                scheduledExecutorService.schedule(metric, metric.getDelaySeconds(), TimeUnit.SECONDS);
                return metric;
            });
        }
        return (T) actualMetric;
    }

    public static void removeDelayMetric(String metricType, String id) {
        try {
            if (StringUtils.isEmpty(metricType) || StringUtils.isEmpty(id)) {
                return;
            }
            Optional.ofNullable(metricMap.get(metricType)).ifPresent(m -> m.invalidate(id));
        } catch (Throwable e) {
            log.error("Error removing metric {}", id, e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            log.info("start shutdown MetricReporterManager");
            scheduledExecutorService.shutdown();
            boolean result = scheduledExecutorService.awaitTermination(MAX_TERMINATE_TIME, TimeUnit.MILLISECONDS);
            log.info("MetricReporterManager shutdown completed. Result: {}", result);
        } catch (InterruptedException e) {
            log.error("MetricReporterManager shutdown interrupted", e);
        }
    }

    private static Cache<String, BasicMetric> buildMetricCache() {
        return Caffeine.newBuilder()
                .expireAfterAccess(DEFAULT_CACHE_EXPIRE_TIME_SECONDS, TimeUnit.SECONDS)
                .recordStats()
                .build();
    }

    private void StaticsCacheStats(String metricType, Cache<String, BasicMetric> cache) {
        try {
            CacheStats stats = cache.stats();
            CubeStandardMetric statics = new CubeStandardMetric("metrics_cache_statics")
                    .withTag("cacheType", metricType)
                    .withField("hitCount", stats.hitCount())
                    .withField("missCount", stats.missCount())
                    .withField("evictionCount", stats.evictionCount())
                    .withField("hitRate", stats.hitRate())
                    .withField("missRate", stats.missRate())
                    .withField("totalLoadTime", stats.totalLoadTime())
                    .withField("requestCount", stats.requestCount())
                    .withField("averageLoadPenalty", stats.averageLoadPenalty())
                    .withField("evictionWeight", stats.evictionWeight())
                    .withField("loadCount", stats.loadCount())
                    .withField("loadFailureCount", stats.loadFailureCount())
                    .withField("loadSuccessCount", stats.loadSuccessCount())
                    .withField("loadFailureRate", stats.loadFailureRate());
            statics.report();
            log.info("Statics cache {} completed", metricType);
        } catch (Throwable e) {
            log.error("Error statics cache {}", metricType, e);
        }
    }


}
