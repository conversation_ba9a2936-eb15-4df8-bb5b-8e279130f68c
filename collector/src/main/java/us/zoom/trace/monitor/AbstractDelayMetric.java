package us.zoom.trace.monitor;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import us.zoom.trace.util.JsonUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractDelayMetric extends BasicMetric implements Runnable {

    @JsonIgnore
    private String keyGroup;

    @JsonIgnore
    private long delaySeconds;

    @JsonIgnore
    private volatile boolean ended;

    private volatile boolean refreshed;

    @JsonIgnore
    private final ReadWriteLock lock = new ReentrantReadWriteLock();

    public AbstractDelayMetric() {
    }

    public abstract void doRefresh();

    public abstract List<Object> buildKeyGroup();

    public abstract String metricType();

    @Override
    public void run() {
        Lock writeLock = lock.writeLock();
        writeLock.lock();
        try {
            report();
        } catch (Throwable e) {
            log.error("report metric failed, metric: {}", JsonUtils.toJsonStringIgnoreException(this), e);
        } finally {
            ended = true;
            refreshed = false;
            MetricReporterManager.removeDelayMetric(metricType(), keyGroup);
            writeLock.unlock();
        }
    }


    public void update(Consumer<AbstractDelayMetric> consumer) {
        Lock readLock = lock.readLock();
        readLock.lock();
        try {
            AbstractDelayMetric metric = this;
            if (ended) {
                refresh();
                metric = MetricReporterManager.registerDelayMetricIfAbsent(this);
            }
            consumer.accept(metric);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        } finally {
            readLock.unlock();
        }
    }

    private void refresh() {
        if (!refreshed) {
            synchronized (this) {
                if (!refreshed) {
                    doRefresh();
                    refreshed = true;
                }
            }
        }
    }

    public String getKeyGroup() {
        if (keyGroup == null) {
            List<Object> group = buildKeyGroup();
            if (CollectionUtils.isNotEmpty(group)) {
                keyGroup = Joiner.on("_").join(group.stream().filter(Objects::nonNull).toList());
            } else {
                keyGroup = StringUtils.EMPTY;
            }
        }
        return keyGroup;
    }

    public long getDelaySeconds() {
        return delaySeconds;
    }

    public void setDelaySeconds(long delaySeconds) {
        this.delaySeconds = delaySeconds;
    }
}
