package us.zoom.trace.asyncmq;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import us.zoom.mq.common.entity.TaskEntity;
import us.zoom.trace.receiver.Receiver;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
@Component("traceDataHandler")
public class TraceDataHandler implements AsyncMqMessageHandler {

    @Autowired
    @Qualifier("asyncMqReceiver")
    private Receiver<TaskEntity<byte[]>, HandleStatus> asyncMqReceiver;

    @Override
    public HandleStatus onMessage(TaskEntity<byte[]> taskEntity) {
        return asyncMqReceiver.receive(taskEntity);
    }
}
