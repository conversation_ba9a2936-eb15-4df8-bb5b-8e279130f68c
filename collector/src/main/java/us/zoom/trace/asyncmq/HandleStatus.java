package us.zoom.trace.asyncmq;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
public enum HandleStatus {

    /**
     * message processing succeeded
     */
    SUCCESS(1, "success"),

    /**
     * need to retry processing message
     */
    RETRY(3, "retry"),

    ;

    private int code;
    private String desc;

    HandleStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
