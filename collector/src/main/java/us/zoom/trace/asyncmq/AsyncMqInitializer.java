package us.zoom.trace.asyncmq;

import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import us.zoom.cube.lib.trace.TraceConfig;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.trace.common.config.TraceConfigCache;
import us.zoom.trace.util.DateUtils;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.MonitorLogUtils;

import java.time.Instant;
import java.util.Date;
import java.util.Map;

import static us.zoom.trace.common.Constants.CUBE_METRIC_LOG_VERSION;
import static us.zoom.trace.common.Constants.MEASURE_LOAD_TRACE_CONFIG;
import static us.zoom.trace.util.ExceptionStackUtils.parseExceptionStackToString;
import static us.zoom.trace.util.ThreadPoolExecutorUtils.getConsumeTraceDataThreadPoolExecutor;

/**
 * @author: eason.jia
 * @date: 2024/8/5
 */
@Service
public class AsyncMqInitializer {

    private Logger logger = LoggerFactory.getLogger(AsyncMqInitializer.class);

    private static final Map<String, TraceConfig> consumeConfigMapping = Maps.newHashMap();

    private static final String MONITOR_LOAD_TRACE_CONFIG_EXCEPTION = "loadTraceConfigException";
    private static final String MONITOR_LOAD_TRACE_CONFIG_SUCCESS = "loadTraceConfigSuccess";

    @Value("${async.mq.endpoint}")
    private String endpoint;
    @Value("${async.mq.username}")
    private String username;
    @Value("${async.mq.password}")
    private String password;
    @Autowired
    @Qualifier("traceDataHandler")
    private AsyncMqMessageHandler traceDataHandler;

    @Value("${cube.trace.environment}")
    private String environment;

    @Value("${cube.trace.consume.delay.time:0}")
    private long consumeDelayMs;

    @Value("${cube.trace.consume.delay.enable:false}")
    private boolean delayConsume;

    @PostConstruct
    public void init() {
        AsyncMqInstance.init(endpoint, username, password, environment, consumeDelayMs, delayConsume);
        AsyncMqInstance.getInstance().registerRotateHandler(username);
    }

    public void refreshTraceConfig(String unitTag) {
        long startTime = System.currentTimeMillis();
        try {
            logger.info("update asyncmq consumer, unit tag: {}", unitTag);
            updateConsumer();
            printLoadTraceConfigMonitorLog(MONITOR_LOAD_TRACE_CONFIG_SUCCESS, System.currentTimeMillis() - startTime, "", "");
        } catch (Exception e) {
            printLoadTraceConfigMonitorLog(MONITOR_LOAD_TRACE_CONFIG_EXCEPTION, System.currentTimeMillis() - startTime, e.getMessage(), parseExceptionStackToString(e));
            logger.error("failed to update asyncmq consumer, unit tag: {}", unitTag, e);
        }
    }


    private void updateConsumer() {
        Map<String, TraceConfig> remoteConsumeConfigMapping = TraceConfigCache.getTopicConfigMapping();
        MapDifference<String, Boolean> difference = Maps.difference(Maps.asMap(remoteConsumeConfigMapping.keySet(), k -> true),
                Maps.asMap(consumeConfigMapping.keySet(), k -> true));
        Map<String, Boolean> needToShutdownConsumer = difference.entriesOnlyOnRight();
        needToShutdownConsumer.keySet().forEach(topic -> shutdownConsumer(consumeConfigMapping.remove(topic)));
        Map<String, Boolean> needToStartConsumer = difference.entriesOnlyOnLeft();
        needToStartConsumer.keySet().forEach(topic -> {
            TraceConfig traceConfig = remoteConsumeConfigMapping.get(topic);
            startConsumer(traceConfig);
            consumeConfigMapping.put(topic, traceConfig);
        });
        Map<String, Boolean> needToUpdateConsumer = difference.entriesInCommon();
        needToUpdateConsumer.keySet().forEach(topic -> {
            TraceConfig remoteTraceConfig = remoteConsumeConfigMapping.get(topic);
            TraceConfig currentTraceConfig = consumeConfigMapping.get(topic);
            if (remoteTraceConfig.getGmtModify().after(currentTraceConfig.getGmtModify())) {
                restartConsumer(remoteTraceConfig);
                consumeConfigMapping.put(topic, remoteTraceConfig);
            }
        });
    }

    private void startConsumer(TraceConfig traceConfig) {
        CommonConsumer consumer = new TraceDataConsumer(traceConfig.getTopic(), traceDataHandler, getConsumeTraceDataThreadPoolExecutor());
        AsyncMqInstance.getInstance().registerConsumer(traceConfig.getTopic(), traceConfig.getGroupId(), traceConfig.getThreadCount(), consumer);
        logger.info("start consumer: {}", JsonUtils.toJsonStringIgnoreException(traceConfig));
    }

    private void shutdownConsumer(TraceConfig traceConfig) {
        AsyncMqInstance.getInstance().shutdownConsumer(traceConfig.getTopic(), traceConfig.getGroupId());
        logger.info("shutdown consumer: {}", JsonUtils.toJsonStringIgnoreException(traceConfig));
    }

    private void restartConsumer(TraceConfig traceConfig) {
        shutdownConsumer(traceConfig);
        startConsumer(traceConfig);
    }

    public void printLoadTraceConfigMonitorLog(String phase, long cost, String message, String stack) {
        long now = Instant.now().toEpochMilli();
        MonitorLogUtils.print(MonitorLog.builder()
                .withCubeVer(CUBE_METRIC_LOG_VERSION)
                .withMeasure(MEASURE_LOAD_TRACE_CONFIG)
                .withTs(now)
                .addTag("phase", phase)
                .addField("msg", message)
                .addField("stack", stack)
                .addField("cost", cost)
                .addField("readableTs", DateUtils.format(new Date(now), DateUtils.FORMART11))
                .build());
    }
}