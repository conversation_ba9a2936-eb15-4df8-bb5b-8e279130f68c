package us.zoom.trace.asyncmq;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.mq.common.entity.TaskEntity;
import us.zoom.trace.util.DateUtils;
import us.zoom.trace.util.MonitorLogUtils;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ThreadPoolExecutor;

import static us.zoom.trace.common.Constants.CUBE_METRIC_LOG_VERSION;
import static us.zoom.trace.common.Constants.MEASURE_MESSAGE_BATCH_CONSUME_METRIC;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
public class TraceDataConsumer implements CommonConsumer {

    private static final Logger logger = LoggerFactory.getLogger(TraceDataConsumer.class);

    private static final TypeReference typeReference = new TypeReference<byte[]>() {
    };
    private String topic;
    private AsyncMqMessageHandler asyncMqMessageHandler;
    private ThreadPoolExecutor threadPoolExecutor;

    public TraceDataConsumer(String topic, AsyncMqMessageHandler asyncMqMessageHandler, ThreadPoolExecutor threadPoolExecutor) {
        this.topic = topic;
        this.asyncMqMessageHandler = asyncMqMessageHandler;
        this.threadPoolExecutor = threadPoolExecutor;
    }

    @Override
    public boolean onMessage(List<TaskEntity<byte[]>> taskEntities) {
        long start = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(taskEntities)) {
            logger.info("taskEntities is empty");
            return false;
        }
        for (TaskEntity<byte[]> taskEntity : taskEntities) {
            threadPoolExecutor.submit(wrapTask(taskEntity));
        }
        printMessageConsumeMetricData(taskEntities.size(), System.currentTimeMillis() - start);
        return false;
    }

    private Callable<Boolean> wrapTask(TaskEntity<byte[]> taskEntity) {
        return () -> {
            try {
                HandleStatus status = asyncMqMessageHandler.onMessage(taskEntity);
                if (status == HandleStatus.RETRY) {
                    logger.error("need re-consume trace data, topic: {}", topic);
                    return true;
                }
                return false;
            } catch (Exception e) {
                logger.error("fail to consume trace data, will retry, topic: {}", topic, e);
                return true;
            }
        };
    }

    @Override
    public TypeReference<byte[]> type() {
        return typeReference;
    }

    private void printMessageConsumeMetricData(int count, long consumeTime) {
        long now = Instant.now().toEpochMilli();
        MonitorLogUtils.print(MonitorLog.builder()
                .withCubeVer(CUBE_METRIC_LOG_VERSION)
                .withMeasure(MEASURE_MESSAGE_BATCH_CONSUME_METRIC)
                .withTs(now)
                .addTag("topic", this.topic)
                .addField("count", count)
                .addField("consumeTime", consumeTime)
                .addField("readableTs", DateUtils.format(new Date(now), DateUtils.FORMART1))
                .build());
    }
}
