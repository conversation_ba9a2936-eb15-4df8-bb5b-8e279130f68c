package us.zoom.trace.asyncmq;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cloud.secrets.rotate.handler.RotateHandlerRegister;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.client.clients.consumer.Consumer;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.client.clients.producer.Producer;
import us.zoom.mq.client.metrics.interceptor.ConsumerMetricsInterceptor;
import us.zoom.mq.client.pojo.ConsumeParam;
import us.zoom.mq.client.pojo.Subscriber;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.client.task.Task;
import us.zoom.mq.common.entity.TaskEntity;
import us.zoom.mq.common.enums.ProtocolStrategy;
import us.zoom.mq.common.listener.ProduceListener;
import us.zoom.mq.common.response.ProduceResult;
import us.zoom.trace.util.IpUtils;

import java.util.List;

import static us.zoom.trace.common.Constants.COMMON_SEPARATOR;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
public class AsyncMqInstance {

    private static final Logger logger = LoggerFactory.getLogger(AsyncMqInstance.class.getName());
    // ip + random string
    private static final String ASYNCMQ_CLIENT_ID_TEMPLATE = "cube-trace-client-%s-%s";
    private static final String ASYNCMQ_PASSWORD_KEY = "async.mq.password";
    private static final String DELAY_GROUP_SUFFIX = "_delay";
    private static final String TASK_TYPE = "business_RED_metrics_task_type";
    private volatile static Consumer consumer;
    // used for asyncmq exporter
    private volatile static Producer producer;
    private volatile static AsyncMQ asyncMQ;
    private volatile static AsyncMqInstance instance;
    private volatile static String environment;
    private ConsumeParam consumeParam;
    private String groupSuffix;

    private AsyncMqInstance(long consumeDelayMs, boolean delayConsume) {
        consumeParam = ConsumeParam.builder()
                .consumeDelayMs(consumeDelayMs)
                .build();
        groupSuffix = delayConsume ? DELAY_GROUP_SUFFIX : StringUtils.EMPTY;
    }

    public static void init(String endpoint, String username, String password, String environmentName, long consumeDelayMs, boolean delayConsume) {
        if (instance == null) {
            synchronized (AsyncMqInstance.class) {
                if (instance == null) {
                    logger.info("start initializing the AsyncMQ instance");
                    if (StringUtils.isBlank(endpoint) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
                        throw new RuntimeException("endpoint, username and password cannot be empty");
                    }
                    environment = environmentName;
                    instance = new AsyncMqInstance(consumeDelayMs, delayConsume);
                    asyncMQ = new DefaultAsyncMQ(endpoint, username, password);
                    asyncMQ.setClientId(String.format(ASYNCMQ_CLIENT_ID_TEMPLATE, IpUtils.getLocalIP(), RandomStringUtils.randomAlphanumeric(4)));
                    consumer = asyncMQ.consumer();
                    producer = asyncMQ.producer();
                    consumer.start();
                    logger.info("initializing the AsyncMQ instance successfully");
                }
            }
        }
    }

    public static AsyncMqInstance getInstance() {
        return instance;
    }

    /**
     * receiveCount means threadCount
     *
     * @param topic
     * @param groupId
     * @param receiveCount
     * @param retryableStraw
     */
    public void registerConsumer(String topic, String groupId, int receiveCount, RetryableStraw<byte[]> retryableStraw) {
        groupId = groupId + COMMON_SEPARATOR + environment + groupSuffix;
        Subscriber subscriber = consumer.registerSubscriber(topic, groupId);
        consumer.setStraw(subscriber, retryableStraw);
        // With the Straw interface, the consumeCount setting is invalid
        consumer.start(subscriber, consumeParam);
        logger.info("start consumer, topic:{}, group id:{}, receiveCount:{}", topic, groupId, receiveCount);
    }

    public void shutdownConsumer(String topic, String groupId) {
        try {
            groupId = groupId + COMMON_SEPARATOR + environment + groupSuffix;
            Subscriber subscriber = Subscriber.getInstance(topic, groupId);
            consumer.unRegisterSubscriber(subscriber);
            consumer.shutdownCleanStartParam(subscriber);
            logger.info("shutdown consumer, topic:{}, groupId:{}", topic, groupId);
        } catch (Exception e) {
            logger.error("shutdownConsumer failed, topic:{}, groupId:{}", e.getMessage(), topic, groupId);
        }
    }

    public void registerRotateHandler(String username) {
        RotateHandlerRegister.registerCurrentVersionHandler(List.of(ASYNCMQ_PASSWORD_KEY), new TraceCurrentVersionKVSecretHandler(asyncMQ, username));
    }

    public void shutdownAsyncMq() {
        asyncMQ.shutdown();
    }

    public Producer getProducer() {
        return producer;
    }

    public void sendAsync(String topic, byte[] message) {
        Task<byte[]> task = new Task<>();
        task.setTopicName(topic);
        task.setPayload(message);
        task.setTaskType(TASK_TYPE);
        task.setProtocolStrategy(ProtocolStrategy.SIMPLE);
        producer.sendAsync(task);
    }
}
