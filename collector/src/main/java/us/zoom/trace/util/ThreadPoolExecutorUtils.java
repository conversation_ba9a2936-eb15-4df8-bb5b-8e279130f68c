package us.zoom.trace.util;

import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.lang.math.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cube.sdk.model.MonitorLog;

import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.*;

import static us.zoom.trace.common.Constants.CUBE_METRIC_LOG_VERSION;
import static us.zoom.trace.common.Constants.MEASURE_THREAD_POOL_METRIC;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
public class ThreadPoolExecutorUtils {

    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolExecutorUtils.class);
    private static final Map<String, ThreadPoolExecutor> threadPoolExecutorMap = Maps.newHashMap();
    private static volatile ThreadPoolExecutor consumeTraceDataThreadPoolExecutor;
    private static volatile ThreadPoolExecutor commonThreadPoolExecutor;

    static {
        // thread pool monitoring
        Executors.newSingleThreadScheduledExecutor()
                .scheduleAtFixedRate(() -> {
                            for (Map.Entry<String, ThreadPoolExecutor> entry : threadPoolExecutorMap.entrySet()) {
                                printThreadPoolMetricData(entry.getValue(), MEASURE_THREAD_POOL_METRIC, entry.getKey());
                            }
                        },
                        RandomUtils.nextInt(5) + 1, 5, TimeUnit.SECONDS);
    }

    public static ThreadPoolExecutor getConsumeTraceDataThreadPoolExecutor() {
        if (consumeTraceDataThreadPoolExecutor == null) {
            synchronized (ThreadPoolExecutorUtils.class) {
                if (consumeTraceDataThreadPoolExecutor == null) {
                    consumeTraceDataThreadPoolExecutor = buildConsumeTraceDataThreadPoolExecutor("trace-data-consumer", new BlockPolicy());
                    threadPoolExecutorMap.put("trace-data-consumer", consumeTraceDataThreadPoolExecutor);
                }
            }
        }
        return consumeTraceDataThreadPoolExecutor;
    }

    // TODO need to design the number of threads
    public static ThreadPoolExecutor getCommonThreadPoolExecutor() {
        if (commonThreadPoolExecutor == null) {
            synchronized (ThreadPoolExecutorUtils.class) {
                if (commonThreadPoolExecutor == null) {
                    commonThreadPoolExecutor = new ThreadPoolExecutor(
                            10,
                            10,
                            300, TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(1000),
                            new ThreadFactoryBuilder().setNameFormat("trace-data-exporter-thread-%d").build(),
                            new ThreadPoolExecutor.CallerRunsPolicy());
                    threadPoolExecutorMap.put("trace-common", commonThreadPoolExecutor);
                }
            }
        }
        return commonThreadPoolExecutor;
    }

    private static ThreadPoolExecutor buildConsumeTraceDataThreadPoolExecutor(String threadPrefix, RejectedExecutionHandler handler) {
        int coreSizeMultiple = PropertiesUtils.getProperty("trace.threadpool.size.core.multiple", Integer.class, 20);
        int maxSizeMultiple = PropertiesUtils.getProperty("trace.threadpool.size.max.multiple", Integer.class, 20);
        int keepAliveTimeMs = PropertiesUtils.getProperty("trace.threadpool.keepAliveTimeMs", Integer.class, 300000);
        int queueSize = PropertiesUtils.getProperty("trace.threadpool.queue.size", Integer.class, 1000);
        int cpuNum = Math.max(Runtime.getRuntime().availableProcessors(), 1);
        logger.info("cpu number is {}", cpuNum);
        return new ThreadPoolExecutor(
                cpuNum * coreSizeMultiple,
                cpuNum * maxSizeMultiple,
                keepAliveTimeMs, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(queueSize),
                new ThreadFactoryBuilder().setNameFormat(threadPrefix + "-thread-%d").build(),
                handler);
    }

    public static void printThreadPoolMetricData(ThreadPoolExecutor threadPoolExecutor, String monitorLogMeasure, String threadPoolName) {
        long now = Instant.now().toEpochMilli();
        MonitorLogUtils.print(MonitorLog.builder()
                .withCubeVer(CUBE_METRIC_LOG_VERSION)
                .withMeasure(monitorLogMeasure)
                .addTag("threadPoolName", threadPoolName)
                .addField("readableTs", DateUtils.format(new Date(now), DateUtils.FORMART1))
                .addField("corePoolSize", threadPoolExecutor.getCorePoolSize())
                .addField("maxPoolSize", threadPoolExecutor.getMaximumPoolSize())
                .addField("queueSize", threadPoolExecutor.getQueue().size())
                .addField("activeCount", threadPoolExecutor.getActiveCount())
                .addField("taskCount", threadPoolExecutor.getTaskCount())
                .addField("completedTaskCount", threadPoolExecutor.getCompletedTaskCount())
                .withTs(now)
                .build());
    }
}