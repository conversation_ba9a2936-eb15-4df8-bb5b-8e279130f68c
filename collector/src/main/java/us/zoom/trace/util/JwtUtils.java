package us.zoom.trace.util;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import us.zoom.jwt.AZMJWTSupport;
import us.zoom.jwt.builder.ZMJWTBuilder;
import us.zoom.jwt.entity.ZMJWTAlgorithm;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static java.util.concurrent.TimeUnit.HOURS;
import static us.zoom.trace.common.Constants.JWT_ISSUER;


/**
 * @author: eason.jia
 * @date: 2024/8/4
 */
public class JwtUtils {

    private static final AZMJWTSupport jwtSupport = new TraceZMJWTSupport();
    private static final Map<String, ZMJWTBuilder> jwtBuildMap = new ConcurrentHashMap<>();
    private static final Long DEFAULT_EXPIRATION_TIME_SECOND = HOURS.toSeconds(1L);
    // jwt
    private static final String CUBE_AUTHENTICATION_KEY = "Authentication";
    private static final String AUTHENTICATION_BEAR = "Bearer" + StringUtils.SPACE;
    private static final String CUBE_AUDIENCE = "cube";

    /**
     * ensure that this initialization method is called only once when the application is initialized
     */
    static {
        jwtSupport.initSecretKeys();
    }

    /**
     * generate one token with given expiration time
     *
     * @param cacheKey
     * @param audience
     * @param expirationTimeSecond
     * @param headerClaims
     * @param payloadClaims
     * @return
     */
    public static String generateToken(String cacheKey, String audience, Long expirationTimeSecond, Map<String, Object> headerClaims, Map<String, Object> payloadClaims) {
        if (expirationTimeSecond == null || expirationTimeSecond <= 0L) {
            expirationTimeSecond = DEFAULT_EXPIRATION_TIME_SECOND;
        }
        ZMJWTBuilder builder = jwtBuildMap.computeIfAbsent(cacheKey,
                s -> jwtSupport.builder(JWT_ISSUER, audience, ZMJWTAlgorithm.ES256));
        Instant now = Instant.now();
        builder.withIssuedAt(Date.from(now));
        builder.withExpiresAt(Date.from(now.plus(expirationTimeSecond, ChronoUnit.SECONDS)));
        if (MapUtils.isNotEmpty(headerClaims)) {
            builder.withHeader(headerClaims);
        }
        if (MapUtils.isNotEmpty(payloadClaims)) {
            builder.addPayloads(payloadClaims);
        }
        return builder.build();
    }

    public static Map<String, String> buildHeaderParam(String cacheKey) {
        return Map.of(CUBE_AUTHENTICATION_KEY, buildJwtToken(cacheKey));
    }

    public static String buildJwtToken(String cacheKey) {
        return AUTHENTICATION_BEAR + JwtUtils.generateToken(cacheKey, CUBE_AUDIENCE, DEFAULT_EXPIRATION_TIME_SECOND, new HashMap<>(), new HashMap<>());
    }
}
