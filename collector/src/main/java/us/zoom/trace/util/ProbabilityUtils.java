package us.zoom.trace.util;

import org.apache.commons.lang.math.RandomUtils;

/**
 * @author: eason.jia
 * @date: 2024/9/8
 */
public class ProbabilityUtils {

    public static boolean checkProbability(double probability) {
        if (probability < 0 || probability > 1) {
            throw new IllegalArgumentException("Probability must be between 0 and 1.");
        }
        return RandomUtils.nextDouble() < probability;
    }
}
