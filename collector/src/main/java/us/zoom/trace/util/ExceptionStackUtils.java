package us.zoom.trace.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class ExceptionStackUtils {

    public static String parseExceptionStackToString(Throwable e) {
        ByteArrayOutputStream buf = null;
        String expMessage;
        try {
            buf = new ByteArrayOutputStream();
            e.printStackTrace(new java.io.PrintWriter(buf, true));
            expMessage = buf.toString();
        } finally {
            if (null != buf) {
                try {
                    buf.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return expMessage == null ? null : (expMessage.length() > 5000 ? expMessage.substring(0, 4999) : expMessage);
    }
}