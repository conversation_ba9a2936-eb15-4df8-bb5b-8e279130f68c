package us.zoom.trace.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.util.concurrent.RateLimiter;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import us.zoom.mq.common.entity.TaskContext;
import us.zoom.trace.common.SystemParamObserver;
import us.zoom.trace.common.config.TraceSystemParamCache;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/11/7
 */
@Component
public class LogRateLimiter implements SystemParamObserver {

    private static final Logger logger = LoggerFactory.getLogger(LogRateLimiter.class);
    private static final String OBSERVED_SYSTEM_PARAM_KEY = "logRateLimiter";
    private static final String LOG_PERMIT_PER_SECOND = "log_permit_per_second";
    private Map<String, RateLimiter> rateLimiters = new HashMap<>();
    private int logPermitPerSecond = 100;

    @PostConstruct
    public void init() {
        TraceSystemParamCache.addObserver(this);
    }

    public RateLimiter getLogRateLimiter(String key) {
        return rateLimiters.computeIfAbsent(key, k -> RateLimiter.create(logPermitPerSecond));
    }

    public boolean getPermitByKey(String key) {
        return getLogRateLimiter(key).tryAcquire();
    }

    public boolean getPermitByAsyncMqContext(TaskContext taskContext) {
        if (taskContext == null) {
            return false;
        }
        String topicName = taskContext.getTopicName();
        return StringUtils.isBlank(topicName) ? false : getPermitByKey(topicName);
    }

    @Override
    public String getObservedKey() {
        return OBSERVED_SYSTEM_PARAM_KEY;
    }

    @Override
    public void onChange(String param) {
        try {
            Map<String, Object> paramMap = JsonUtils.parse(param, new TypeReference<>() {
            });
            if (paramMap.containsKey(LOG_PERMIT_PER_SECOND)) {
                logPermitPerSecond = Integer.parseInt(paramMap.get(LOG_PERMIT_PER_SECOND).toString());
                logger.info("logPermitPerSecond set to {}", logPermitPerSecond);
            }
        } catch (JsonProcessingException e) {
            logger.error("failed to parse json string: {}", param, e);
        }
    }
}
