package us.zoom.trace.util;

import org.apache.commons.lang3.StringUtils;

import java.net.*;
import java.util.Enumeration;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
public class IpUtils {

    public static final String DEFAULT_IP = "127.0.0.1";

    private volatile static String host;
    private volatile static String ip;

    public static String getLocalIpByNetcard() {
        try {
            for (Enumeration<NetworkInterface> e = NetworkInterface.getNetworkInterfaces(); e.hasMoreElements(); ) {
                NetworkInterface item = e.nextElement();
                for (InterfaceAddress address : item.getInterfaceAddresses()) {
                    if (item.isLoopback() || !item.isUp()) {
                        continue;
                    }
                    if (address.getAddress() instanceof Inet4Address) {
                        Inet4Address inet4Address = (Inet4Address) address.getAddress();
                        return inet4Address.getHostAddress();
                    }
                }
            }
            return InetAddress.getLocalHost().getHostAddress();
        } catch (SocketException | UnknownHostException e) {
            throw new RuntimeException(e);
        }
    }

    public static String getLocalIP() {
        if (StringUtils.isNotBlank(ip)) {
            return ip;
        }

        try {
            ip = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            throw new RuntimeException(e);
        }
        return ip;
    }

    public static String getHost() {
        if (null != host) {
            return host;
        }
        try {
            InetAddress addr = InetAddress.getLocalHost();
            host = addr.getHostName().toString();
            return host;
        } catch (UnknownHostException e) {
        }
        return null;
    }
}
