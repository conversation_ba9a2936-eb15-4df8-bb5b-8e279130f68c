package us.zoom.trace.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * @author: eason.jia
 * @date: 2024/8/1
 */
public class PropertiesUtils {

    private static final Properties properties = new Properties();

    static {
        InputStream resourceAsStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("application.properties");
        try {
            properties.load(resourceAsStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static String getProperty(String key) {
        return properties.getProperty(key);
    }

    public static String getProperty(String key, String defaultValue) {
        String val = getProperty(key);
        return (val == null) ? defaultValue : val;
    }

    public static <T> T getProperty(String key, Class<T> targetType, T defaultValue) {
        String val = getProperty(key);
        return (val == null) ? defaultValue : targetType.cast(val);
    }
}
