package us.zoom.trace.util;

import lombok.Data;

import java.time.Instant;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
@Data
public class TimeDuration {

    private long startTime;
    private long endTime;

    public TimeDuration(long startTime) {
        this.startTime = startTime;
    }

    public static TimeDuration start() {
        return new TimeDuration(Instant.now().toEpochMilli());
    }

    public void end() {
        this.endTime = Instant.now().toEpochMilli();
    }

    public long duration() {
        if (startTime == 0L) {
            return 0L;
        }
        if (endTime == 0L) {
            return Instant.now().toEpochMilli() - this.startTime;
        }
        return endTime - startTime;
    }
}

