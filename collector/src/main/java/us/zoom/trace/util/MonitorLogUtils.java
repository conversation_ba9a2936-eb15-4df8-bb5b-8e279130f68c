package us.zoom.trace.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.trace.common.Constants;

/**
 * @author: eason.jia
 * @date: 2024/8/5
 */
public class MonitorLogUtils {

    private static final Logger logger = LoggerFactory.getLogger(Constants.MONITOR_LOGGER_NAME);


    public static void print(MonitorLog monitorLog) {
        logger.info(JsonUtils.toJsonStringIgnoreException(monitorLog));
    }

    public static Logger getLogger() {
        return logger;
    }

}