package us.zoom.trace.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: eason.jia
 * @date: 2024/4/23
 */
@Component
public class RedisService {

    private static final Logger logger = LoggerFactory.getLogger(RedisService.class);

    private final RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public RedisService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public Boolean setIfAbsent(String key, Object value, long timeout, TimeUnit unit) {
        try {
            return redisTemplate.opsForValue().setIfAbsent(key, value, timeout, unit);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute setIfAbsent command, key: %s, value: %s, timeout: %d, unit: %s", key, String.valueOf(value), timeout, String.valueOf(unit)), e);
        }
        return false;
    }

    public Boolean setIfPresent(String key, Object value, long timeout, TimeUnit unit) {
        try {
            return redisTemplate.opsForValue().setIfPresent(key, value, timeout, unit);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute setIfPresent command, key: %s, value: %s, timeout: %d, unit: %s", key, String.valueOf(value), timeout, String.valueOf(unit)), e);
        }
        return false;
    }

    public Boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            return redisTemplate.expire(key, timeout, unit);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute expire command, key: %s, timeout: %d, unit: %s", key, timeout, String.valueOf(unit)), e);
        }
        return false;
    }

    public Object get(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute get command, key: %s", key), e);
        }
        return null;
    }

    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute set command, key: %s", key), e);
            throw e;
        }
    }

    public void hset(String key, String hashKey, Object value) {
        try {
            redisTemplate.opsForHash().put(key, hashKey, value);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute hash set command, key: %s", key), e);
            throw e;
        }
    }

    public void setEx(String key, Object value, long expireTime, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, expireTime, unit);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute setEx command, key: %s", key), e);
            throw e;
        }
    }
    public Boolean setExIfAbsent(String key, Object value, long expireTime, TimeUnit unit) {
        try {
            return redisTemplate.opsForValue().setIfAbsent(key, value, expireTime, unit);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute setEx command, key: %s", key), e);
            throw e;
        }
    }

    public Boolean delete(String key) {
        try {
            return redisTemplate.delete(key);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute delete command, key: %s", key), e);
        }
        return false;
    }

    public Boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute hasKey command, key: %s", key), e);
        }
        return false;
    }

    public Long increment(String key) {
        return increment(key, 1L);
    }

    public Long increment(String key, long delta) {
        try {
            return redisTemplate.opsForValue().increment(key, delta);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute increment command, key: %s, delta: %d", key, delta), e);
        }
        return null;
    }

    public Long decrement(String key) {
        return decrement(key, 1L);
    }

    public Long decrement(String key, long delta) {
        try {
            return redisTemplate.opsForValue().decrement(key, delta);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute increment command, key: %s, delta: %d", key, delta), e);
        }
        return null;
    }

    public List hvals(String key) {
        try {
            return redisTemplate.opsForHash().values(key);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute hvals command, key: %s", key), e);
        }
        return null;
    }


    public Object hget(String key, String hashKey) {
        try {
            return redisTemplate.opsForHash().get(key, hashKey);
        } catch (Exception e) {
            logger.error("Failed to execute hget command, key: {}, hashKey: {}", key, hashKey, e);
        }
        return null;
    }

    public Set<String> scanForAllKeys(String pattern, long count) {
        Set<String> keys = new HashSet<>();
        try {
            Cursor<String> cursor = redisTemplate.scan(ScanOptions.scanOptions().match(pattern).count(count).build());
            while (cursor.hasNext()) {
                keys.add(cursor.next());
            }
            cursor.close();
        } catch (Exception e) {
            logger.error(String.format("Failed to execute scan command, pattern: %s, count: %d", pattern, count), e);
        }
        return keys;
    }

    public List<Object> mget(Collection<String> keys) {
        try {
            return redisTemplate.opsForValue().multiGet(keys);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute mget command, keys: %s", JsonUtils.toJsonStringIgnoreException(keys)), e);
        }
        return null;
    }

    public boolean sisMember(String key, Object member) {
        try {
            return redisTemplate.opsForSet().isMember(key, member);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute sisMember command, key: %s, member: %s", key, member));
        }
        return false;
    }

    public Long sadd(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().add(key, values);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute sadd command, key: %s, values: %s", key, Arrays.toString(values)), e);
        }
        return null;
    }

    public boolean zadd(String key, Object value, double score) {
        try {
            return redisTemplate.opsForZSet().add(key, value, score);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute zadd command, key: %s, values: %s, score: %s", key, value, score), e);
        }
        return false;
    }

    public Long zadd(String key, Map<String, Double> elements) {
        try {
            Set<TypedTuple<Object>> tupleSet = elements.entrySet()
                    .stream()
                    .map(entry -> TypedTuple.<Object>of(entry.getKey(), entry.getValue()))
                    .collect(Collectors.toSet());
            return redisTemplate.opsForZSet().add(key, tupleSet);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute zadd command, key: %s, values: %s", key, elements), e);
        }
        return null;
    }

    public Long zremoveRangeByScore(String key, double min, double max) {
        try {
            return redisTemplate.opsForZSet().removeRangeByScore(key, min, max);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute zremoveRangeByScore command, key: %s, min: %s, max: %s", key, min, max), e);
        }
        return null;
    }

    public Double zscore(String key, Object member) {
        try {
            return redisTemplate.opsForZSet().score(key, member);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute zscore command, key: %s, member: %s", key, member), e);
        }
        return null;
    }

    public Object eval(String script, Class returnType, List<String> keys, Object... args) {
        try {
            return redisTemplate.execute(RedisScript.of(script, returnType), keys, args);
        } catch (Exception e) {
            logger.error(String.format("Failed to execute lua script, keys: %s, args: %s", JsonUtils.toJsonStringIgnoreException(keys), Arrays.toString(args)), e);
        }
        return null;
    }

    public <T> T eval(RedisScript<T> redisScript, List<String> keys, Object... args) {
        return redisTemplate.execute(redisScript, keys, args);
    }
}