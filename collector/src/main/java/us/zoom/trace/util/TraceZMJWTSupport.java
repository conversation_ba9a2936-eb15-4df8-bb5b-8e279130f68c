package us.zoom.trace.util;

import us.zoom.jwt.AZMJWTSupport;

import static us.zoom.trace.common.Constants.JWT_ISSUER;

/**
 * @author: eason.jia
 * @date: 2024/8/1
 */
public class TraceZMJWTSupport extends AZ<PERSON><PERSON>WTSupport {

    private static final String JWT_PRIVATE_KEY_NAME = "jwtSigningPrivate";

    @Override
    public void initSecretKeys() {
        // jwtSigningPrivate
        this.addAsymmetricSignedKey(JWT_ISSUER, JWT_PRIVATE_KEY_NAME);
    }
}