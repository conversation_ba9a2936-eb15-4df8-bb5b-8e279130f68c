package us.zoom.trace.util;

import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Consumer;

/**
 * When the task queue is too long, it is blocked until it is added to the queue.
 * If the blocking is interrupted, will throw an {@link InterruptedException} exception.
 *
 * @author: eason.jia
 * @date: 2024/8/1
 */
public class BlockPolicy implements RejectedExecutionHandler {

    private final Consumer<Runnable> handlerWhenShutdown;

    public BlockPolicy(final Consumer<Runnable> handlerWhenShutdown) {
        this.handlerWhenShutdown = handlerWhenShutdown;
    }

    public BlockPolicy() {
        this(null);
    }

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
        if (false == e.isShutdown()) {
            try {
                e.getQueue().put(r);
            } catch (InterruptedException ex) {
                throw new RejectedExecutionException("Task " + r + " rejected from " + e);
            }
        } else if (null != handlerWhenShutdown) {
            handlerWhenShutdown.accept(r);
        }
    }
}
