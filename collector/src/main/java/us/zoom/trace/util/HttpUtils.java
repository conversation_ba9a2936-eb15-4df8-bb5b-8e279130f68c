package us.zoom.trace.util;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

/**
 * @author: eason.jia
 * @date: 2024/8/5
 */
public class HttpUtils {

    private static final String PATH_SPLIT = "/";
    private static final Logger LOG = LoggerFactory.getLogger(HttpUtils.class.getName());
    private static final String UTF8 = "UTF-8";
    private static final Integer HTTP_SUCCESS_CODE = 200;
    private static final Integer DEFAULT_TIME_OUT = 35 * 1000;

    public static String adjustUrl(String flinkUrl) {
        if (!StringUtils.endsWith(flinkUrl, PATH_SPLIT)) {
            flinkUrl += PATH_SPLIT;
        }
        return flinkUrl;
    }

    public static <T> T getForObject(String url, Class<T> responseType, Map<String, String> uriVariables) throws IOException {
        return JsonUtils.parse(doGet(url, uriVariables), responseType);
    }

    public static String doGet(List<String> urls, Map<String, String> param, Map<String, String> headers, Function function, Integer maxRetryCount, AtomicInteger statusCode) {
        int retryCount = 0;
        for (String url : urls) {
            try {
                return doGet(url, param, headers, function, statusCode);
            } catch (IOException e) {
                retryCount++;
            } finally {
                if (retryCount >= maxRetryCount) {
                    LOG.error("extends the max retry count!");
                    break;
                }
            }
        }
        return null;
    }

    public static String put(List<String> urls, Map<String, String> param, Map<String, String> headers, String bodyJson, Function function, Integer maxRetryCount, AtomicInteger statusCode) {
        return doAction(urls, param, headers, bodyJson, function, maxRetryCount, new HttpPut(), statusCode);
    }

    public static String doAction(List<String> urls, Map<String, String> param, Map<String, String> headers, String bodyJson, Function function, Integer maxRetryCount, HttpEntityEnclosingRequestBase httpAction, AtomicInteger statusCode) {
        int retryCount = 0;
        for (String url : urls) {
            try {
                return doAction(url, param, headers, bodyJson, function, httpAction, statusCode);
            } catch (IOException e) {
                retryCount++;
            } finally {
                if (retryCount >= maxRetryCount) {
                    LOG.error("extends the max retry count!");
                    break;
                }
            }
        }
        return null;
    }

    private static String doAction(String url, Map<String, String> param, Map<String, String> headers, String bodyJson, Function function, HttpEntityEnclosingRequestBase httpAction, AtomicInteger statusCode) throws IOException {
        AtomicBoolean initOver = new AtomicBoolean(false);
        CloseableHttpClient httpclient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        try {
            if (!initOver.get()) {
                URI uri = createUri(url, param);
                httpAction.setURI(uri);
                setHeads(httpAction, headers);
                httpAction.setEntity(new StringEntity(bodyJson, ContentType.APPLICATION_JSON));
                httpAction.setConfig(generateRequestCfg());
                initOver.set(true);
            }
            if (initOver.get() && null != function) {
                function.apply(httpAction);
            }
            response = httpclient.execute(httpAction);

            statusCode.set(response.getStatusLine().getStatusCode());
            if (response.getStatusLine().getStatusCode() == HTTP_SUCCESS_CODE) {
                return EntityUtils.toString(response.getEntity(), UTF8);
            } else {
                LOG.error("the response is {}", JsonUtils.toJsonStringIgnoreException(response));
            }
        } catch (IOException e) {
            LOG.error("url is {}, param is {}", url, null != param ? param.toString() : null, e);
            throw e;
        } finally {
            close(httpclient, response);
        }
        return null;
    }

    private static RequestConfig generateRequestCfg() {
        return RequestConfig.custom()
                .setConnectTimeout(DEFAULT_TIME_OUT)
                .setConnectionRequestTimeout(DEFAULT_TIME_OUT)
                .setSocketTimeout(DEFAULT_TIME_OUT)
                .build();
    }

    public static String doGet(String url, Map<String, String> param, Map<String, String> headers, Function function, AtomicInteger statusCode) throws IOException {
        AtomicBoolean initOver = new AtomicBoolean(false);
        CloseableHttpClient httpclient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        try {
            HttpGet httpGet = null;
            if (!initOver.get()) {
                URI uri = createUri(url, param);
                httpGet = new HttpGet(uri);
                setHeads(httpGet, headers);
                initOver.set(true);
                httpGet.setConfig(generateRequestCfg());
            }
            if (initOver.get() && null != function) {
                function.apply(httpGet);
            }
            response = httpclient.execute(httpGet);
            if (null != statusCode) {
                statusCode.set(response.getStatusLine().getStatusCode());
            }
            if (response.getStatusLine().getStatusCode() == HTTP_SUCCESS_CODE) {
                return EntityUtils.toString(response.getEntity(), UTF8);
            }
        } catch (IOException e) {
            LOG.error("url is {},para is {}", url, null != param ? param.toString() : null);
            throw e;
        } finally {
            close(httpclient, response);
        }
        return null;
    }

    private static void close(CloseableHttpClient httpclient, CloseableHttpResponse response) {
        try {
            if (response != null) {
                response.close();
            }

            if (null != httpclient) {
                httpclient.close();
            }
        } catch (IOException e) {
            LOG.error("close is error", e);
        }
    }

    private static URI createUri(String url, Map<String, String> param) {
        URIBuilder builder = null;
        try {
            builder = new URIBuilder(url);
            if (param != null) {
                for (String key : param.keySet()) {
                    builder.addParameter(key, param.get(key));
                }
            }
            return builder.build();
        } catch (URISyntaxException e) {
            LOG.error("create uri error", e);
        }
        return null;
    }

    private static void setHeads(HttpRequestBase httpRequestBase, Map<String, String> headers) {
        httpRequestBase.addHeader("Content-Type", "application/json");
//        httpRequestBase.addHeader("Content-Type", "application/x-www-form-urlencoded");
        if (MapUtils.isEmpty(headers)) {
            return;
        }
        headers.forEach((k, v) -> httpRequestBase.addHeader(k, v));
    }

    public static String doGet(String url, Map<String, String> param) throws IOException {
        return doGet(url, param, null, null, null);
    }

    public static String post(List<String> urls, Map<String, String> param, Map<String, String> heads, String bodyJson, AtomicInteger statusCode, Function function, Integer maxRetryCount) throws IOException {
        return doAction(urls, param, heads, bodyJson, function, maxRetryCount, new HttpPost(), statusCode);
    }

    private static void setPostParam(Map<String, String> param, HttpPost httpPost) throws UnsupportedEncodingException {
        if (MapUtils.isEmpty(param)) {
            return;
        }
        List<NameValuePair> paramList = new ArrayList<>();
        for (String key : param.keySet()) {
            paramList.add(new BasicNameValuePair(key, param.get(key)));
        }
        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList);
        httpPost.setEntity(entity);
    }
}
