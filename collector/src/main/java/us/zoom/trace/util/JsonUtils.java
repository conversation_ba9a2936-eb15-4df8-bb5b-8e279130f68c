package us.zoom.trace.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.google.common.collect.Maps;
import com.google.crypto.tink.subtle.Hex;
import com.google.protobuf.ByteString;
import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.hubspot.jackson.datatype.protobuf.ProtobufModule;
import io.opentelemetry.proto.trace.v1.ResourceSpans;
import io.opentelemetry.proto.trace.v1.Span;
import net.bytebuddy.agent.ByteBuddyAgent;
import net.bytebuddy.agent.builder.AgentBuilder;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.implementation.FieldAccessor;
import net.bytebuddy.matcher.ElementMatchers;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.instrument.Instrumentation;
import java.util.Iterator;
import java.util.Map;
import java.util.function.BiFunction;

/**
 * @author: eason.jia
 * @date: 2024/7/31
 */
public class JsonUtils {

    private static final Logger logger = LoggerFactory.getLogger(JsonUtils.class);
    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        Instrumentation install = ByteBuddyAgent.install();
        new AgentBuilder.Default()
                .type(ElementMatchers.named("com.hubspot.jackson.datatype.protobuf.builtin.deserializers.MessageDeserializer"))
                .transform((builder, typeDescription, classLoader, javaModule, protectionDomain) ->
                        builder.implement(MessageTypeGetter.class)
                                .method(ElementMatchers.named("getTargetMessageType"))
                                .intercept(FieldAccessor.ofField("messageType"))
                )
                .installOn(install);
        new AgentBuilder.Default()
                .type(ElementMatchers.named("com.hubspot.jackson.datatype.protobuf.ProtobufDeserializer"))
                .transform((builder, typeDescription, classLoader, module, domain) ->
                        {
                            return builder
                                    .visit(Advice.to(SpanDeserializeEnhance.class)
                                            .on(ElementMatchers.named("readValue")
                                                    .and(ElementMatchers.takesArgument(0, Message.Builder.class))
                                                    .and(ElementMatchers.takesArgument(1, Descriptors.FieldDescriptor.class))
                                                    .and(ElementMatchers.takesArgument(2, Message.class))
                                                    .and(ElementMatchers.takesArgument(3, JsonParser.class))
                                                    .and(ElementMatchers.takesArgument(4, DeserializationContext.class))
                                                    .and(ElementMatchers.returns(Object.class))));
                        }
                )
                .installOn(install);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.getSerializerProvider().setNullKeySerializer(new CustomNullKeySerializer());
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Map.Entry.class, new MapEntryDeserializer());
        mapper.registerModule(module);
        mapper.registerModule(new ProtobufModule());
    }

    public static <T> T parse(String jsonData, Class<T> classInfo) throws JsonProcessingException {
        return mapper.readValue(jsonData, classInfo);
    }

    public static <T> T parse(String jsonData, TypeReference<T> typeReference) throws JsonProcessingException {
        return mapper.readValue(jsonData, typeReference);
    }

    public static <T> T parseWithIgnoreError(String jsonData, TypeReference<T> typeReference, T defaultValue) {
        try {
            return mapper.readValue(jsonData, typeReference);
        } catch (Throwable e) {
            return defaultValue;
        }
    }

    public static String toJsonString(Object value) throws JsonProcessingException {
        return mapper.writeValueAsString(value);
    }

    public static String toJsonStringIgnoreException(Object value) {
        try {
            return mapper.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            logger.error("fail to serialize object: {} to json string", value);
        }
        return null;
    }

    public static Map<String, Object> beanToMap(Object value) {
        return mapper.convertValue(value, new TypeReference<>() {
        });
    }

    public static boolean isJsonObject(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        return isWrap(StringUtils.trim(str), '{', '}');
    }

    public static boolean isJsonArray(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        return isWrap(StringUtils.trim(str), '[', ']');
    }

    private static class CustomNullKeySerializer extends StdSerializer<Object> {
        public CustomNullKeySerializer() {
            this(null);
        }

        public CustomNullKeySerializer(Class<Object> t) {
            super(t);
        }

        @Override
        public void serialize(Object nullKey, JsonGenerator jsonGenerator, SerializerProvider unused)
                throws IOException {
            jsonGenerator.writeFieldName("null");
        }
    }

    private static boolean isWrap(CharSequence str, char prefixChar, char suffixChar) {
        if (null == str) {
            return false;
        }
        return str.charAt(0) == prefixChar && str.charAt(str.length() - 1) == suffixChar;
    }

    public static <T> T toObject(String input, Class<T> classMeta) {
        try {
            return mapper.readValue(input, classMeta);
        } catch (IOException e) {
            logger.error("to object error, input is {} ", input, e);
            throw new RuntimeException(e);
        }
    }

    public static <T> T toObjectByTypeRef(String input, TypeReference<T> typeReference) {
        try {
            return mapper.readValue(input, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static class MapEntryDeserializer extends JsonDeserializer<Map.Entry> {
        @Override
        public Map.Entry deserialize(JsonParser p, DeserializationContext ctx)
                throws IOException {
            JsonNode node = p.getCodec().readTree(p);
            if (node != null && node.isObject() && node.size() == 1) {
                Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
                if (fields.hasNext()) {
                    Map.Entry<String, JsonNode> field = fields.next();
                    Object key = field.getKey();
                    Object value = ctx.readTreeAsValue(field.getValue(), Object.class);
                    return Map.entry(key, value);
                }
            }
            return ImmutablePair.nullPair();
        }
    }

    public static interface MessageTypeGetter {
        Class getTargetMessageType();
    }

    public static class SpanDeserializeEnhance {
        public static Map<String, BiFunction<String, DeserializationContext, Object>> FIELD_REMAPPING_FUNCTIONS = Maps.newHashMap();

        public static final int SPAN_ID_BYTES_LENGTH = 8;
        public static final int TRACE_ID_BYTES_LENGTH = 16;

        static {
            FIELD_REMAPPING_FUNCTIONS.put("traceId", (jsonValue, context) -> {
                if (jsonValue.length() == TRACE_ID_BYTES_LENGTH * 2) {
                    return ByteString.fromHex(jsonValue);
                } else {
                    return ByteString.copyFrom(context.getBase64Variant().decode(jsonValue));
                }
            });
            FIELD_REMAPPING_FUNCTIONS.put("spanId", (jsonValue, context) -> {
                if (jsonValue.length() == SPAN_ID_BYTES_LENGTH * 2) {
                    return ByteString.fromHex(jsonValue);
                } else {
                    return ByteString.copyFrom(context.getBase64Variant().decode(jsonValue));
                }
            });
            FIELD_REMAPPING_FUNCTIONS.put("parentSpanId", (jsonValue, context) -> {
                if (jsonValue.length() == SPAN_ID_BYTES_LENGTH * 2) {
                    return ByteString.fromHex(jsonValue);
                } else {
                    return ByteString.copyFrom(context.getBase64Variant().decode(jsonValue));
                }
            });
        }

        @Advice.OnMethodEnter(skipOn = Advice.OnNonDefaultValue.class)
        public static boolean skipOn(
                @Advice.Argument(0) Message.Builder builder,
                @Advice.Argument(1) Descriptors.FieldDescriptor field,
                @Advice.Argument(2) Message defaultInstance,
                @Advice.Argument(3) JsonParser parser,
                @Advice.Argument(4) DeserializationContext context,
                @Advice.This Object thisInstance,
                @Advice.Local("jsonValue") String jsonValue,
                @Advice.Local("shouldSkip") boolean shouldSkip

        ) throws IOException {
            shouldSkip = false;
            if (((MessageTypeGetter) thisInstance).getTargetMessageType() == Span.class &&
                    FIELD_REMAPPING_FUNCTIONS.containsKey(field.getJsonName())) {
                jsonValue = parser.getText();
                shouldSkip = true;
                return shouldSkip;
            }
            return shouldSkip;
        }

        @Advice.OnMethodExit(suppress = Throwable.class, onThrowable = Throwable.class)
        public static void exit(
                @Advice.Argument(0) Message.Builder builder,
                @Advice.Argument(1) Descriptors.FieldDescriptor field,
                @Advice.Argument(2) Message defaultInstance,
                @Advice.Argument(3) JsonParser parser,
                @Advice.Argument(4) DeserializationContext context,
                @Advice.This Object thisInstance,
                @Advice.Local("jsonValue") String jsonValue,
                @Advice.Local("shouldSkip") boolean shouldSkip,
                @Advice.Return(readOnly = false) Object returnValue
        ) {
            if (shouldSkip) {
                BiFunction<String, DeserializationContext, Object> function = FIELD_REMAPPING_FUNCTIONS.get(field.getJsonName());
                returnValue = function.apply(jsonValue, context);
            }
        }
    }
}