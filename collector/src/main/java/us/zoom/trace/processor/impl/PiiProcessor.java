package us.zoom.trace.processor.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.Span;
import jakarta.annotation.PostConstruct;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.SystemParamObserver;
import us.zoom.trace.common.config.TraceSystemParamCache;
import us.zoom.trace.common.exception.ProcessException;
import us.zoom.trace.processor.ProcessorType;
import us.zoom.trace.util.JsonUtils;

import java.util.Map;
import java.util.Set;

import static us.zoom.trace.processor.ProcessorType.PII;

/**
 * @author: eason.jia
 * @date: 2024/9/29
 */
@Order(90)
@Component("piiProcessor")
@ConditionalOnExpression("'${pii.processor.enable:false}'.equals('true')")
public class PiiProcessor extends BaseProcessor implements SystemParamObserver {

    private static final Logger logger = LoggerFactory.getLogger(PiiProcessor.class);
    private static final Set<String> HTTP_ATTRIBUTE_SET = Sets.newHashSet("externalHttpUrl", "httpUrl", "url.full");
    private static final String OBSERVED_SYSTEM_PARAM_KEY = "piiProcessor";
    private static final String REMOVED_PII_ATTRIBUTE_KEYS = "removed_pii_attribute_keys";
    private Set<String> removedPiiAttributeKeys = Sets.newHashSet("url.path", "url.query");

    @PostConstruct
    public void init() {
        TraceSystemParamCache.addObserver(this);
    }

    @Override
    public ProcessorType getType() {
        return PII;
    }

    @Override
    public boolean process(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) throws ProcessException {
        if (MapUtils.isEmpty(attributeMap)) {
            return true;
        }
        for (String removedPiiAttributeKey : removedPiiAttributeKeys) {
            attributeMap.remove(removedPiiAttributeKey);
        }
        for (String httpKey : HTTP_ATTRIBUTE_SET) {
            AnyValue anyValue = attributeMap.get(httpKey);
            if (anyValue != null) {
                AnyValue.Builder valueBuilder = anyValue.toBuilder();
                valueBuilder.setStringValue(removeHttpQueryParameter(valueBuilder.getStringValue()));
                attributeMap.put(httpKey, valueBuilder.build());
            }
        }
        AnyValue dbSystem = attributeMap.get("db.system");
        AnyValue dbStatement = attributeMap.get("db.statement");
        if (dbSystem != null && StringUtils.equalsAnyIgnoreCase(dbSystem.getStringValue(), "redis") && dbStatement != null) {
            AnyValue.Builder valueBuilder = dbStatement.toBuilder();
            valueBuilder.setStringValue(removeRedisKey(valueBuilder.getStringValue()));
            attributeMap.put("db.statement", valueBuilder.build());
        }
        return true;
    }

    private String removeHttpQueryParameter(String query) {
        if (StringUtils.isBlank(query)) {
            return query;
        }
        return StringUtils.substringBefore(query, "?");
    }

    // db.system = redis
    // db.statement
    private String removeRedisKey(String dbStatement) {
        if (StringUtils.isBlank(dbStatement)) {
            return dbStatement;
        }
        return StringUtils.substringBefore(dbStatement, StringUtils.SPACE);
    }

    @Override
    public String getObservedKey() {
        return OBSERVED_SYSTEM_PARAM_KEY;
    }

    @Override
    public void onChange(String param) {
        try {
            Map<String, Object> paramMap = JsonUtils.parse(param, new TypeReference<>() {
            });
            if (paramMap.containsKey(REMOVED_PII_ATTRIBUTE_KEYS)) {
                Object value = paramMap.get(REMOVED_PII_ATTRIBUTE_KEYS);
                removedPiiAttributeKeys = JsonUtils.toObjectByTypeRef(String.valueOf(value), new TypeReference<Set<String>>() {
                });
                logger.info("piiAttributeKeys set to {}", removedPiiAttributeKeys);
            }
        } catch (JsonProcessingException e) {
            logger.error("failed to parse json string: {}", param, e);
        }
    }
}
