package us.zoom.trace.processor.impl;

import com.google.common.hash.BloomFilter;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.Span;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.trace.biz.longtimestorage.LongTimeStorageTraceIdCache;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.exception.ProcessException;
import us.zoom.trace.processor.ProcessorType;

import java.util.Map;

import static us.zoom.trace.processor.ProcessorType.BLOOM_FILTER_SAMPLING;

/**
 * sampling by bloom filter
 *
 * @author: adan.zeng
 * @date: 2025/03/11
 */
@Order(1)
@Component("bloomFilterSamplingProcessor")
@ConditionalOnExpression("'${cube.trace.consume.delay.enable:false}'.equals('true')")
public class BloomFilterSamplingProcessor extends BaseProcessor {
    private static final Logger logger = LoggerFactory.getLogger(BloomFilterSamplingProcessor.class);

    @Autowired
    private LongTimeStorageTraceIdCache longTimeStorageTraceIdCache;

    @Override
    public ProcessorType getType() {
        return BLOOM_FILTER_SAMPLING;
    }


    @Override
    public boolean process(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) throws ProcessException {
        BloomFilter<byte[]> bloomFilter = longTimeStorageTraceIdCache.getBloomFilter(otelSpan.getStartTimeUnixNano());
        return bloomFilter != null && bloomFilter.mightContain(otelSpan.getTraceId().toByteArray());
    }

    @Override
    public boolean needSkipWhenFailed() {
        return true;
    }
}
