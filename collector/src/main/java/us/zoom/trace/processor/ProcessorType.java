package us.zoom.trace.processor;

/**
 * @author: eason.jia
 * @date: 2024/8/2
 */
public enum ProcessorType {

    /**
     * transform trace protocol
     */
    TRANSFORM("transform"),

    /**
     * determine whether the span needs to be stored for long time
     */
    RATE_LIMIT_SAMPLING("rate_limit_sampling"),

    /**
     * determine whether the span needs to be stored for long time
     */
    LONG_TIME_STORAGE_SAMPLING("long_time_storage_sampling"),

    /**
     * determine whether the span needs to be stored judged by bloom filter
     */
    BLOOM_FILTER_SAMPLING("bloom_filter_sampling"),

    /**
     * some trace id or span id from python or go will be encoded
     */
    DECODE_ID_PROCESSOR("decodeIdProcessor"),

    /**
     * calculate duration as attribute
     */
    CALCULATE_DURATION_PROCESSOR("calculateDurationProcessor"),

    /**
     * remove the PII information in the attribute
     */
    PII("pii"),


    /**
     * rename span, some span only have method like POST, need to add more info
     */
    RENAME_SPAN_PROCESSOR("renameSpanProcessor"),

    /**
     * collect RED metrics
     */
    RED_METRICS_PROCESSOR("REDMetricsProcessor"),
    SQL_STATEMENT_PROCESSOR("sqlStatementProcessor"),

    ;

    private String name;

    ProcessorType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
