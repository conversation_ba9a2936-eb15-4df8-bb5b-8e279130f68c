package us.zoom.trace.processor.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.Span;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.SystemParamObserver;
import us.zoom.trace.common.config.TraceSystemParamCache;
import us.zoom.trace.common.exception.ProcessException;
import us.zoom.trace.processor.ProcessorType;
import us.zoom.trace.util.JsonUtils;
import us.zoom.trace.util.LogRateLimiter;
import us.zoom.trace.util.MonitorLogUtils;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import static us.zoom.trace.common.Constants.CUBE_METRIC_LOG_VERSION;
import static us.zoom.trace.common.Constants.MEASURE_TOPIC_RATE_LIMIT_METRIC;
import static us.zoom.trace.processor.ProcessorType.RATE_LIMIT_SAMPLING;

/**
 * @author: eason.jia
 * @date: 2024/9/9
 */
@Order(10)
@Component("rateLimitProcessor")
@ConditionalOnExpression("'${rate.limit.sampling.enable:false}'.equals('true')")
public class RateLimitProcessor extends BaseProcessor implements SystemParamObserver {

    private static final Logger logger = LoggerFactory.getLogger(RateLimitProcessor.class);
    private static final String OBSERVED_SYSTEM_PARAM_KEY = "rateLimitProcessor";
    private static final String GLOBAL_PERMIT_PER_SECOND = "global_permit_per_second";
    private static final String DEFAULT_TOPIC_PERMIT_PER_SECOND = "default_topic_permit_per_second";
    private static final String TOPIC_PERMIT_PER_SECOND = "topic_permit_per_second";

    private boolean enableGlobalRateLimit = false;
    private boolean enableDefaultTopicRateLimit = false;
    private int globalPermitsPerSecond = 100000;
    private int defaultTopicPermitPerSecond = 100;
    private RateLimiter globalRateLimiter = RateLimiter.create(globalPermitsPerSecond);
    private Map<String, RateLimiter> rateLimiterMap = new HashMap<>();
    private Map<String, RateLimiter> defaultTopicRateLimiterMap = Maps.newConcurrentMap();

    @Autowired
    private LogRateLimiter logRateLimiter;

    @PostConstruct
    public void init() {
        TraceSystemParamCache.addObserver(this);
    }

    @Override
    public ProcessorType getType() {
        return RATE_LIMIT_SAMPLING;
    }

    @Override
    public boolean process(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) throws ProcessException {
        // global dimension, does not distinguish topic
        if (enableGlobalRateLimit) {
            return globalRateLimiter.tryAcquire();
        }
        // topic dimension
        if (rateLimiterMap.containsKey(context.getTopicName())) {
            return rateLimiterMap.get(context.getTopicName()).tryAcquire();
        }
        // topic dimension, default permit
        if (enableDefaultTopicRateLimit) {
            RateLimiter rateLimiter = defaultTopicRateLimiterMap.computeIfAbsent(context.getTopicName(), k -> RateLimiter.create(defaultTopicPermitPerSecond));
            return rateLimiter.tryAcquire();
        }
        return true;
    }

    @Override
    public String getObservedKey() {
        return OBSERVED_SYSTEM_PARAM_KEY;
    }

    @Override
    public void onFalse(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) {
        if (logRateLimiter.getPermitByKey(this.getClass().getName())) {
            MonitorLogUtils.print(MonitorLog.builder()
                    .withCubeVer(CUBE_METRIC_LOG_VERSION)
                    .withMeasure(MEASURE_TOPIC_RATE_LIMIT_METRIC)
                    .withTs(Instant.now().toEpochMilli())
                    .addTag("topic", context.getTopicName())
                    .build());
        }
    }

    @Override
    public void onChange(String param) {
        try {
            Map<String, Object> paramMap = JsonUtils.parse(param, new TypeReference<>() {
            });
            if (paramMap.containsKey(GLOBAL_PERMIT_PER_SECOND)) {
                globalPermitsPerSecond = Integer.parseInt(paramMap.get(GLOBAL_PERMIT_PER_SECOND).toString());
                globalRateLimiter.setRate(globalPermitsPerSecond);
                enableGlobalRateLimit = true;
                logger.info("globalPermitsPerSecond set to {} and enableGlobalRateLimit set to true", globalPermitsPerSecond);
                return;
            } else {
                enableGlobalRateLimit = false;
                logger.info("enableGlobalRateLimit set to false");
            }
            if (paramMap.containsKey(TOPIC_PERMIT_PER_SECOND)) {
                Map<String, Integer> topicPermitRateLimit = (Map<String, Integer>) paramMap.get(TOPIC_PERMIT_PER_SECOND);
                logger.info("topic_permit_per_second is: {}", topicPermitRateLimit);
                Map<String, RateLimiter> tmpRateLimiterMap = new HashMap<>();
                for (Map.Entry<String, Integer> entry : topicPermitRateLimit.entrySet()) {
                    tmpRateLimiterMap.put(entry.getKey(), RateLimiter.create(entry.getValue()));
                }
                rateLimiterMap = tmpRateLimiterMap;
                for (String topicName : rateLimiterMap.keySet()) {
                    // remove in default map if specific rate limit rate
                    defaultTopicRateLimiterMap.remove(topicName);
                }
                logger.info("rateLimiterMap set to {}", rateLimiterMap);
            }
            if (paramMap.containsKey(DEFAULT_TOPIC_PERMIT_PER_SECOND)) {
                defaultTopicPermitPerSecond = (int) paramMap.get(DEFAULT_TOPIC_PERMIT_PER_SECOND);
                defaultTopicRateLimiterMap.forEach((topic, rateLimiter) -> rateLimiter.setRate(defaultTopicPermitPerSecond));
                enableDefaultTopicRateLimit = true;
                logger.info("defaultTopicPermitPerSecond set to {} and enableGlobalRateLimit set to true", defaultTopicPermitPerSecond);
            } else {
                enableDefaultTopicRateLimit = false;
                defaultTopicRateLimiterMap.clear();
                logger.info("enableDefaultTopicRateLimit set to false");
            }
        } catch (JsonProcessingException e) {
            logger.error("failed to parse json string: {}", param, e);
        }
    }
}
