package us.zoom.trace.processor.impl;

import com.google.common.collect.Maps;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.Span;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.trace.biz.longtimestorage.LongTimeStorageRuleMatcher;
import us.zoom.trace.biz.longtimestorage.LongTimeStorageTraceIdCache;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.exception.ProcessException;
import us.zoom.trace.monitor.MetricReporterManager;
import us.zoom.trace.monitor.RuleMatcherMetric;
import us.zoom.trace.processor.ProcessorType;
import us.zoom.trace.util.JsonUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static us.zoom.trace.processor.ProcessorType.LONG_TIME_STORAGE_SAMPLING;

/**
 * sampling for long time storage
 *
 * @author: eason.jia
 * @date: 2024/8/2
 */
@Order(100)
@Component("longTimeStorageSamplingProcessor")
@ConditionalOnExpression("'${cube.trace.consume.delay.enable:false}'.equals('false') && '${long.time.storage.sampling.enable:false}'.equals('true')")
public class LongTimeStorageSamplingProcessor extends BaseProcessor {
    private static final Logger logger = LoggerFactory.getLogger(LongTimeStorageSamplingProcessor.class);


    private static final long DEFAULT_DELAY_SECONDS = 60;
    @Autowired
    private List<LongTimeStorageRuleMatcher> ruleMatchers;
    @Autowired
    private LongTimeStorageTraceIdCache longTimeStorageTraceIdCache;

    @Override
    public ProcessorType getType() {
        return LONG_TIME_STORAGE_SAMPLING;
    }

    @Override
    public boolean process(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) throws ProcessException {
        try {
            for (LongTimeStorageRuleMatcher ruleMatcher : ruleMatchers) {
                if (ruleMatcher.isMatch(context, otelSpan, attributeMap)) {
                    long periodTime = longTimeStorageTraceIdCache.addTraceId(otelSpan.getTraceId().toByteArray(),
                            otelSpan.getStartTimeUnixNano());
                    RuleMatcherMetric metric = RuleMatcherMetric.builder()
                            .periodTime(periodTime)
                            .ruleCounters(Maps.newConcurrentMap())
                            .delaySeconds(DEFAULT_DELAY_SECONDS)
                            .build();
                    metric = MetricReporterManager.registerDelayMetricIfAbsent(metric);
                    metric.update(m -> {
                        RuleMatcherMetric ruleMatcherMetric = (RuleMatcherMetric) m;
                        Map<String, AtomicInteger> ruleCounters = ruleMatcherMetric.getRuleCounters();
                        String simpleName = ruleMatcher.getClass().getSimpleName();
                        AtomicInteger counter = ruleCounters.get(simpleName);
                        if (counter == null) {
                            counter = ruleCounters.computeIfAbsent(simpleName, k -> new AtomicInteger());
                        }
                        counter.incrementAndGet();
                    });
                    break;
                }
            }
        } catch (Throwable e) {
            logger.error("failed to process data: {}", JsonUtils.toJsonStringIgnoreException(otelSpan), e);
        }
        return true;
    }
}
