package us.zoom.trace.processor;

import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.Span;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.exception.ProcessException;

import java.util.Map;

/**
 * Singleton in one pipeline, Stateless
 *
 * @author: eason.jia
 * @date: 2024/7/31
 */
public interface Processor {

    /**
     * get processor type
     *
     * @return
     */
    ProcessorType getType();

    /**
     * Only be called once in the life cycle
     */
    void init();

    /**
     * Will be called before data processing
     *
     * @param context
     * @param attributeMap
     */
    default void preProcess(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) {

    }

    /**
     * Event processing method
     *
     * @param context
     * @param otelSpan
     * @param attributeMap
     * @param otelScope
     * @param otelResource
     * @throws ProcessException
     */
    boolean process(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) throws ProcessException;

    /**
     * Will be called after every event processing
     *
     * @param context
     * @param attributeMap
     */
    default void postProcess(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) {

    }

    /**
     * Will be called when handler execution return false, not throw exception
     *
     * @param context
     * @param attributeMap
     */
    default void onFalse(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) {

    }

    /**
     * Will be called when process execution throws exception
     *
     * @param context
     * @param attributeMap
     * @param cause
     */
    default void onException(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource, Throwable cause) {

    }

    /**
     * Will be called anyway, just like the Java's keyword 'finally'
     *
     * @param context
     * @param attributeMap
     */
    default void onFinally(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) {

    }

    default boolean needSkipWhenFailed() {
        return false;
    }
}
