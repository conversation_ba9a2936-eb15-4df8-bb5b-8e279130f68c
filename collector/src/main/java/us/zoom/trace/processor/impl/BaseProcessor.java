package us.zoom.trace.processor.impl;

import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.Span;
import us.zoom.trace.common.Context;
import us.zoom.trace.processor.Processor;

import java.util.Map;


/**
 * @author: eason.jia
 * @date: 2024/8/2
 */
public abstract class BaseProcessor implements Processor {

    @Override
    public void init() {

    }

    @Override
    public void preProcess(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) {

    }

    @Override
    public void onFinally(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) {

    }
}
