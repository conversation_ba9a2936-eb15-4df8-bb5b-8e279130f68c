package us.zoom.trace.processor.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.netty.handler.codec.http.HttpScheme;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.InstrumentationScope;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.Span;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import us.zoom.trace.common.Context;
import us.zoom.trace.common.enums.BasicAttribute;
import us.zoom.trace.common.exception.ProcessException;
import us.zoom.trace.processor.ProcessorType;
import us.zoom.trace.util.OtelUtils;

import java.lang.invoke.MethodHandles;
import java.lang.invoke.VarHandle;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: adan.zeng
 * @date: 2024/11/29
 */
@Order(20)
@Component("renameSpanProcessor")
@ConditionalOnExpression("'${rename.processor.enable:true}'.equals('true')")
public class RenameSpanProcessor extends BaseProcessor {

    private static List<RenameInnerProcessor> innerProcessorList = Lists.newArrayList(
            new RenameSpanNameInnerProcessor(),
            new RenameDbSystemProcessor()
    );

    @Override
    public ProcessorType getType() {
        return ProcessorType.RENAME_SPAN_PROCESSOR;
    }

    @Override
    public boolean process(Context context, Span otelSpan, Map<String, AnyValue> attributeMap, InstrumentationScope otelScope, Resource otelResource) throws ProcessException {
        innerProcessorList.forEach(processor -> {
            Set<String> keys = processor.getKeys();
            if (keys == null || keys.isEmpty()) {
                return;
            }
            for (String key : keys) {
                if (!attributeMap.containsKey(key)) {
                    return;
                }
            }
            processor.rename(otelSpan, attributeMap);
        });
        return true;
    }

    interface RenameInnerProcessor {
        void rename(Span span, Map<String, AnyValue> attributeMap);

        Set<String> getKeys();
    }

    static class RenameSpanNameInnerProcessor implements RenameInnerProcessor {

        private static final Set<String> HTTP_CLIENT_KEYS = Sets.newHashSet(
                BasicAttribute.HTTP_REQUEST_METHOD.getKeyName(),
                BasicAttribute.SERVER_ADDRESS.getKeyName(),
                BasicAttribute.SERVER_PORT.getKeyName(),
                BasicAttribute.URL_FULL.getKeyName()
        );

        private static final VarHandle FIELD_HANDLE;

        static {
            try {
                Class<?> messageClass = Class.forName("io.opentelemetry.proto.trace.v1.Span");
                MethodHandles.Lookup lookup = MethodHandles.privateLookupIn(messageClass, MethodHandles.lookup());
                FIELD_HANDLE = lookup.findVarHandle(messageClass, "name_", Object.class);
            } catch (Exception e) {
                throw new RuntimeException("initialize varHandler failed", e);
            }
        }

        private static void setName(Object messageInstance, String value) {
            FIELD_HANDLE.set(messageInstance, value);
        }


        @Override
        public void rename(Span span, Map<String, AnyValue> attributeMap) {
            if (Span.SpanKind.SPAN_KIND_CLIENT_VALUE != span.getKindValue()) {
                return;
            }
            String requestMethod = getActualValueAsString(attributeMap.get(BasicAttribute.HTTP_REQUEST_METHOD.getKeyName()));
            String serverAddr = getActualValueAsString(attributeMap.get(BasicAttribute.SERVER_ADDRESS.getKeyName()));
            String serverPort = getActualValueAsString(attributeMap.get(BasicAttribute.SERVER_PORT.getKeyName()));
            String urlFull = getActualValueAsString(attributeMap.get(BasicAttribute.URL_FULL.getKeyName()));
            if (!requestMethod.equals(span.getName())) {
                return;
            }
            if ((serverPort == null || Integer.parseInt(serverPort) < 0) && urlFull != null) {
                if (urlFull.startsWith(HttpScheme.HTTPS.toString())) {
                    serverPort = String.valueOf(HttpScheme.HTTPS.port());
                } else if (urlFull.startsWith(HttpScheme.HTTP.toString())) {
                    serverPort = String.valueOf(HttpScheme.HTTP.port());
                }
            }
            if (serverPort == null) {
                setName(span, Joiner.on(StringUtils.SPACE).join(requestMethod, serverAddr));
            } else {
                setName(span, Joiner.on(StringUtils.SPACE).join(requestMethod, serverAddr, serverPort));
            }
        }

        @Override
        public Set<String> getKeys() {
            return HTTP_CLIENT_KEYS;
        }
    }

    static class RenameDbSystemProcessor implements RenameInnerProcessor {

        private static final Set<String> UNKNOWN_SYSTEM_SET = Sets.newHashSet("other_sql");

        private static final Set<String> DB_KEYS = Sets.newHashSet(
                BasicAttribute.DB_SYSTEM.getKeyName(),
                BasicAttribute.DB_CONNECTION_STRING.getKeyName()
        );

        private static final String JDBC = "jdbc";

        private static final String COLON = ":";

        @Override
        public void rename(Span span, Map<String, AnyValue> attributeMap) {
            if (Span.SpanKind.SPAN_KIND_CLIENT_VALUE != span.getKindValue()) {
                return;
            }
            AnyValue dbSystem = attributeMap.get(BasicAttribute.DB_SYSTEM.getKeyName());
            if (!UNKNOWN_SYSTEM_SET.contains(getActualValueAsString(dbSystem))) {
                return;
            }
            AnyValue connectionString = attributeMap.get(BasicAttribute.DB_CONNECTION_STRING.getKeyName());
            String[] split = getActualValueAsString(connectionString).split(COLON);
            for (String key : split) {
                if (key.equals(JDBC)) {
                    continue;
                }
                AnyValue.Builder builder = dbSystem.toBuilder();
                builder.setStringValue(key);
                attributeMap.put(BasicAttribute.DB_SYSTEM.getKeyName(), builder.build());
                break;
            }
        }

        @Override
        public Set<String> getKeys() {
            return DB_KEYS;
        }
    }

    private static String getActualValueAsString(AnyValue attribute) {
        Object actualValue = OtelUtils.getActualValue(attribute);
        if (actualValue == null) {
            throw new RuntimeException("attribute value shouldn't be null");
        }
        return actualValue.toString();
    }

}
