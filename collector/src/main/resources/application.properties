# port
server.port=8080

# async mq
async.mq.endpoint=https://asyncmq.zoomdev.us
async.mq.username=app_hub
async.mq.password=

# system param
unit.tag.name=adan_local
cube.config.client.endpoint=https://cubeconfig-perf-new.zoomdev.us
cube.config.dao.enable=false
#cube.trace.environment=eason-local-main
cube.trace.environment=local

# actuator
management.health.defaults.enabled=false
management.health.ping.enabled=true
management.endpoint.health.show-components=always
management.endpoint.health.show-details=always
management.endpoints.web.exposure.include=info,prometheus,up,health

# apm
apm.log.home=logs/cube-trace-collector/zoom_middleware/apm/Infra_Monitor_Cube-Trace
monitor.executorsReplace.enabled=false

# sampling
long.time.storage.sampling.enable=true
rate.limit.sampling.enable=true

#redis
spring.data.redis.timeout=5000
spring.data.redis.ssl.enabled=false
spring.data.redis.host=127.0.0.1
spring.data.redis.port=6379