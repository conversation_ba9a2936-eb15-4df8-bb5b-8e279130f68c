DROP TABLE IF EXISTS signoz_traces.signoz_spans;

CREATE TABLE signoz_traces.signoz_spans
(
    `time`      DateTime,
    `timestamp` DateTime,
    `traceID`   String,
    `model`     String
)
    ENGINE = ReplicatedMergeTree(
            '/clickhouse/tables/{shard}/signoz_traces/signoz_spans.875d8bdaac344fafb71dbb53c09df71b',
            '{replica}')
        PARTITION BY toDate(timestamp)
        ORDER BY traceID
        TTL timestamp + toIntervalDay(3)
        SETTINGS index_granularity = 1024, ttl_only_drop_parts = 1