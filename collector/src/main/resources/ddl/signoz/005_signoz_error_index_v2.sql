DROP TABLE IF EXISTS signoz_traces.signoz_error_index_v2;

CREATE TABLE signoz_traces.signoz_error_index_v2
(
    `time`                DateTime,
    `timestamp`           DateTime,
    `errorID`             String,
    `groupID`             String,
    `traceID`             String,
    `spanID`              String,
    `serviceName`         LowCardinality(String),
    `exceptionType`       LowCardinality(String),
    `exceptionMessage`    String,
    `exceptionStacktrace` String,
    `exceptionEscaped`    Bool,
    `resourceTagsMap`     Map(String, String),
    INDEX idx_error_id errorID TYPE bloom_filter GRANULARITY 4,
    INDEX idx_resourceTagsMapKeys mapKeys(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_resourceTagsMapValues mapValues(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64
)
    ENGINE = ReplicatedMergeTree(
            '/clickhouse/tables/{shard}/signoz_traces/signoz_error_index_v2.b3c5637a58314ef491a10b38aaec45b1',
            '{replica}')
        PARTITION BY toDate(timestamp)
        ORDER BY (timestamp, groupID)
        TTL timestamp + toIntervalDay(3)
        SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1