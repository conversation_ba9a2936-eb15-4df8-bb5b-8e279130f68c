CREATE DATABASE IF NOT EXISTS signoz_traces;


show databases ;

-- signoz_index_v2
DROP TABLE IF EXISTS signoz_traces.signoz_index_v2;
create table signoz_traces.signoz_index_v2 ON CLUSTER '{cluster}'
(
    time               DateTime,
    timestamp          DateTime64(9),
    traceID            String,
    spanID             String,
    parentSpanID       String,
    serviceName        LowCardinality(String),
    name               LowCardinality(String),
    kind               Int8,
    durationNano       UInt64,
    statusCode         Int16,
    externalHttpMethod LowCardinality(String),
    externalHttpUrl    LowCardinality(String),
    dbSystem           LowCardinality(String),
    dbName             LowCardinality(String),
    dbOperation        LowCardinality(String),
    peerService        LowCardinality(String),
    events             Array(String),
    httpMethod         LowCardinality(String),
    httpUrl            LowCardinality(String),
    httpRoute          LowCardinality(String),
    httpHost           LowCardinality(String),
    msgSystem          LowCardinality(String),
    msgOperation       LowCardinality(String),
    hasError           Bool,
    rpcSystem          LowCardinality(String),
    rpcService         LowCardinality(String),
    rpcMethod          LowCardinality(String),
    responseStatusCode LowCardinality(String),
    stringTagMap       Map(String, String),
    numberTagMap       Map(String, Float64),
    boolTagMap         Map(String, Bool),
    resourceTagsMap    Map(String, String),
    isRemote           LowCardinality(String),
    statusMessage      String,
    statusCodeString   String,
    spanKind           String,
    INDEX idx_service serviceName TYPE bloom_filter GRANULARITY 4,
    INDEX idx_name name TYPE bloom_filter GRANULARITY 4,
    INDEX idx_kind kind TYPE minmax GRANULARITY 4,
    INDEX idx_duration durationNano TYPE minmax GRANULARITY 1,
    INDEX idx_hasError hasError TYPE set(2) GRANULARITY 1,
    INDEX idx_httpRoute httpRoute TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpUrl httpUrl TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpHost httpHost TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpMethod httpMethod TYPE bloom_filter GRANULARITY 4,
    INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1,
    INDEX idx_rpcMethod rpcMethod TYPE bloom_filter GRANULARITY 4,
    INDEX idx_responseStatusCode responseStatusCode TYPE set(0) GRANULARITY 1,
    INDEX idx_resourceTagsMapKeys mapKeys(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_resourceTagsMapValues mapValues(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_statusCodeString statusCodeString TYPE set(3) GRANULARITY 4,
    INDEX idx_spanKind spanKind TYPE set(5) GRANULARITY 4
)
    engine = ReplicatedMergeTree('/clickhouse/tables/{shard}/signoz_traces/signoz_index_v2.3bfb488464f54a0d97886434e7fafd93',
                                 '{replica}') PARTITION BY toDate(timestamp)
        PRIMARY KEY (serviceName, hasError, toStartOfHour(timestamp), name)
        ORDER BY (serviceName, hasError, toStartOfHour(timestamp), name, timestamp)
        TTL time + toIntervalDay(2)
        SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;


-- signoz_error_index_v2
DROP TABLE IF EXISTS signoz_traces.signoz_error_index_v2;
create table signoz_traces.signoz_error_index_v2 ON CLUSTER '{cluster}'
(
    time                DateTime,
    timestamp           DateTime64(9),
    errorID             String,
    groupID             String,
    traceID             String,
    spanID              String,
    serviceName         LowCardinality(String),
    exceptionType       LowCardinality(String),
    exceptionMessage    String,
    exceptionStacktrace String,
    exceptionEscaped    Bool,
    resourceTagsMap     Map(String, String),
    INDEX idx_error_id errorID TYPE bloom_filter GRANULARITY 4,
    INDEX idx_resourceTagsMapKeys mapKeys(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_resourceTagsMapValues mapValues(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64
)
    engine = ReplicatedMergeTree(
            '/clickhouse/tables/{shard}/signoz_traces/signoz_error_index_v2.0f853f0ff9af43a48a98ad6b84a11e31',
            '{replica}') PARTITION BY toDate(timestamp)
        ORDER BY (timestamp, groupID)
        TTL time + toIntervalDay(2)
        SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;

-- signoz_spans
DROP TABLE IF EXISTS signoz_traces.signoz_spans;
create table signoz_traces.signoz_spans ON CLUSTER '{cluster}'
(
    time      DateTime,
    timestamp DateTime64(9),
    traceID   String,
    model     String
)
    engine = ReplicatedMergeTree('/clickhouse/tables/{shard}/signoz_traces/signoz_spans.bb918c73530f413bbbe3b22233231682',
                                 '{replica}') PARTITION BY toDate(timestamp)
        ORDER BY traceID
        TTL time + toIntervalDay(2)
        SETTINGS index_granularity = 1024, ttl_only_drop_parts = 1;


-- span_attributes
DROP TABLE IF EXISTS signoz_traces.span_attributes;
create table signoz_traces.span_attributes ON CLUSTER '{cluster}'
(
    time            DateTime,
    timestamp       DateTime,
    tagKey          LowCardinality(String),
    tagType         Enum8('tag' = 1, 'resource' = 2),
    dataType        Enum8('string' = 1, 'bool' = 2, 'float64' = 3),
    stringTagValue  String,
    float64TagValue Nullable(Float64),
    isColumn        Bool
)
    engine = ReplicatedReplacingMergeTree(
            '/clickhouse/tables/{shard}/signoz_traces/span_attributes.5ef6e0ff9b4e4f29aeefe21371225690',
            '{replica}')
        ORDER BY (tagKey, tagType, dataType, stringTagValue, float64TagValue, isColumn)
        TTL time + toIntervalDay(2)
        SETTINGS ttl_only_drop_parts = 1, allow_nullable_key = 1, index_granularity = 8192;

-- span_attributes_keys
DROP TABLE IF EXISTS signoz_traces.span_attributes_keys;
create table signoz_traces.span_attributes_keys ON CLUSTER '{cluster}'
(
    time     DateTime,
    tagKey   LowCardinality(String),
    tagType  Enum8('tag' = 1, 'resource' = 2),
    dataType Enum8('string' = 1, 'bool' = 2, 'float64' = 3),
    isColumn Bool
)
    engine = ReplicatedReplacingMergeTree(
            '/clickhouse/tables/{shard}/signoz_traces/span_attributes_keys.b834d39538e24ff9a021264c24b78094',
            '{replica}')
        ORDER BY (tagKey, tagType, dataType, isColumn) SETTINGS index_granularity = 8192;

-- top_level_operations
DROP TABLE IF EXISTS signoz_traces.top_level_operations;
create table signoz_traces.top_level_operations ON CLUSTER '{cluster}'
(
    name        LowCardinality(String),
    serviceName LowCardinality(String),
    time        DateTime default now()
)
    engine = ReplicatedReplacingMergeTree(
            '/clickhouse/tables/{shard}/signoz_traces/top_level_operations.0e80215305d94e45a65e5ddcae669e03',
            '{replica}')
        ORDER BY (serviceName, name)
        TTL time + toIntervalMonth(1)
        SETTINGS index_granularity = 8192;

-- usage_explorer
DROP TABLE IF EXISTS signoz_traces.usage_explorer;
create table signoz_traces.usage_explorer ON CLUSTER '{cluster}'
(
    timestamp    DateTime64(9),
    service_name LowCardinality(String),
    count        UInt64
)
    engine = ReplicatedSummingMergeTree(
            '/clickhouse/tables/{shard}/signoz_traces/usage_explorer.cdee192573094bf79abd8aeffeffa7e9',
            '{replica}') PARTITION BY toDate(timestamp)
        ORDER BY (timestamp, service_name)
        TTL toDateTime(timestamp) + toIntervalDay(3)
        SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;


-- dependency_graph_minutes_v2
DROP TABLE IF EXISTS signoz_traces.dependency_graph_minutes_v2;
create table signoz_traces.dependency_graph_minutes_v2 ON CLUSTER '{cluster}'
(
    src                      LowCardinality(String),
    dest                     LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              SimpleAggregateFunction(sum, UInt64),
    total_count              SimpleAggregateFunction(sum, UInt64),
    timestamp                DateTime,
    deployment_environment   LowCardinality(String),
    k8s_cluster_name         LowCardinality(String),
    k8s_namespace_name       LowCardinality(String)
)
    engine = ReplicatedAggregatingMergeTree(
            '/clickhouse/tables/{shard}/signoz_traces/dependency_graph_minutes_v2.f753d31a6405413294269bf2c9ef11c0',
            '{replica}') PARTITION BY toDate(timestamp)
        ORDER BY (timestamp, src, dest, deployment_environment, k8s_cluster_name, k8s_namespace_name)
        TTL timestamp + toIntervalDay(3)
        SETTINGS index_granularity = 8192;

-- distributed_signoz_index_v2
DROP TABLE IF EXISTS signoz_traces.distributed_signoz_index_v2;
create table signoz_traces.distributed_signoz_index_v2 ON CLUSTER '{cluster}'
(
    time               DateTime,
    timestamp          DateTime64(9),
    traceID            String,
    spanID             String,
    parentSpanID       String,
    serviceName        LowCardinality(String),
    name               LowCardinality(String),
    kind               Int8,
    durationNano       UInt64,
    statusCode         Int16,
    externalHttpMethod LowCardinality(String),
    externalHttpUrl    LowCardinality(String),
    dbSystem           LowCardinality(String),
    dbName             LowCardinality(String),
    dbOperation        LowCardinality(String),
    peerService        LowCardinality(String),
    events             Array(String),
    httpMethod         LowCardinality(String),
    httpUrl            LowCardinality(String),
    httpRoute          LowCardinality(String),
    httpHost           LowCardinality(String),
    msgSystem          LowCardinality(String),
    msgOperation       LowCardinality(String),
    hasError           Bool,
    rpcSystem          LowCardinality(String),
    rpcService         LowCardinality(String),
    rpcMethod          LowCardinality(String),
    responseStatusCode LowCardinality(String),
    stringTagMap       Map(String, String),
    numberTagMap       Map(String, Float64),
    boolTagMap         Map(String, Bool),
    resourceTagsMap    Map(String, String),
    isRemote           LowCardinality(String),
    statusMessage      String,
    statusCodeString   String,
    spanKind           String
)
    engine = Distributed('{cluster}', 'signoz_traces', 'signoz_index_v2', cityHash64(traceID));

-- distributed_signoz_error_index_v2
DROP TABLE IF EXISTS signoz_traces.distributed_signoz_error_index_v2;
create table signoz_traces.distributed_signoz_error_index_v2 ON CLUSTER '{cluster}'
(
    time                DateTime,
    timestamp           DateTime64(9),
    errorID             String,
    groupID             String,
    traceID             String,
    spanID              String,
    serviceName         LowCardinality(String),
    exceptionType       LowCardinality(String),
    exceptionMessage    String,
    exceptionStacktrace String,
    exceptionEscaped    Bool,
    resourceTagsMap     Map(String, String)
)
    engine = Distributed('{cluster}', 'signoz_traces', 'signoz_error_index_v2', cityHash64(groupID));


-- distributed_signoz_spans
DROP TABLE IF EXISTS signoz_traces.distributed_signoz_spans;
create table signoz_traces.distributed_signoz_spans ON CLUSTER '{cluster}'
(
    time      DateTime,
    timestamp DateTime64(9),
    traceID   String,
    model     String
)
    engine = Distributed('{cluster}', 'signoz_traces', 'signoz_spans', cityHash64(traceID));

-- distributed_span_attributes
DROP TABLE IF EXISTS signoz_traces.distributed_span_attributes;
create table signoz_traces.distributed_span_attributes ON CLUSTER '{cluster}'
(
    time            DateTime,
    timestamp       DateTime,
    tagKey          LowCardinality(String),
    tagType         Enum8('tag' = 1, 'resource' = 2),
    dataType        Enum8('string' = 1, 'bool' = 2, 'float64' = 3),
    stringTagValue  String,
    float64TagValue Nullable(Float64),
    isColumn        Bool
)
    engine = Distributed('{cluster}', 'signoz_traces', 'span_attributes', cityHash64(rand()));

-- distributed_span_attributes_keys
DROP TABLE IF EXISTS signoz_traces.distributed_span_attributes_keys;
create table signoz_traces.distributed_span_attributes_keys ON CLUSTER '{cluster}'
(
    time     DateTime,
    tagKey   LowCardinality(String),
    tagType  Enum8('tag' = 1, 'resource' = 2),
    dataType Enum8('string' = 1, 'bool' = 2, 'float64' = 3),
    isColumn Bool
)
    engine = Distributed('{cluster}', 'signoz_traces', 'span_attributes_keys', cityHash64(rand()));

-- distributed_top_level_operations
DROP TABLE IF EXISTS signoz_traces.distributed_top_level_operations;
create table signoz_traces.distributed_top_level_operations ON CLUSTER '{cluster}'
(
    name        LowCardinality(String),
    serviceName LowCardinality(String),
    time        DateTime default now()
)
    engine = Distributed('{cluster}', 'signoz_traces', 'top_level_operations', cityHash64(rand()));

-- distributed_usage_explorer
DROP TABLE IF EXISTS signoz_traces.distributed_usage_explorer;
create table signoz_traces.distributed_usage_explorer ON CLUSTER '{cluster}'
(
    timestamp    DateTime64(9),
    service_name LowCardinality(String),
    count        UInt64
)
    engine = Distributed('{cluster}', 'signoz_traces', 'usage_explorer', cityHash64(rand()));

-- distributed_dependency_graph_minutes_v2
DROP TABLE IF EXISTS signoz_traces.distributed_dependency_graph_minutes_v2;
create table signoz_traces.distributed_dependency_graph_minutes_v2 ON CLUSTER '{cluster}'
(
    src                      LowCardinality(String),
    dest                     LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              SimpleAggregateFunction(sum, UInt64),
    total_count              SimpleAggregateFunction(sum, UInt64),
    timestamp                DateTime,
    deployment_environment   LowCardinality(String),
    k8s_cluster_name         LowCardinality(String),
    k8s_namespace_name       LowCardinality(String)
)
    engine = Distributed('{cluster}', 'signoz_traces', 'dependency_graph_minutes_v2', cityHash64(rand()));


-- dependency_graph_minutes_db_calls_mv_v2
DROP VIEW IF EXISTS signoz_traces.dependency_graph_minutes_db_calls_mv_v2;
CREATE MATERIALIZED VIEW signoz_traces.dependency_graph_minutes_db_calls_mv_v2 ON CLUSTER '{cluster}'
        TO signoz_traces.dependency_graph_minutes_v2
        (
         `src` LowCardinality(String),
         `dest` LowCardinality(String),
         `duration_quantiles_state` AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
         `error_count` UInt64,
         `total_count` UInt64,
         `timestamp` DateTime,
         `deployment_environment` String,
         `k8s_cluster_name` String,
         `k8s_namespace_name` String
            )
AS
SELECT serviceName                                                         AS src,
       dbSystem                                                            AS dest,
       quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano)) AS duration_quantiles_state,
       countIf(statusCode = 2)                                             AS error_count,
       count(*)                                                            AS total_count,
       toStartOfMinute(timestamp)                                          AS timestamp,
       resourceTagsMap['deployment.environment']                           AS deployment_environment,
       resourceTagsMap['k8s.cluster.name']                                 AS k8s_cluster_name,
       resourceTagsMap['k8s.namespace.name']                               AS k8s_namespace_name
FROM signoz_traces.signoz_index_v2
WHERE (dest != '')
  AND (kind != 2)
GROUP BY timestamp,
         src,
         dest,
         deployment_environment,
         k8s_cluster_name,
         k8s_namespace_name;

-- dependency_graph_minutes_messaging_calls_mv_v2
DROP VIEW IF EXISTS signoz_traces.dependency_graph_minutes_messaging_calls_mv_v2;
CREATE MATERIALIZED VIEW signoz_traces.dependency_graph_minutes_messaging_calls_mv_v2 ON CLUSTER '{cluster}'
        TO signoz_traces.dependency_graph_minutes_v2
        (
         `src` LowCardinality(String),
         `dest` LowCardinality(String),
         `duration_quantiles_state` AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
         `error_count` UInt64,
         `total_count` UInt64,
         `timestamp` DateTime,
         `deployment_environment` String,
         `k8s_cluster_name` String,
         `k8s_namespace_name` String
            )
AS
SELECT serviceName                                                         AS src,
       msgSystem                                                           AS dest,
       quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano)) AS duration_quantiles_state,
       countIf(statusCode = 2)                                             AS error_count,
       count(*)                                                            AS total_count,
       toStartOfMinute(timestamp)                                          AS timestamp,
       resourceTagsMap['deployment.environment']                           AS deployment_environment,
       resourceTagsMap['k8s.cluster.name']                                 AS k8s_cluster_name,
       resourceTagsMap['k8s.namespace.name']                               AS k8s_namespace_name
FROM signoz_traces.signoz_index_v2
WHERE (dest != '')
  AND (kind != 2)
GROUP BY timestamp,
         src,
         dest,
         deployment_environment,
         k8s_cluster_name,
         k8s_namespace_name;

-- dependency_graph_minutes_service_calls_mv_v2
DROP VIEW IF EXISTS signoz_traces.dependency_graph_minutes_service_calls_mv_v2;
CREATE MATERIALIZED VIEW signoz_traces.dependency_graph_minutes_service_calls_mv_v2 ON CLUSTER '{cluster}'
        TO signoz_traces.dependency_graph_minutes_v2
        (
         `src` LowCardinality(String),
         `dest` LowCardinality(String),
         `duration_quantiles_state` AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
         `error_count` UInt64,
         `total_count` UInt64,
         `timestamp` DateTime,
         `deployment_environment` String,
         `k8s_cluster_name` String,
         `k8s_namespace_name` String
            )
AS
SELECT A.serviceName                                                         AS src,
       B.serviceName                                                         AS dest,
       quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(B.durationNano)) AS duration_quantiles_state,
       countIf(B.statusCode = 2)                                             AS error_count,
       count(*)                                                              AS total_count,
       toStartOfMinute(B.timestamp)                                          AS timestamp,
       B.resourceTagsMap['deployment.environment']                           AS deployment_environment,
       B.resourceTagsMap['k8s.cluster.name']                                 AS k8s_cluster_name,
       B.resourceTagsMap['k8s.namespace.name']                               AS k8s_namespace_name
FROM signoz_traces.signoz_index_v2 AS A,
     signoz_traces.signoz_index_v2 AS B
WHERE (A.serviceName != B.serviceName)
  AND (A.spanID = B.parentSpanID)
GROUP BY timestamp,
         src,
         dest,
         deployment_environment,
         k8s_cluster_name,
         k8s_namespace_name;

-- root_operations
DROP VIEW IF EXISTS signoz_traces.root_operations;
CREATE MATERIALIZED VIEW signoz_traces.root_operations ON CLUSTER '{cluster}'
        TO signoz_traces.top_level_operations
        (
         `name` LowCardinality(String),
         `serviceName` LowCardinality(String)
            )
AS
SELECT DISTINCT name,
                serviceName
FROM signoz_traces.signoz_index_v2
WHERE parentSpanID = '';

-- sub_root_operations
DROP VIEW IF EXISTS signoz_traces.sub_root_operations;
CREATE MATERIALIZED VIEW signoz_traces.sub_root_operations ON CLUSTER '{cluster}'
        TO signoz_traces.top_level_operations
        (
         `name` LowCardinality(String),
         `serviceName` LowCardinality(String)
            )
AS
SELECT DISTINCT name,
                serviceName
FROM signoz_traces.signoz_index_v2 AS A,
     signoz_traces.signoz_index_v2 AS B
WHERE (A.serviceName != B.serviceName)
  AND (A.parentSpanID = B.spanID);

-- usage_explorer_mv
DROP VIEW IF EXISTS signoz_traces.usage_explorer_mv;
CREATE MATERIALIZED VIEW signoz_traces.usage_explorer_mv ON CLUSTER '{cluster}'
        TO signoz_traces.usage_explorer
        (
         `timestamp` DateTime,
         `service_name` LowCardinality(String),
         `count` UInt64
            )
AS
SELECT toStartOfHour(timestamp) AS timestamp,
       serviceName              AS service_name,
       count()                  AS count
FROM signoz_traces.signoz_index_v2
GROUP BY timestamp,
         serviceName;