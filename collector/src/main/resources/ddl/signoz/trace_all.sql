DROP DATABASE IF EXISTS signoz_traces;

CREATE DATABASE IF NOT EXISTS signoz_traces;

DROP TABLE IF EXISTS signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas;

SET allow_experimental_projection_optimization = 0;
CREATE TABLE IF NOT EXISTS signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas (
    time DateTime,
    timestamp DateTime64(9) CODEC(DoubleDelta, LZ4),
    traceID String CODEC(ZSTD(1)),
    spanID String CODEC(ZSTD(1)),
    parentSpanID String CODEC(ZSTD(1)),
    serviceName LowCardinality(String) CODEC(ZSTD(1)),
    name LowCardinality(String) CODEC(ZSTD(1)),
    kind Int8 CODEC(T64, ZSTD(1)),
    durationNano UInt64 CODEC(T64, ZSTD(1)),
    statusCode Int16 CODEC(T64, ZSTD(1)),
    externalHttpMethod LowCardinality(String) CODEC(ZSTD(1)),
    externalHttpUrl LowCardinality(String) CODEC(ZSTD(1)),
    component LowCardinality(String) CODEC(ZSTD(1)),
    dbSystem LowCardinality(String) CODEC(ZSTD(1)),
    dbName LowCardinality(String) CODEC(ZSTD(1)),
    dbOperation LowCardinality(String) CODEC(ZSTD(1)),
    peerService LowCardinality(String) CODEC(ZSTD(1)),
    events Array(String) CODEC(ZSTD(2)),
    httpMethod LowCardinality(String) CODEC(ZSTD(1)),
    httpUrl LowCardinality(String) CODEC(ZSTD(1)),
    httpCode LowCardinality(String) CODEC(ZSTD(1)),
    httpRoute LowCardinality(String) CODEC(ZSTD(1)),
    httpHost LowCardinality(String) CODEC(ZSTD(1)),
    msgSystem LowCardinality(String) CODEC(ZSTD(1)),
    msgOperation LowCardinality(String) CODEC(ZSTD(1)),
    hasError bool CODEC(T64, ZSTD(1)),
    tagMap Map(String, String),
    PROJECTION timestampSort (SELECT * ORDER BY timestamp),
    INDEX idx_service serviceName TYPE bloom_filter GRANULARITY 4,
    INDEX idx_name name TYPE bloom_filter GRANULARITY 4,
    INDEX idx_kind kind TYPE minmax GRANULARITY 4,
    INDEX idx_duration durationNano TYPE minmax GRANULARITY 1,
    INDEX idx_httpCode httpCode TYPE set(0) GRANULARITY 1,
    INDEX idx_hasError hasError TYPE set(2) GRANULARITY 1,
    INDEX idx_tagMapKeys mapKeys(tagMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_tagMapValues mapValues(tagMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_httpRoute httpRoute TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpUrl httpUrl TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpHost httpHost TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpMethod httpMethod TYPE bloom_filter GRANULARITY 4,
    INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1
    ) ENGINE ReplicatedMergeTree
    PARTITION BY toDate(timestamp)
    PRIMARY KEY (serviceName, hasError, toStartOfHour(timestamp), name)
    ORDER BY (serviceName, hasError, toStartOfHour(timestamp), name, timestamp)
    TTL time + toIntervalDay(3) DELETE SETTINGS index_granularity = 8192;

SET allow_experimental_projection_optimization = 1;

CREATE TABLE IF NOT EXISTS signoz_traces.signoz_spans ON CLUSTER hz_shards_2replicas (
    time DateTime,
    timestamp DateTime64(9) CODEC(DoubleDelta, LZ4),
    traceID String CODEC(ZSTD(1)),
    model String CODEC(ZSTD(9))
    ) ENGINE ReplicatedMergeTree
    PARTITION BY toDate(timestamp)
    ORDER BY traceID
    TTL time + toIntervalDay(3) DELETE SETTINGS index_granularity=1024;

ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS gRPCMethod LowCardinality(String) CODEC(ZSTD(1)), ADD COLUMN IF NOT EXISTS gRPCCode LowCardinality(String) CODEC(ZSTD(1));


CREATE TABLE IF NOT EXISTS signoz_traces.durationSort ON CLUSTER hz_shards_2replicas (
    timestamp DateTime64(9) CODEC(DoubleDelta, LZ4),
    traceID String CODEC(ZSTD(1)),
    spanID String CODEC(ZSTD(1)),
    parentSpanID String CODEC(ZSTD(1)),
    serviceName LowCardinality(String) CODEC(ZSTD(1)),
    name LowCardinality(String) CODEC(ZSTD(1)),
    kind Int8 CODEC(T64, ZSTD(1)),
    durationNano UInt64 CODEC(T64, ZSTD(1)),
    statusCode Int16 CODEC(T64, ZSTD(1)),
    component LowCardinality(String) CODEC(ZSTD(1)),
    httpMethod LowCardinality(String) CODEC(ZSTD(1)),
    httpUrl LowCardinality(String) CODEC(ZSTD(1)),
    httpCode LowCardinality(String) CODEC(ZSTD(1)),
    httpRoute LowCardinality(String) CODEC(ZSTD(1)),
    httpHost LowCardinality(String) CODEC(ZSTD(1)),
    gRPCCode LowCardinality(String) CODEC(ZSTD(1)),
    gRPCMethod LowCardinality(String) CODEC(ZSTD(1)),
    hasError bool CODEC(T64, ZSTD(1)),
    tagMap Map(String, String),
    INDEX idx_service serviceName TYPE bloom_filter GRANULARITY 4,
    INDEX idx_name name TYPE bloom_filter GRANULARITY 4,
    INDEX idx_kind kind TYPE minmax GRANULARITY 4,
    INDEX idx_duration durationNano TYPE minmax GRANULARITY 1,
    INDEX idx_httpCode httpCode TYPE set(0) GRANULARITY 1,
    INDEX idx_hasError hasError TYPE set(2) GRANULARITY 1,
    INDEX idx_tagMapKeys mapKeys(tagMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_tagMapValues mapValues(tagMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_httpRoute httpRoute TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpUrl httpUrl TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpHost httpHost TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpMethod httpMethod TYPE bloom_filter GRANULARITY 4,
    INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1
    ) ENGINE ReplicatedMergeTree
    PARTITION BY toDate(timestamp)
    ORDER BY (durationNano, timestamp)
    TTL toDateTime(timestamp) + toIntervalDay(3) DELETE SETTINGS index_granularity = 8192;

CREATE TABLE IF NOT EXISTS signoz_traces.signoz_error_index_v2 ON CLUSTER hz_shards_2replicas (
    time DateTime,
    timestamp DateTime64(9) CODEC(DoubleDelta, LZ4),
    errorID String CODEC(ZSTD(1)),
    groupID String CODEC(ZSTD(1)),
    traceID String CODEC(ZSTD(1)),
    spanID String CODEC(ZSTD(1)),
    serviceName LowCardinality(String) CODEC(ZSTD(1)),
    exceptionType LowCardinality(String) CODEC(ZSTD(1)),
    exceptionMessage String CODEC(ZSTD(1)),
    exceptionStacktrace String CODEC(ZSTD(1)),
    exceptionEscaped bool CODEC(T64, ZSTD(1)),
    INDEX idx_error_id errorID TYPE bloom_filter GRANULARITY 4
    ) ENGINE ReplicatedMergeTree
    PARTITION BY toDate(timestamp)
    ORDER BY (timestamp, groupID)
    TTL time + toIntervalDay(3) DELETE;

DROP TABLE IF EXISTS signoz_traces.durationSortMV ON CLUSTER hz_shards_2replicas;

ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS `rpcSystem` LowCardinality(String) CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS `rpcService` LowCardinality(String) CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS `rpcMethod` LowCardinality(String) CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS `responseStatusCode` LowCardinality(String) CODEC(ZSTD(1));



ALTER TABLE signoz_traces.durationSort ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS `rpcSystem` LowCardinality(String) CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS `rpcService` LowCardinality(String) CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS `rpcMethod` LowCardinality(String) CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS `responseStatusCode` LowCardinality(String) CODEC(ZSTD(1));



ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas
    ADD INDEX IF NOT EXISTS idx_rpcMethod rpcMethod TYPE bloom_filter GRANULARITY 4,
    ADD INDEX IF NOT EXISTS idx_responseStatusCode responseStatusCode TYPE set(0) GRANULARITY 1;


ALTER TABLE signoz_traces.durationSort ON CLUSTER hz_shards_2replicas
    ADD INDEX IF NOT EXISTS idx_rpcMethod rpcMethod TYPE bloom_filter GRANULARITY 4,
    ADD INDEX IF NOT EXISTS idx_responseStatusCode responseStatusCode TYPE set(0) GRANULARITY 1;

CREATE TABLE IF NOT EXISTS signoz_traces.usage_explorer ON CLUSTER hz_shards_2replicas (
    timestamp DateTime64(9) CODEC(DoubleDelta, LZ4),
    service_name LowCardinality(String) CODEC(ZSTD(1)),
    count UInt64 CODEC(T64, ZSTD(1))
    ) ENGINE ReplicatedSummingMergeTree
    PARTITION BY toDate(timestamp)
    ORDER BY (timestamp, service_name)
    TTL toDateTime(timestamp) + toIntervalDay(3) DELETE;



CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.usage_explorer_mv ON CLUSTER hz_shards_2replicas
TO signoz_traces.usage_explorer
AS SELECT
              toStartOfHour(timestamp) as timestamp,
              serviceName as service_name,
              count() as count
   FROM signoz_traces.signoz_index_v2
   GROUP BY timestamp, serviceName;

CREATE TABLE IF NOT EXISTS signoz_traces.top_level_operations ON CLUSTER hz_shards_2replicas (
    name LowCardinality(String) CODEC(ZSTD(1)),
    serviceName LowCardinality(String) CODEC(ZSTD(1))
    ) ENGINE ReplicatedReplacingMergeTree
    ORDER BY (serviceName, name);


CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.sub_root_operations ON CLUSTER hz_shards_2replicas
TO signoz_traces.top_level_operations
AS SELECT DISTINCT
              name,
              serviceName
   FROM signoz_traces.signoz_index_v2 AS A, signoz_traces.signoz_index_v2 AS B
   WHERE (A.serviceName != B.serviceName) AND (A.parentSpanID = B.spanID) AND (A.traceID = B.traceID);

CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.root_operations ON CLUSTER hz_shards_2replicas
TO signoz_traces.top_level_operations
AS SELECT DISTINCT
              name,
              serviceName
   FROM signoz_traces.signoz_index_v2
   WHERE parentSpanID = '';

CREATE TABLE IF NOT EXISTS signoz_traces.dependency_graph_minutes ON CLUSTER hz_shards_2replicas (
    src LowCardinality(String) CODEC(ZSTD(1)),
    dest LowCardinality(String) CODEC(ZSTD(1)),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64) CODEC(Default),
    error_count SimpleAggregateFunction(sum, UInt64) CODEC(T64, ZSTD(1)),
    total_count SimpleAggregateFunction(sum, UInt64) CODEC(T64, ZSTD(1)),
    timestamp DateTime CODEC(DoubleDelta, LZ4)
    ) ENGINE ReplicatedAggregatingMergeTree
    PARTITION BY toDate(timestamp)
    ORDER BY (timestamp, src, dest)
    TTL timestamp + toIntervalDay(3) DELETE;



CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.dependency_graph_minutes_service_calls_mv ON CLUSTER hz_shards_2replicas
TO signoz_traces.dependency_graph_minutes AS
SELECT
    A.serviceName as src,
    B.serviceName as dest,
    quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(B.durationNano)) as duration_quantiles_state,
        countIf(B.statusCode=2) as error_count,
    count(*) as total_count,
    toStartOfMinute(B.timestamp) as timestamp
FROM signoz_traces.signoz_index_v2 AS A, signoz_traces.signoz_index_v2 AS B
WHERE (A.serviceName != B.serviceName) AND (A.spanID = B.parentSpanID) AND (A.traceID = B.traceID)
GROUP BY timestamp, src, dest;

CREATE TABLE IF NOT EXISTS signoz_traces.distributed_signoz_index_v2 ON CLUSTER hz_shards_2replicas AS signoz_traces.signoz_index_v2
    ENGINE = Distributed("hz_shards_2replicas", "signoz_traces", signoz_index_v2, cityHash64(traceID));

CREATE TABLE IF NOT EXISTS signoz_traces.distributed_signoz_spans ON CLUSTER hz_shards_2replicas AS signoz_traces.signoz_spans
    ENGINE = Distributed("hz_shards_2replicas", "signoz_traces", signoz_spans, cityHash64(traceID));

CREATE TABLE IF NOT EXISTS signoz_traces.distributed_durationSort ON CLUSTER hz_shards_2replicas AS signoz_traces.durationSort
    ENGINE = Distributed("hz_shards_2replicas", "signoz_traces", durationSort, cityHash64(traceID));

CREATE TABLE IF NOT EXISTS signoz_traces.distributed_signoz_error_index_v2 ON CLUSTER hz_shards_2replicas AS signoz_traces.signoz_error_index_v2
    ENGINE = Distributed("hz_shards_2replicas", "signoz_traces", signoz_error_index_v2, cityHash64(groupID));

CREATE TABLE IF NOT EXISTS signoz_traces.distributed_usage_explorer ON CLUSTER hz_shards_2replicas AS signoz_traces.usage_explorer
    ENGINE = Distributed("hz_shards_2replicas", "signoz_traces", usage_explorer, cityHash64(rand()));

CREATE TABLE IF NOT EXISTS signoz_traces.distributed_top_level_operations ON CLUSTER hz_shards_2replicas AS signoz_traces.top_level_operations
    ENGINE = Distributed("hz_shards_2replicas", "signoz_traces", top_level_operations, cityHash64(rand()));

CREATE TABLE IF NOT EXISTS signoz_traces.distributed_dependency_graph_minutes ON CLUSTER hz_shards_2replicas AS signoz_traces.dependency_graph_minutes
    ENGINE = Distributed("hz_shards_2replicas", "signoz_traces", dependency_graph_minutes, cityHash64(rand()));

DROP TABLE IF EXISTS signoz_traces.durationSortMV ON CLUSTER hz_shards_2replicas;

ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS stringTagMap Map(String, String),
    ADD COLUMN IF NOT EXISTS numberTagMap Map(String, Float64),
    ADD COLUMN IF NOT EXISTS boolTagMap Map(String, bool);

ALTER TABLE signoz_traces.distributed_signoz_index_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS stringTagMap Map(String, String),
    ADD COLUMN IF NOT EXISTS numberTagMap Map(String, Float64),
    ADD COLUMN IF NOT EXISTS boolTagMap Map(String, bool);

ALTER TABLE signoz_traces.durationSort ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS stringTagMap Map(String, String),
    ADD COLUMN IF NOT EXISTS numberTagMap Map(String, Float64),
    ADD COLUMN IF NOT EXISTS boolTagMap Map(String, bool);

ALTER TABLE signoz_traces.distributed_durationSort ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS stringTagMap Map(String, String),
    ADD COLUMN IF NOT EXISTS numberTagMap Map(String, Float64),
    ADD COLUMN IF NOT EXISTS boolTagMap Map(String, bool);

ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas MODIFY SETTING ttl_only_drop_parts = 1;
ALTER TABLE signoz_traces.signoz_error_index_v2 ON CLUSTER hz_shards_2replicas MODIFY SETTING ttl_only_drop_parts = 1;
ALTER TABLE signoz_traces.signoz_spans ON CLUSTER hz_shards_2replicas MODIFY SETTING ttl_only_drop_parts = 1;
ALTER TABLE signoz_traces.durationSort ON CLUSTER hz_shards_2replicas MODIFY SETTING ttl_only_drop_parts = 1;
ALTER TABLE signoz_traces.dependency_graph_minutes ON CLUSTER hz_shards_2replicas MODIFY SETTING ttl_only_drop_parts = 1;
ALTER TABLE signoz_traces.usage_explorer ON CLUSTER hz_shards_2replicas MODIFY SETTING ttl_only_drop_parts = 1;

ALTER TABLE signoz_traces.signoz_error_index_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS resourceTagsMap Map(String, String),
    ADD INDEX IF NOT EXISTS idx_resourceTagsMapKeys mapKeys(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    ADD INDEX IF NOT EXISTS idx_resourceTagsMapValues mapValues(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64;

ALTER TABLE signoz_traces.distributed_signoz_error_index_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS resourceTagsMap Map(String, String);


ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS resourceTagsMap Map(String, String),
    ADD INDEX IF NOT EXISTS idx_resourceTagsMapKeys mapKeys(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    ADD INDEX IF NOT EXISTS idx_resourceTagsMapValues mapValues(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64;

ALTER TABLE signoz_traces.distributed_signoz_index_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS resourceTagsMap Map(String, String);

CREATE TABLE IF NOT EXISTS signoz_traces.dependency_graph_minutes_v2 ON CLUSTER hz_shards_2replicas (
    src LowCardinality(String) CODEC(ZSTD(1)),
    dest LowCardinality(String) CODEC(ZSTD(1)),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64) CODEC(Default),
    error_count SimpleAggregateFunction(sum, UInt64) CODEC(T64, ZSTD(1)),
    total_count SimpleAggregateFunction(sum, UInt64) CODEC(T64, ZSTD(1)),
    timestamp DateTime CODEC(DoubleDelta, LZ4),
    deployment_environment LowCardinality(String) CODEC(ZSTD(1)),
    k8s_cluster_name LowCardinality(String) CODEC(ZSTD(1)),
    k8s_namespace_name LowCardinality(String) CODEC(ZSTD(1))
    ) ENGINE ReplicatedAggregatingMergeTree
    PARTITION BY toDate(timestamp)
    ORDER BY (timestamp, src, dest, deployment_environment, k8s_cluster_name, k8s_namespace_name)
    TTL timestamp + toIntervalDay(3) DELETE;


CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.dependency_graph_minutes_service_calls_mv_v2 ON CLUSTER hz_shards_2replicas
TO signoz_traces.dependency_graph_minutes_v2 AS
SELECT
    A.serviceName as src,
    B.serviceName as dest,
    quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(B.durationNano)) as duration_quantiles_state,
        countIf(B.statusCode=2) as error_count,
    count(*) as total_count,
    toStartOfMinute(B.timestamp) as timestamp,
    B.resourceTagsMap['deployment.environment'] as deployment_environment,
    B.resourceTagsMap['k8s.cluster.name'] as k8s_cluster_name,
    B.resourceTagsMap['k8s.namespace.name'] as k8s_namespace_name
FROM signoz_traces.signoz_index_v2 AS A, signoz_traces.signoz_index_v2 AS B
WHERE (A.serviceName != B.serviceName) AND (A.spanID = B.parentSpanID) AND (A.traceID = B.traceID)
GROUP BY timestamp, src, dest, deployment_environment, k8s_cluster_name, k8s_namespace_name;

CREATE TABLE IF NOT EXISTS signoz_traces.distributed_dependency_graph_minutes_v2 ON CLUSTER hz_shards_2replicas AS signoz_traces.dependency_graph_minutes_v2
    ENGINE = Distributed("hz_shards_2replicas", "signoz_traces", dependency_graph_minutes_v2, cityHash64(rand()));

CREATE TABLE IF NOT EXISTS signoz_traces.span_attributes ON CLUSTER hz_shards_2replicas (
    time DateTime,
    timestamp DateTime CODEC(DoubleDelta, ZSTD(1)),
    tagKey LowCardinality(String) CODEC(ZSTD(1)),
    tagType Enum8('tag', 'resource') CODEC(ZSTD(1)),
    dataType Enum8('string', 'bool', 'float64') CODEC(ZSTD(1)),
    stringTagValue String CODEC(ZSTD(1)),
    float64TagValue Nullable(Float64) CODEC(ZSTD(1)),
    isColumn bool CODEC(ZSTD(1)),
    ) ENGINE ReplicatedReplacingMergeTree
    ORDER BY (tagKey, tagType, dataType, stringTagValue, float64TagValue, isColumn)
    TTL time + toIntervalDay(3) DELETE SETTINGS ttl_only_drop_parts = 1, allow_nullable_key = 1;

CREATE TABLE IF NOT EXISTS signoz_traces.distributed_span_attributes ON CLUSTER hz_shards_2replicas AS signoz_traces.span_attributes
    ENGINE = Distributed("hz_shards_2replicas", "signoz_traces", span_attributes, cityHash64(rand()));



CREATE TABLE IF NOT EXISTS signoz_traces.span_attributes_keys ON CLUSTER hz_shards_2replicas (
    time DateTime,
    tagKey LowCardinality(String) CODEC(ZSTD(1)),
    tagType Enum8('tag', 'resource') CODEC(ZSTD(1)),
    dataType Enum8('string', 'bool', 'float64') CODEC(ZSTD(1)),
    isColumn bool CODEC(ZSTD(1)),
    ) ENGINE ReplicatedReplacingMergeTree
    ORDER BY (tagKey, tagType, dataType, isColumn);

CREATE TABLE IF NOT EXISTS signoz_traces.distributed_span_attributes_keys ON CLUSTER hz_shards_2replicas AS signoz_traces.span_attributes_keys
    ENGINE = Distributed("hz_shards_2replicas", "signoz_traces", span_attributes_keys, cityHash64(rand()));

ALTER TABLE signoz_traces.top_level_operations ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS time DateTime DEFAULT now() CODEC(ZSTD(1));

ALTER TABLE signoz_traces.top_level_operations ON CLUSTER hz_shards_2replicas
    MODIFY TTL time + INTERVAL 1 MONTH;

ALTER TABLE signoz_traces.distributed_top_level_operations ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS time DateTime DEFAULT now() CODEC(ZSTD(1));

CREATE TABLE IF NOT EXISTS signoz_traces.usage ON CLUSTER hz_shards_2replicas (
      tenant String,
      collector_id String,
      exporter_id String,
      timestamp DateTime,
      data String
) ENGINE ReplicatedMergeTree()
    ORDER BY (tenant, collector_id, exporter_id, timestamp)
    TTL timestamp + toIntervalDay(3);


CREATE TABLE IF NOT EXISTS signoz_traces.distributed_usage  ON CLUSTER hz_shards_2replicas
AS signoz_traces.usage
    ENGINE = Distributed(hz_shards_2replicas, signoz_traces, usage, cityHash64(rand()));


DROP TABLE IF EXISTS signoz_traces.durationSortMV ON CLUSTER hz_shards_2replicas;

ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS `isRemote` LowCardinality(String) CODEC(ZSTD(1));

ALTER TABLE signoz_traces.distributed_signoz_index_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS `isRemote` LowCardinality(String) CODEC(ZSTD(1));

ALTER TABLE signoz_traces.durationSort ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS `isRemote` LowCardinality(String) CODEC(ZSTD(1));

ALTER TABLE signoz_traces.distributed_durationSort ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS `isRemote` LowCardinality(String) CODEC(ZSTD(1));

DROP TABLE IF EXISTS signoz_traces.durationSortMV ON CLUSTER hz_shards_2replicas;
DROP VIEW IF EXISTS signoz_traces.dependency_graph_minutes_db_calls_mv ON CLUSTER hz_shards_2replicas;
DROP VIEW IF EXISTS signoz_traces.dependency_graph_minutes_messaging_calls_mv ON CLUSTER hz_shards_2replicas;
DROP VIEW IF EXISTS signoz_traces.dependency_graph_minutes_db_calls_mv_v2 ON CLUSTER hz_shards_2replicas;
DROP VIEW IF EXISTS signoz_traces.dependency_graph_minutes_messaging_calls_mv_v2 ON CLUSTER hz_shards_2replicas;

CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.dependency_graph_minutes_db_calls_mv ON CLUSTER hz_shards_2replicas
TO signoz_traces.dependency_graph_minutes AS
SELECT
    serviceName as src,
    dbSystem as dest,
    quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano)) as duration_quantiles_state,
        countIf(statusCode=2) as error_count,
    count(*) as total_count,
    toStartOfMinute(timestamp) as timestamp
FROM signoz_traces.signoz_index_v2
WHERE dest != '' and kind != 2
GROUP BY timestamp, src, dest;

CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.dependency_graph_minutes_messaging_calls_mv ON CLUSTER hz_shards_2replicas
TO signoz_traces.dependency_graph_minutes AS
SELECT
    serviceName as src,
    msgSystem as dest,
    quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano)) as duration_quantiles_state,
        countIf(statusCode=2) as error_count,
    count(*) as total_count,
    toStartOfMinute(timestamp) as timestamp
FROM signoz_traces.signoz_index_v2
WHERE dest != '' and kind != 2
GROUP BY timestamp, src, dest;

CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.dependency_graph_minutes_db_calls_mv_v2 ON CLUSTER hz_shards_2replicas
TO signoz_traces.dependency_graph_minutes_v2 AS
SELECT
    serviceName as src,
    dbSystem as dest,
    quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano)) as duration_quantiles_state,
        countIf(statusCode=2) as error_count,
    count(*) as total_count,
    toStartOfMinute(timestamp) as timestamp,
    resourceTagsMap['deployment.environment'] as deployment_environment,
    resourceTagsMap['k8s.cluster.name'] as k8s_cluster_name,
    resourceTagsMap['k8s.namespace.name'] as k8s_namespace_name
FROM signoz_traces.signoz_index_v2
WHERE dest != '' and kind != 2
GROUP BY timestamp, src, dest, deployment_environment, k8s_cluster_name, k8s_namespace_name;

CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.dependency_graph_minutes_messaging_calls_mv_v2 ON CLUSTER hz_shards_2replicas
TO signoz_traces.dependency_graph_minutes_v2 AS
SELECT
    serviceName as src,
    msgSystem as dest,
    quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano)) as duration_quantiles_state,
        countIf(statusCode=2) as error_count,
    count(*) as total_count,
    toStartOfMinute(timestamp) as timestamp,
    resourceTagsMap['deployment.environment'] as deployment_environment,
    resourceTagsMap['k8s.cluster.name'] as k8s_cluster_name,
    resourceTagsMap['k8s.namespace.name'] as k8s_namespace_name
FROM signoz_traces.signoz_index_v2
WHERE dest != '' and kind != 2
GROUP BY timestamp, src, dest, deployment_environment, k8s_cluster_name, k8s_namespace_name;

ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas
DROP INDEX IF EXISTS idx_httpCode,
DROP INDEX IF EXISTS idx_tagMapKeys,
DROP INDEX IF EXISTS idx_tagMapValues,
DROP COLUMN IF EXISTS tagMap,
DROP COLUMN IF EXISTS gRPCCode,
DROP COLUMN IF EXISTS gRPCMethod,
DROP COLUMN IF EXISTS httpCode,
DROP COLUMN IF EXISTS component;

ALTER TABLE signoz_traces.distributed_signoz_index_v2 ON CLUSTER hz_shards_2replicas
DROP COLUMN IF EXISTS tagMap,
DROP COLUMN IF EXISTS gRPCCode,
DROP COLUMN IF EXISTS gRPCMethod,
DROP COLUMN IF EXISTS httpCode,
DROP COLUMN IF EXISTS component;

ALTER TABLE signoz_traces.durationSort ON CLUSTER hz_shards_2replicas
DROP INDEX IF EXISTS idx_httpCode,
DROP INDEX IF EXISTS idx_tagMapKeys,
DROP INDEX IF EXISTS idx_tagMapValues,
DROP COLUMN IF EXISTS tagMap,
DROP COLUMN IF EXISTS gRPCCode,
DROP COLUMN IF EXISTS gRPCMethod,
DROP COLUMN IF EXISTS httpCode,
DROP COLUMN IF EXISTS component;

ALTER TABLE signoz_traces.distributed_durationSort ON CLUSTER hz_shards_2replicas
DROP COLUMN IF EXISTS tagMap,
DROP COLUMN IF EXISTS gRPCCode,
DROP COLUMN IF EXISTS gRPCMethod,
DROP COLUMN IF EXISTS httpCode,
DROP COLUMN IF EXISTS component;

CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.durationSortMV ON CLUSTER hz_shards_2replicas
TO signoz_traces.durationSort
AS SELECT
                                       timestamp,
                                       traceID,
                                       spanID,
                                       parentSpanID,
                                       serviceName,
                                       name,
                                       kind,
                                       durationNano,
                                       statusCode,
                                       httpMethod,
                                       httpUrl,
                                       httpRoute,
                                       httpHost,
                                       hasError,
                                       rpcSystem,
                                       rpcService,
                                       rpcMethod,
                                       responseStatusCode,
                                       stringTagMap,
                                       numberTagMap,
                                       boolTagMap,
                                       isRemote
   FROM signoz_traces.signoz_index_v2
   ORDER BY durationNano, timestamp;


DROP TABLE IF EXISTS signoz_traces.durationSortMV ON CLUSTER hz_shards_2replicas;

ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS statusMessage String CODEC(ZSTD(1));
ALTER TABLE signoz_traces.durationSort ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS statusMessage String CODEC(ZSTD(1));
ALTER TABLE signoz_traces.distributed_signoz_index_v2 ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS statusMessage String CODEC(ZSTD(1));
ALTER TABLE signoz_traces.distributed_durationSort ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS statusMessage String CODEC(ZSTD(1));

ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS statusCodeString String CODEC(ZSTD(1));
ALTER TABLE signoz_traces.durationSort ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS statusCodeString String CODEC(ZSTD(1));
ALTER TABLE signoz_traces.distributed_signoz_index_v2 ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS statusCodeString String CODEC(ZSTD(1));
ALTER TABLE signoz_traces.distributed_durationSort ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS statusCodeString String CODEC(ZSTD(1));

ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas ADD INDEX IF NOT EXISTS idx_statusCodeString statusCodeString TYPE set(3) GRANULARITY 4;

ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS spanKind String CODEC(ZSTD(1));
ALTER TABLE signoz_traces.durationSort ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS spanKind String CODEC(ZSTD(1));
ALTER TABLE signoz_traces.distributed_signoz_index_v2 ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS spanKind String CODEC(ZSTD(1));
ALTER TABLE signoz_traces.distributed_durationSort ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS spanKind String CODEC(ZSTD(1));


ALTER TABLE signoz_traces.signoz_index_v2 ON CLUSTER hz_shards_2replicas ADD INDEX IF NOT EXISTS idx_spanKind spanKind TYPE set(5) GRANULARITY 4;


CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_traces.durationSortMV ON CLUSTER hz_shards_2replicas
TO signoz_traces.durationSort
AS SELECT
              timestamp,
              traceID,
              spanID,
              parentSpanID,
              serviceName,
              name,
              kind,
              durationNano,
              statusCode,
              httpMethod,
              httpUrl,
              httpRoute,
              httpHost,
              hasError,
              rpcSystem,
              rpcService,
              rpcMethod,
              responseStatusCode,
              stringTagMap,
              numberTagMap,
              boolTagMap,
              isRemote,
              statusMessage,
              statusCodeString,
              spanKind
   FROM signoz_traces.signoz_index_v2
   ORDER BY durationNano, timestamp;