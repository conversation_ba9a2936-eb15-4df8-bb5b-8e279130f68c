DROP DATABASE IF EXISTS signoz_metrics;

CREATE DATABASE IF NOT EXISTS signoz_metrics;

DROP TABLE IF EXISTS signoz_metrics.samples_v2 ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.distributed_samples_v2 ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.time_series_v2 ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.distributed_time_series_v2 ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.time_series_v3 ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.distributed_time_series_v3 ON CLUSTER hz_shards_2replicas;
CREATE TABLE IF NOT EXISTS signoz_metrics.samples_v2 ON CLUSTER hz_shards_2replicas (
                                                                                        metric_name LowCardinality(String),
    fingerprint UInt64 Codec(DoubleDelta, LZ4),
    timestamp_ms Int64 Codec(DoubleDelta, LZ4),
    value Float64 Codec(Gorilla, LZ4)
    )
    ENGINE = ReplicatedMergeTree
    PARTITION BY toDate(timestamp_ms / 1000)
    ORDER BY (metric_name, fingerprint, timestamp_ms)
    TTL toDateTime(timestamp_ms/1000) + INTERVAL 2592000 SECOND DELETE;

CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_samples_v2 ON CLUSTER hz_shards_2replicas AS signoz_metrics.samples_v2 ENGINE = Distributed("hz_shards_2replicas", "signoz_metrics", samples_v2, cityHash64(metric_name, fingerprint));

ALTER TABLE signoz_metrics.samples_v2 ON CLUSTER hz_shards_2replicas MODIFY SETTING ttl_only_drop_parts = 1;

SET allow_experimental_object_type = 1;

CREATE TABLE IF NOT EXISTS signoz_metrics.time_series_v2 ON CLUSTER hz_shards_2replicas(
                                                                                           metric_name LowCardinality(String),
    fingerprint UInt64 Codec(DoubleDelta, LZ4),
    timestamp_ms Int64 Codec(DoubleDelta, LZ4),
    labels String Codec(ZSTD(5))
    )
    ENGINE = ReplicatedReplacingMergeTree
    PARTITION BY toDate(timestamp_ms / 1000)
    ORDER BY (metric_name, fingerprint)
    TTL toDateTime(timestamp_ms/1000) + INTERVAL 2592000 SECOND DELETE;

CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_time_series_v2 ON CLUSTER hz_shards_2replicas AS signoz_metrics.time_series_v2 ENGINE = Distributed("hz_shards_2replicas", signoz_metrics, time_series_v2, cityHash64(metric_name, fingerprint));

ALTER TABLE signoz_metrics.time_series_v2 ON CLUSTER hz_shards_2replicas DROP COLUMN IF EXISTS labels_object;

ALTER TABLE signoz_metrics.distributed_time_series_v2 ON CLUSTER hz_shards_2replicas DROP COLUMN IF EXISTS labels_object;

ALTER TABLE signoz_metrics.time_series_v2 ON CLUSTER hz_shards_2replicas MODIFY SETTING ttl_only_drop_parts = 1;

ALTER TABLE signoz_metrics.time_series_v2 ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS temporality LowCardinality(String) DEFAULT 'Unspecified' CODEC(ZSTD(5));

ALTER TABLE signoz_metrics.distributed_time_series_v2 ON CLUSTER hz_shards_2replicas ADD COLUMN IF NOT EXISTS temporality LowCardinality(String) DEFAULT 'Unspecified' CODEC(ZSTD(5));

ALTER TABLE signoz_metrics.time_series_v2 ON CLUSTER hz_shards_2replicas ADD INDEX IF NOT EXISTS temporality_index temporality TYPE SET(3) GRANULARITY 1;

CREATE TABLE IF NOT EXISTS signoz_metrics.time_series_v3 ON CLUSTER hz_shards_2replicas (
                                                                                            env LowCardinality(String) DEFAULT 'default',
    temporality LowCardinality(String) DEFAULT 'Unspecified',
    metric_name LowCardinality(String),
    fingerprint UInt64 CODEC(Delta, ZSTD),
    timestamp_ms Int64 CODEC(Delta, ZSTD),
    labels String CODEC(ZSTD(5))
    )
    ENGINE = ReplicatedReplacingMergeTree
    PARTITION BY toDate(timestamp_ms / 1000)
    ORDER BY (env, temporality, metric_name, fingerprint);

CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_time_series_v3 ON CLUSTER hz_shards_2replicas AS signoz_metrics.time_series_v3 ENGINE = Distributed("hz_shards_2replicas", signoz_metrics, time_series_v3, cityHash64(env, temporality, metric_name, fingerprint));
ALTER TABLE signoz_metrics.time_series_v3 ON CLUSTER hz_shards_2replicas
DROP COLUMN IF EXISTS description,
    DROP COLUMN IF EXISTS unit,
    DROP COLUMN IF EXISTS type,
    DROP COLUMN IF EXISTS is_monotonic;

ALTER TABLE signoz_metrics.distributed_time_series_v3 ON CLUSTER hz_shards_2replicas
DROP COLUMN IF EXISTS description,
    DROP COLUMN IF EXISTS unit,
    DROP COLUMN IF EXISTS type,
    DROP COLUMN IF EXISTS is_monotonic;

ALTER TABLE signoz_metrics.time_series_v2 ON CLUSTER hz_shards_2replicas
DROP COLUMN IF EXISTS description,
    DROP COLUMN IF EXISTS unit,
    DROP COLUMN IF EXISTS type,
    DROP COLUMN IF EXISTS is_monotonic;

ALTER TABLE signoz_metrics.distributed_time_series_v2 ON CLUSTER hz_shards_2replicas
DROP COLUMN IF EXISTS description,
    DROP COLUMN IF EXISTS unit,
    DROP COLUMN IF EXISTS type,
    DROP COLUMN IF EXISTS is_monotonic;

ALTER TABLE signoz_metrics.time_series_v3 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS description LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS unit LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS type LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS is_monotonic Bool DEFAULT false CODEC(ZSTD(1));

ALTER TABLE signoz_metrics.distributed_time_series_v3 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS description LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS unit LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS type LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS is_monotonic Bool DEFAULT false CODEC(ZSTD(1));

ALTER TABLE signoz_metrics.time_series_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS description LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS unit LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS type LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS is_monotonic Bool DEFAULT false CODEC(ZSTD(1));

ALTER TABLE signoz_metrics.distributed_time_series_v2 ON CLUSTER hz_shards_2replicas
    ADD COLUMN IF NOT EXISTS description LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS unit LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS type LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    ADD COLUMN IF NOT EXISTS is_monotonic Bool DEFAULT false CODEC(ZSTD(1));

DROP TABLE IF EXISTS signoz_metrics.time_series_v4 ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.distributed_time_series_v4 ON CLUSTER hz_shards_2replicas;
CREATE TABLE IF NOT EXISTS signoz_metrics.time_series_v4 ON CLUSTER hz_shards_2replicas (
                                                                                            env LowCardinality(String) DEFAULT 'default',
    temporality LowCardinality(String) DEFAULT 'Unspecified',
    metric_name LowCardinality(String),
    description LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    unit LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    type LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    is_monotonic Bool DEFAULT false CODEC(ZSTD(1)),
    fingerprint UInt64 CODEC(Delta, ZSTD),
    unix_milli Int64 CODEC(Delta, ZSTD),
    labels String CODEC(ZSTD(5)),
    INDEX idx_labels labels TYPE ngrambf_v1(4, 1024, 3, 0) GRANULARITY 1
    )
    ENGINE = ReplicatedReplacingMergeTree
    PARTITION BY toDate(unix_milli / 1000)
    ORDER BY (env, temporality, metric_name, fingerprint, unix_milli)
    TTL toDateTime(unix_milli/1000) + INTERVAL 2592000 SECOND DELETE
                                                                  SETTINGS ttl_only_drop_parts = 1;

CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_time_series_v4 ON CLUSTER hz_shards_2replicas AS signoz_metrics.time_series_v4 ENGINE = Distributed("hz_shards_2replicas", signoz_metrics, time_series_v4, cityHash64(env, temporality, metric_name, fingerprint));
DROP TABLE IF EXISTS signoz_metrics.time_series_v4_6hrs ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.distributed_time_series_v4_6hrs ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.time_series_v4_1day ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.distributed_time_series_v4_1day ON CLUSTER hz_shards_2replicas;

CREATE TABLE IF NOT EXISTS signoz_metrics.time_series_v4_6hrs ON CLUSTER hz_shards_2replicas (
                                                                                                 env LowCardinality(String) DEFAULT 'default',
    temporality LowCardinality(String) DEFAULT 'Unspecified',
    metric_name LowCardinality(String),
    description LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    unit LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    type LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    is_monotonic Bool DEFAULT false CODEC(ZSTD(1)),
    fingerprint UInt64 CODEC(Delta, ZSTD),
    unix_milli Int64 CODEC(Delta, ZSTD),
    labels String CODEC(ZSTD(5)),
    INDEX idx_labels labels TYPE ngrambf_v1(4, 1024, 3, 0) GRANULARITY 1
    )
    ENGINE = ReplicatedReplacingMergeTree
    PARTITION BY toDate(unix_milli / 1000)
    ORDER BY (env, temporality, metric_name, fingerprint, unix_milli)
    TTL toDateTime(unix_milli/1000) + INTERVAL 2592000 SECOND DELETE
                                                                  SETTINGS ttl_only_drop_parts = 1;

CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_time_series_v4_6hrs ON CLUSTER hz_shards_2replicas AS signoz_metrics.time_series_v4_6hrs ENGINE = Distributed("hz_shards_2replicas", signoz_metrics, time_series_v4_6hrs, cityHash64(env, temporality, metric_name, fingerprint));

CREATE TABLE IF NOT EXISTS signoz_metrics.time_series_v4_1day ON CLUSTER hz_shards_2replicas (
                                                                                                 env LowCardinality(String) DEFAULT 'default',
    temporality LowCardinality(String) DEFAULT 'Unspecified',
    metric_name LowCardinality(String),
    description LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    unit LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    type LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    is_monotonic Bool DEFAULT false CODEC(ZSTD(1)),
    fingerprint UInt64 CODEC(Delta, ZSTD),
    unix_milli Int64 CODEC(Delta, ZSTD),
    labels String CODEC(ZSTD(5)),
    INDEX idx_labels labels TYPE ngrambf_v1(4, 1024, 3, 0) GRANULARITY 1
    )
    ENGINE = ReplicatedReplacingMergeTree
    PARTITION BY toDate(unix_milli / 1000)
    ORDER BY (env, temporality, metric_name, fingerprint, unix_milli)
    TTL toDateTime(unix_milli/1000) + INTERVAL 2592000 SECOND DELETE
                                                                  SETTINGS ttl_only_drop_parts = 1;

CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_time_series_v4_1day ON CLUSTER hz_shards_2replicas AS signoz_metrics.time_series_v4_1day ENGINE = Distributed("hz_shards_2replicas", signoz_metrics, time_series_v4_1day, cityHash64(env, temporality, metric_name, fingerprint));

-- mat views

-- unix_milli rounded nearest 6 hours

CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_metrics.time_series_v4_6hrs_mv ON CLUSTER hz_shards_2replicas
TO signoz_metrics.time_series_v4_6hrs
AS SELECT
              env,
              temporality,
              metric_name,
              description,
              unit,
              type,
              is_monotonic,
              fingerprint,
              floor(unix_milli/21600000)*21600000 AS unix_milli,
              labels
   FROM signoz_metrics.time_series_v4;

-- unix_milli rounded nearest 1 day

CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_metrics.time_series_v4_1day_mv ON CLUSTER hz_shards_2replicas
TO signoz_metrics.time_series_v4_1day
AS SELECT
              env,
              temporality,
              metric_name,
              description,
              unit,
              type,
              is_monotonic,
              fingerprint,
              floor(unix_milli/86400000)*86400000 AS unix_milli,
              labels
   FROM signoz_metrics.time_series_v4;
DROP TABLE IF EXISTS signoz_metrics.samples_v4 ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.distributed_samples_v4 ON CLUSTER hz_shards_2replicas;
CREATE TABLE IF NOT EXISTS signoz_metrics.samples_v4 ON CLUSTER hz_shards_2replicas (
                                                                                        env LowCardinality(String) DEFAULT 'default',
    temporality LowCardinality(String) DEFAULT 'Unspecified',
    metric_name LowCardinality(String),
    fingerprint UInt64 CODEC(Delta, ZSTD),
    unix_milli Int64 CODEC(DoubleDelta, ZSTD),
    value Float64 Codec(Gorilla, ZSTD)
    )
    ENGINE = ReplicatedMergeTree
    PARTITION BY toDate(unix_milli / 1000)
    ORDER BY (env, temporality, metric_name, fingerprint, unix_milli)
    TTL toDateTime(unix_milli/1000) + INTERVAL 2592000 SECOND DELETE
                                                                  SETTINGS ttl_only_drop_parts = 1;

CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_samples_v4 ON CLUSTER hz_shards_2replicas AS signoz_metrics.samples_v4 ENGINE = Distributed("hz_shards_2replicas", "signoz_metrics", samples_v4, cityHash64(env, temporality, metric_name, fingerprint));
DROP TABLE IF EXISTS signoz_metrics.usage on CLUSTER hz_shards_2replicas;
DROP TABLE IF EXISTS signoz_metrics.distributed_usage on CLUSTER hz_shards_2replicas;
CREATE TABLE IF NOT EXISTS signoz_metrics.usage ON CLUSTER hz_shards_2replicas (
                                                                                   tenant String,
                                                                                   collector_id String,
                                                                                   exporter_id String,
                                                                                   timestamp DateTime,
                                                                                   data String
) ENGINE ReplicatedMergeTree()
    ORDER BY (tenant, collector_id, exporter_id, timestamp)
    TTL timestamp + INTERVAL 3 DAY;


CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_usage  ON CLUSTER hz_shards_2replicas
AS signoz_metrics.usage
    ENGINE = Distributed(hz_shards_2replicas, signoz_metrics, usage, cityHash64(rand()));
DROP TABLE IF EXISTS signoz_metrics.exp_hist ON CLUSTER hz_shards_2replicas;

DROP TABLE IF EXISTS signoz_metrics.distributed_exp_hist ON CLUSTER hz_shards_2replicas;

CREATE TABLE IF NOT EXISTS signoz_metrics.exp_hist ON CLUSTER hz_shards_2replicas (
                                                                                      env LowCardinality(String) DEFAULT 'default',
    temporality LowCardinality(String) DEFAULT 'Unspecified',
    metric_name LowCardinality(String),
    fingerprint UInt64 CODEC(Delta, ZSTD),
    unix_milli Int64 CODEC(DoubleDelta, ZSTD),
    count UInt64 CODEC(ZSTD(1)),
    sum Float64 Codec(Gorilla, ZSTD),
    min Float64 Codec(Gorilla, ZSTD),
    max Float64 Codec(Gorilla, ZSTD),
    sketch AggregateFunction(quantiles(0.01, 0.5, 0.75, 0.9, 0.95, 0.99), UInt64) CODEC(ZSTD(1))
    )
    ENGINE = ReplicatedMergeTree
    PARTITION BY toDate(unix_milli / 1000)
    ORDER BY (env, temporality, metric_name, fingerprint, unix_milli)
    TTL toDateTime(unix_milli/1000) + INTERVAL 2592000 SECOND DELETE
                                                                  SETTINGS ttl_only_drop_parts = 1;

CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_exp_hist ON CLUSTER hz_shards_2replicas AS signoz_metrics.exp_hist ENGINE = Distributed("hz_shards_2replicas", "signoz_metrics", exp_hist, cityHash64(env, temporality, metric_name, fingerprint));
DROP TABLE IF EXISTS signoz_metrics.samples_v4_agg_5m ON CLUSTER hz_shards_2replicas;
DROP TABLE IF EXISTS signoz_metrics.samples_v4_agg_5m_mv ON CLUSTER hz_shards_2replicas;
DROP TABLE IF EXISTS signoz_metrics.samples_v4_agg_30m ON CLUSTER hz_shards_2replicas;
DROP TABLE IF EXISTS signoz_metrics.samples_v4_agg_30m_mv ON CLUSTER hz_shards_2replicas;
DROP TABLE IF EXISTS signoz_metrics.distributed_samples_v4_agg_5m ON CLUSTER hz_shards_2replicas;
DROP TABLE IF EXISTS signoz_metrics.distributed_samples_v4_agg_30m ON CLUSTER hz_shards_2replicas;
DROP TABLE IF EXISTS signoz_metrics.time_series_v4_1week ON CLUSTER hz_shards_2replicas;
DROP TABLE IF EXISTS signoz_metrics.distributed_time_series_v4_1week ON CLUSTER hz_shards_2replicas;
DROP TABLE IF EXISTS signoz_metrics.time_series_v4_1week_mv ON CLUSTER hz_shards_2replicas;

-- 5m aggregation table
CREATE TABLE IF NOT EXISTS signoz_metrics.samples_v4_agg_5m ON CLUSTER hz_shards_2replicas
(
    `env` LowCardinality(String) DEFAULT 'default',
    `temporality` LowCardinality(String) DEFAULT 'Unspecified',
    `metric_name` LowCardinality(String),
    `fingerprint` UInt64 CODEC(ZSTD(1)),
    `unix_milli` Int64 CODEC(DoubleDelta, ZSTD(1)),
    `last` SimpleAggregateFunction(anyLast, Float64) CODEC(ZSTD(1)),
    `min` SimpleAggregateFunction(min, Float64) CODEC(ZSTD(1)),
    `max` SimpleAggregateFunction(max, Float64) CODEC(ZSTD(1)),
    `sum` SimpleAggregateFunction(sum, Float64) CODEC(ZSTD(1)),
    `count` SimpleAggregateFunction(sum, UInt64) CODEC(ZSTD(1))
    )
    ENGINE = ReplicatedAggregatingMergeTree
    PARTITION BY toDate(unix_milli / 1000)
    ORDER BY (env, temporality, metric_name, fingerprint, unix_milli)
    TTL toDateTime(unix_milli/1000) + INTERVAL 2592000 SECOND DELETE
                                                                  SETTINGS ttl_only_drop_parts = 1;

-- 30m aggregation table
CREATE TABLE IF NOT EXISTS signoz_metrics.samples_v4_agg_30m ON CLUSTER hz_shards_2replicas
(
    `env` LowCardinality(String) DEFAULT 'default',
    `temporality` LowCardinality(String) DEFAULT 'Unspecified',
    `metric_name` LowCardinality(String),
    `fingerprint` UInt64 CODEC(ZSTD(1)),
    `unix_milli` Int64 CODEC(DoubleDelta, ZSTD(1)),
    `last` SimpleAggregateFunction(anyLast, Float64) CODEC(ZSTD(1)),
    `min` SimpleAggregateFunction(min, Float64) CODEC(ZSTD(1)),
    `max` SimpleAggregateFunction(max, Float64) CODEC(ZSTD(1)),
    `sum` SimpleAggregateFunction(sum, Float64) CODEC(ZSTD(1)),
    `count` SimpleAggregateFunction(sum, UInt64) CODEC(ZSTD(1))
    )
    ENGINE = ReplicatedAggregatingMergeTree
    PARTITION BY toDate(unix_milli / 1000)
    ORDER BY (env, temporality, metric_name, fingerprint, unix_milli)
    TTL toDateTime(unix_milli/1000) + INTERVAL 2592000 SECOND DELETE
                                                                  SETTINGS ttl_only_drop_parts = 1;

-- 5m aggregation materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_metrics.samples_v4_agg_5m_mv ON CLUSTER hz_shards_2replicas
TO signoz_metrics.samples_v4_agg_5m AS
SELECT
    env,
    temporality,
    metric_name,
    fingerprint,
    intDiv(unix_milli, 300000) * 300000 as unix_milli,
    anyLast(value) as last,
    min(value) as min,
    max(value) as max,
    sum(value) as sum,
    count(*) as count
FROM signoz_metrics.samples_v4
GROUP BY
    env,
    temporality,
    metric_name,
    fingerprint,
    unix_milli;

-- 30m aggregation materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_metrics.samples_v4_agg_30m_mv ON CLUSTER hz_shards_2replicas
TO signoz_metrics.samples_v4_agg_30m AS
SELECT
    env,
    temporality,
    metric_name,
    fingerprint,
    intDiv(unix_milli, 1800000) * 1800000 as unix_milli,
    anyLast(last) as last,
    min(min) as min,
    max(max) as max,
    sum(sum) as sum,
    sum(count) as count
FROM signoz_metrics.samples_v4_agg_5m
GROUP BY
    env,
    temporality,
    metric_name,
    fingerprint,
    unix_milli;

-- 5m aggregation distributed table
CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_samples_v4_agg_5m ON CLUSTER hz_shards_2replicas
AS signoz_metrics.samples_v4_agg_5m
    ENGINE = Distributed('hz_shards_2replicas', 'signoz_metrics', 'samples_v4_agg_5m', cityHash64(env, temporality, metric_name, fingerprint));

-- 30m aggregation distributed table
CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_samples_v4_agg_30m ON CLUSTER hz_shards_2replicas
AS signoz_metrics.samples_v4_agg_30m
    ENGINE = Distributed('hz_shards_2replicas', 'signoz_metrics', 'samples_v4_agg_30m', cityHash64(env, temporality, metric_name, fingerprint));

-- time series v4 1 week table
CREATE TABLE IF NOT EXISTS signoz_metrics.time_series_v4_1week ON CLUSTER hz_shards_2replicas (
                                                                                                  env LowCardinality(String) DEFAULT 'default',
    temporality LowCardinality(String) DEFAULT 'Unspecified',
    metric_name LowCardinality(String),
    description LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    unit LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    type LowCardinality(String) DEFAULT '' CODEC(ZSTD(1)),
    is_monotonic Bool DEFAULT false CODEC(ZSTD(1)),
    fingerprint UInt64 CODEC(Delta, ZSTD),
    unix_milli Int64 CODEC(Delta, ZSTD),
    labels String CODEC(ZSTD(5)),
    INDEX idx_labels labels TYPE ngrambf_v1(4, 1024, 3, 0) GRANULARITY 1
    )
    ENGINE = ReplicatedReplacingMergeTree
    PARTITION BY toDate(unix_milli / 1000)
    ORDER BY (env, temporality, metric_name, fingerprint, unix_milli)
    TTL toDateTime(unix_milli/1000) + INTERVAL 2592000 SECOND DELETE
                                                                  SETTINGS ttl_only_drop_parts = 1;

-- time series v4 1 week materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS signoz_metrics.time_series_v4_1week_mv ON CLUSTER hz_shards_2replicas
TO signoz_metrics.time_series_v4_1week
AS SELECT
              env,
              temporality,
              metric_name,
              description,
              unit,
              type,
              is_monotonic,
              fingerprint,
              floor(unix_milli/604800000)*604800000 AS unix_milli,
              labels
   FROM signoz_metrics.time_series_v4_1day;

-- time series v4 1 week distributed table
CREATE TABLE IF NOT EXISTS signoz_metrics.distributed_time_series_v4_1week ON CLUSTER hz_shards_2replicas
AS signoz_metrics.time_series_v4_1week
    ENGINE = Distributed("hz_shards_2replicas", signoz_metrics, time_series_v4_1week, cityHash64(env, temporality, metric_name, fingerprint));