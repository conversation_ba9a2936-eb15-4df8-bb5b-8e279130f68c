DROP TABLE IF EXISTS signoz_traces.signoz_index_v2;

CREATE TABLE signoz_traces.signoz_index_v2
(
    `time`               DateTime,
    `timestamp`          DateTime,
    `traceID`            String,
    `spanID`             String,
    `parentSpanID`       String,
    `serviceName`        LowCardinality(String),
    `name`               LowCardinality(String),
    `kind`               Int32,
    `durationNano`       Int64,
    `statusCode`         Int32,
    `externalHttpMethod` LowCardinality(String),
    `externalHttpUrl`    LowCardinality(String),
    `dbSystem`           LowCardinality(String),
    `dbName`             LowCardinality(String),
    `dbOperation`        LowCardinality(String),
    `peerService`        LowCardinality(String),
    `events`             Array(String),
    `httpMethod`         LowCardinality(String),
    `httpUrl`            LowCardinality(String),
    `httpRoute`          LowCardinality(String),
    `httpHost`           LowCardinality(String),
    `msgSystem`          LowCardinality(String),
    `msgOperation`       LowCardinality(String),
    `hasError`           Bool,
    `rpcSystem`          LowCardinality(String),
    `rpcService`         LowCardinality(String),
    `rpcMethod`          LowCardinality(String),
    `responseStatusCode` LowCardinality(String),
    `stringTagMap`       Map(String, String),
    `numberTagMap`       Map(String, Float64),
    `boolTagMap`         Map(String, Bool),
    `resourceTagsMap`    Map(String, String),
    `isRemote`           LowCardinality(String),
    `statusMessage`      String,
    `statusCodeString`   String,
    `spanKind`           String,
    INDEX idx_service serviceName TYPE bloom_filter GRANULARITY 4,
    INDEX idx_name name TYPE bloom_filter GRANULARITY 4,
    INDEX idx_kind kind TYPE minmax GRANULARITY 4,
    INDEX idx_duration durationNano TYPE minmax GRANULARITY 1,
    INDEX idx_hasError hasError TYPE set(2) GRANULARITY 1,
    INDEX idx_httpRoute httpRoute TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpUrl httpUrl TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpHost httpHost TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpMethod httpMethod TYPE bloom_filter GRANULARITY 4,
    INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1,
    INDEX idx_rpcMethod rpcMethod TYPE bloom_filter GRANULARITY 4,
    INDEX idx_responseStatusCode responseStatusCode TYPE set(0) GRANULARITY 1,
    INDEX idx_resourceTagsMapKeys mapKeys(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_resourceTagsMapValues mapValues(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_statusCodeString statusCodeString TYPE set(3) GRANULARITY 4,
    INDEX idx_spanKind spanKind TYPE set(5) GRANULARITY 4,
    PROJECTION timestampSort
        (
        SELECT *
        ORDER BY timestamp
        )
)
    ENGINE = ReplicatedMergeTree(
            '/clickhouse/tables/{shard}/signoz_traces/signoz_index_v2.dc48a3e5264045adb36a22a64dc3f0ac',
            '{replica}')
        PARTITION BY toDate(timestamp)
        PRIMARY KEY (serviceName, hasError, toStartOfHour(timestamp), name)
        ORDER BY (serviceName, hasError, toStartOfHour(timestamp), name, timestamp)
        TTL timestamp + toIntervalDay(3)
        SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1