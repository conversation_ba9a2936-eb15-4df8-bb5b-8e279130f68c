DROP TABLE IF EXISTS signoz_traces.distributed_signoz_error_index_v2;

CREATE TABLE signoz_traces.distributed_signoz_error_index_v2
(
    `time`                DateTime,
    `timestamp`           DateTime,
    `errorID`             String,
    `groupID`             String,
    `traceID`             String,
    `spanID`              String,
    `serviceName`         LowCardinality(String),
    `exceptionType`       LowCardinality(String),
    `exceptionMessage`    String,
    `exceptionStacktrace` String,
    `exceptionEscaped`    Bool,
    `resourceTagsMap`     Map(String, String)
)
    ENGINE = Distributed('{cluster}', 'signoz_traces', 'signoz_error_index_v2', rand());