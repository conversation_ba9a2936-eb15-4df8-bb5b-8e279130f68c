DROP TABLE IF EXISTS signoz_traces.distributed_signoz_index_v2;

CREATE TABLE signoz_traces.distributed_signoz_index_v2
(
    `time`               DateTime,
    `timestamp`          DateTime,
    `traceID`            String,
    `spanID`             String,
    `parentSpanID`       String,
    `serviceName`        LowCardinality(String),
    `name`               LowCardinality(String),
    `kind`               Int32,
    `durationNano`       Int64,
    `statusCode`         Int32,
    `externalHttpMethod` LowCardinality(String),
    `externalHttpUrl`    LowCardinality(String),
    `dbSystem`           LowCardinality(String),
    `dbName`             LowCardinality(String),
    `dbOperation`        LowCardinality(String),
    `peerService`        LowCardinality(String),
    `events`             Array(String),
    `httpMethod`         LowCardinality(String),
    `httpUrl`            LowCardinality(String),
    `httpRoute`          LowCardinality(String),
    `httpHost`           LowCardinality(String),
    `msgSystem`          LowCardinality(String),
    `msgOperation`       LowCardinality(String),
    `hasError`           Bool,
    `rpcSystem`          LowCardinality(String),
    `rpcService`         LowCardinality(String),
    `rpcMethod`          LowCardinality(String),
    `responseStatusCode` LowCardinality(String),
    `stringTagMap`       Map(String, String),
    `numberTagMap`       Map(String, Float64),
    `boolTagMap`         Map(String, Bool),
    `resourceTagsMap`    Map(String, String),
    `isRemote`           LowCardinality(String),
    `statusMessage`      String,
    `statusCodeString`   String,
    `spanKind`           String
)
    ENGINE = Distributed('{cluster}', 'signoz_traces', 'signoz_index_v2', rand());