DROP TABLE IF EXISTS signoz_traces.distributed_span_attribute_keys;

CREATE TABLE signoz_traces.distributed_span_attribute_keys
(
    `time`     DateTime,
    `tagKey`   LowCardinality(String),
    `tagType`  Enum8('tag' = 1, 'resource' = 2) CODEC (ZSTD(1)),
    `dataType` Enum8('string' = 1, 'bool' = 2, 'float64' = 3) CODEC (ZSTD(1)),
    `isColumn` Bool
)
    ENGINE = Distributed('{cluster}', 'signoz_traces', 'span_attribute_keys', rand());