CREATE DATABASE IF NOT EXISTS cube_trace;


show databases;

-- trace_index
DROP TABLE IF EXISTS cube_trace.trace_index;
create table cube_trace.trace_index ON CLUSTER '{cluster}'
(
    time               DateTime,
    timestamp          DateTime64(9),
    traceID            String,
    spanID             String,
    parentSpanID       String,
    serviceName        LowCardinality(String),
    name               LowCardinality(String),
    kind               Int8,
    durationNano       UInt64,
    statusCode         Int16,
    externalHttpMethod LowCardinality(String),
    externalHttpUrl    LowCardinality(String),
    dbSystem           LowCardinality(String),
    dbName             LowCardinality(String),
    dbOperation        LowCardinality(String),
    peerService        LowCardinality(String),
    events             Array(String),
    httpMethod         LowCardinality(String),
    httpUrl            LowCardinality(String),
    httpRoute          LowCardinality(String),
    httpHost           LowCardinality(String),
    msgSystem          LowCardinality(String),
    msgOperation       LowCardinality(String),
    hasError           <PERSON>,
    rpcSystem          LowCardinality(String),
    rpcService         LowCardinality(String),
    rpcMethod          LowCardinality(String),
    responseStatusCode LowCardinality(String),
    stringTagMap       Map(String, String),
    numberTagMap       Map(String, Float64),
    boolTagMap         Map(String, Bool),
    resourceTagsMap    Map(String, String),
    isRemote           LowCardinality(String),
    statusMessage      String,
    statusCodeString   String,
    spanKind           String,
    component          LowCardinality(String),
    INDEX idx_service serviceName TYPE bloom_filter GRANULARITY 4,
    INDEX idx_name name TYPE bloom_filter GRANULARITY 4,
    INDEX idx_kind kind TYPE minmax GRANULARITY 4,
    INDEX idx_duration durationNano TYPE minmax GRANULARITY 1,
    INDEX idx_hasError hasError TYPE set(2) GRANULARITY 1,
    INDEX idx_httpRoute httpRoute TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpUrl httpUrl TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpHost httpHost TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpMethod httpMethod TYPE bloom_filter GRANULARITY 4,
    INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1,
    INDEX idx_rpcMethod rpcMethod TYPE bloom_filter GRANULARITY 4,
    INDEX idx_responseStatusCode responseStatusCode TYPE set(0) GRANULARITY 1,
    INDEX idx_resourceTagsMapKeys mapKeys(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_resourceTagsMapValues mapValues(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_statusCodeString statusCodeString TYPE set(3) GRANULARITY 4,
    INDEX idx_spanKind spanKind TYPE set(5) GRANULARITY 4
)
    engine = ReplicatedMergeTree('/clickhouse/tables/{shard}/cube_trace/trace_index.de508b7dea7e4dab9dfbef921ab94a15',
                                 '{replica}') PARTITION BY toDate(timestamp)
        PRIMARY KEY (serviceName, hasError, toStartOfHour(timestamp), name)
        ORDER BY (serviceName, hasError, toStartOfHour(timestamp), name, timestamp)
        TTL time + toIntervalDay(7)
        SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;


-- trace_error_index
DROP TABLE IF EXISTS cube_trace.trace_error_index;
create table cube_trace.trace_error_index ON CLUSTER '{cluster}'
(
    time                DateTime,
    timestamp           DateTime64(9),
    errorID             String,
    groupID             String,
    traceID             String,
    spanID              String,
    serviceName         LowCardinality(String),
    exceptionType       LowCardinality(String),
    exceptionMessage    String,
    exceptionStacktrace String,
    exceptionEscaped    Bool,
    resourceTagsMap     Map(String, String),
    component          LowCardinality(String),
    INDEX idx_error_id errorID TYPE bloom_filter GRANULARITY 4,
    INDEX idx_resourceTagsMapKeys mapKeys(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_resourceTagsMapValues mapValues(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64
)
    engine = ReplicatedMergeTree(
            '/clickhouse/tables/{shard}/cube_trace/trace_error_index.5e468acc72e74932be677d1ed9842cc1',
            '{replica}') PARTITION BY toDate(timestamp)
        ORDER BY (timestamp, groupID)
        TTL time + toIntervalDay(7)
        SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;

-- trace_spans
DROP TABLE IF EXISTS cube_trace.trace_spans;
create table cube_trace.trace_spans ON CLUSTER '{cluster}'
(
    time      DateTime,
    timestamp DateTime64(9),
    traceID   String,
    model     String
)
    engine = ReplicatedMergeTree('/clickhouse/tables/{shard}/cube_trace/trace_spans.71e3c40f456a4bbfb1bea17a8ec818d2',
                                 '{replica}') PARTITION BY toDate(timestamp)
        ORDER BY traceID
        TTL time + toIntervalDay(7)
        SETTINGS index_granularity = 1024, ttl_only_drop_parts = 1;


-- span_attributes
DROP TABLE IF EXISTS cube_trace.span_attributes;
create table cube_trace.span_attributes ON CLUSTER '{cluster}'
(
    time            DateTime,
    timestamp       DateTime,
    tagKey          LowCardinality(String),
    tagType         Enum8('tag' = 1, 'resource' = 2),
    dataType        Enum8('string' = 1, 'bool' = 2, 'float64' = 3),
    stringTagValue  String,
    float64TagValue Nullable(Float64),
    isColumn        Bool
)
    engine = ReplicatedReplacingMergeTree(
            '/clickhouse/tables/{shard}/cube_trace/span_attributes.7155487028564228b24bc59f5af1e238',
            '{replica}')
        ORDER BY (tagKey, tagType, dataType, stringTagValue, float64TagValue, isColumn)
        TTL time + toIntervalDay(15)
        SETTINGS ttl_only_drop_parts = 1, allow_nullable_key = 1, index_granularity = 8192;

-- span_attributes_keys
DROP TABLE IF EXISTS cube_trace.span_attributes_keys;
create table cube_trace.span_attributes_keys ON CLUSTER '{cluster}'
(
    time     DateTime,
    tagKey   LowCardinality(String),
    tagType  Enum8('tag' = 1, 'resource' = 2),
    dataType Enum8('string' = 1, 'bool' = 2, 'float64' = 3),
    isColumn Bool
)
    engine = ReplicatedReplacingMergeTree(
            '/clickhouse/tables/{shard}/cube_trace/span_attributes_keys.c2acd359d201477baecac3e658c50058',
            '{replica}')
        ORDER BY (tagKey, tagType, dataType, isColumn)
        TTL time + toIntervalDay(15)
        SETTINGS index_granularity = 8192;

-- top_level_operations
DROP TABLE IF EXISTS cube_trace.top_level_operations;
create table cube_trace.top_level_operations ON CLUSTER '{cluster}'
(
    name        LowCardinality(String),
    serviceName LowCardinality(String),
    time        DateTime default now()
)
    engine = ReplicatedReplacingMergeTree(
            '/clickhouse/tables/{shard}/cube_trace/top_level_operations.aa6b6794123e46cf9d8a963e5456cb43',
            '{replica}')
        ORDER BY (serviceName, name)
        TTL time + toIntervalDay(30)
        SETTINGS index_granularity = 8192;

-- usage_explorer
DROP TABLE IF EXISTS cube_trace.usage_explorer;
create table cube_trace.usage_explorer ON CLUSTER '{cluster}'
(
    timestamp    DateTime64(9),
    service_name LowCardinality(String),
    count        UInt64
)
    engine = ReplicatedSummingMergeTree(
            '/clickhouse/tables/{shard}/cube_trace/usage_explorer.dc72ff1f3f9343e486c99fcb8e54a4f6',
            '{replica}') PARTITION BY toDate(timestamp)
        ORDER BY (timestamp, service_name)
        TTL toDateTime(timestamp) + toIntervalDay(30)
        SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;


-- dependency_graph_minutes
DROP TABLE IF EXISTS cube_trace.dependency_graph_minutes;
create table cube_trace.dependency_graph_minutes ON CLUSTER '{cluster}'
(
    src                      LowCardinality(String),
    dest                     LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              SimpleAggregateFunction(sum, UInt64),
    total_count              SimpleAggregateFunction(sum, UInt64),
    timestamp                DateTime,
    deployment_environment   LowCardinality(String),
    k8s_cluster_name         LowCardinality(String),
    k8s_namespace_name       LowCardinality(String)
)
    engine = ReplicatedAggregatingMergeTree(
            '/clickhouse/tables/{shard}/cube_trace/dependency_graph_minutes.ecf8723135a241cd9562dc066bba9f6c',
            '{replica}') PARTITION BY toDate(timestamp)
        ORDER BY (timestamp, src, dest, deployment_environment, k8s_cluster_name, k8s_namespace_name)
        TTL timestamp + toIntervalDay(15)
        SETTINGS index_granularity = 8192;

-- trace_index_cold
DROP TABLE IF EXISTS cube_trace.trace_index_cold;
create table cube_trace.trace_index_cold ON CLUSTER '{cluster}'
(
    time               DateTime,
    timestamp          DateTime64(9),
    traceID            String,
    spanID             String,
    parentSpanID       String,
    serviceName        LowCardinality(String),
    name               LowCardinality(String),
    kind               Int8,
    durationNano       UInt64,
    statusCode         Int16,
    externalHttpMethod LowCardinality(String),
    externalHttpUrl    LowCardinality(String),
    dbSystem           LowCardinality(String),
    dbName             LowCardinality(String),
    dbOperation        LowCardinality(String),
    peerService        LowCardinality(String),
    events             Array(String),
    httpMethod         LowCardinality(String),
    httpUrl            LowCardinality(String),
    httpRoute          LowCardinality(String),
    httpHost           LowCardinality(String),
    msgSystem          LowCardinality(String),
    msgOperation       LowCardinality(String),
    hasError           Bool,
    rpcSystem          LowCardinality(String),
    rpcService         LowCardinality(String),
    rpcMethod          LowCardinality(String),
    responseStatusCode LowCardinality(String),
    stringTagMap       Map(String, String),
    numberTagMap       Map(String, Float64),
    boolTagMap         Map(String, Bool),
    resourceTagsMap    Map(String, String),
    isRemote           LowCardinality(String),
    statusMessage      String,
    statusCodeString   String,
    spanKind           String,
    component          LowCardinality(String),
    INDEX idx_service serviceName TYPE bloom_filter GRANULARITY 4,
    INDEX idx_name name TYPE bloom_filter GRANULARITY 4,
    INDEX idx_kind kind TYPE minmax GRANULARITY 4,
    INDEX idx_duration durationNano TYPE minmax GRANULARITY 1,
    INDEX idx_hasError hasError TYPE set(2) GRANULARITY 1,
    INDEX idx_httpRoute httpRoute TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpUrl httpUrl TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpHost httpHost TYPE bloom_filter GRANULARITY 4,
    INDEX idx_httpMethod httpMethod TYPE bloom_filter GRANULARITY 4,
    INDEX idx_timestamp timestamp TYPE minmax GRANULARITY 1,
    INDEX idx_rpcMethod rpcMethod TYPE bloom_filter GRANULARITY 4,
    INDEX idx_responseStatusCode responseStatusCode TYPE set(0) GRANULARITY 1,
    INDEX idx_resourceTagsMapKeys mapKeys(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_resourceTagsMapValues mapValues(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_statusCodeString statusCodeString TYPE set(3) GRANULARITY 4,
    INDEX idx_spanKind spanKind TYPE set(5) GRANULARITY 4
)
    engine = ReplicatedMergeTree(
            '/clickhouse/tables/{shard}/cube_trace/trace_index_cold.a8a6b08ad0444721b66da8b8b26b2d19',
            '{replica}')
        PARTITION BY toDate(timestamp)
        PRIMARY KEY (serviceName, hasError, toStartOfHour(timestamp), name)
        ORDER BY (serviceName, hasError, toStartOfHour(timestamp), name, timestamp)
        TTL time + toIntervalDay(15)
        SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;

-- trace_spans_cold
DROP TABLE IF EXISTS cube_trace.trace_spans_cold;
create table cube_trace.trace_spans_cold ON CLUSTER '{cluster}'
(
    time      DateTime,
    timestamp DateTime64(9),
    traceID   String,
    model     String
)
    engine = ReplicatedMergeTree(
            '/clickhouse/tables/{shard}/cube_trace/trace_spans_cold.8a6a6ab9b49c4c6aab3a10d126baa7cf',
            '{replica}')
        PARTITION BY toDate(timestamp)
        ORDER BY traceID
        TTL time + toIntervalDay(15)
        SETTINGS index_granularity = 1024, ttl_only_drop_parts = 1;

-- trace_error_index_cold
DROP TABLE IF EXISTS cube_trace.trace_error_index_cold;
create table cube_trace.trace_error_index_cold ON CLUSTER '{cluster}'
(
    time                DateTime,
    timestamp           DateTime64(9),
    errorID             String,
    groupID             String,
    traceID             String,
    spanID              String,
    serviceName         LowCardinality(String),
    exceptionType       LowCardinality(String),
    exceptionMessage    String,
    exceptionStacktrace String,
    exceptionEscaped    Bool,
    resourceTagsMap     Map(String, String),
    component           LowCardinality(String),
    INDEX idx_error_id errorID TYPE bloom_filter GRANULARITY 4,
    INDEX idx_resourceTagsMapKeys mapKeys(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64,
    INDEX idx_resourceTagsMapValues mapValues(resourceTagsMap) TYPE bloom_filter(0.01) GRANULARITY 64
)
    engine = ReplicatedMergeTree(
            '/clickhouse/tables/{shard}/cube_trace/trace_error_index_cold.ad7c8db6b225483c925e50b790fa825d',
            '{replica}')
        PARTITION BY toDate(timestamp)
        ORDER BY (timestamp, groupID)
        TTL time + toIntervalDay(15)
        SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;

-- distributed_trace_index
DROP TABLE IF EXISTS cube_trace.distributed_trace_index;
create table cube_trace.distributed_trace_index ON CLUSTER '{cluster}'
(
    time               DateTime,
    timestamp          DateTime64(9),
    traceID            String,
    spanID             String,
    parentSpanID       String,
    serviceName        LowCardinality(String),
    name               LowCardinality(String),
    kind               Int8,
    durationNano       UInt64,
    statusCode         Int16,
    externalHttpMethod LowCardinality(String),
    externalHttpUrl    LowCardinality(String),
    dbSystem           LowCardinality(String),
    dbName             LowCardinality(String),
    dbOperation        LowCardinality(String),
    peerService        LowCardinality(String),
    events             Array(String),
    httpMethod         LowCardinality(String),
    httpUrl            LowCardinality(String),
    httpRoute          LowCardinality(String),
    httpHost           LowCardinality(String),
    msgSystem          LowCardinality(String),
    msgOperation       LowCardinality(String),
    hasError           Bool,
    rpcSystem          LowCardinality(String),
    rpcService         LowCardinality(String),
    rpcMethod          LowCardinality(String),
    responseStatusCode LowCardinality(String),
    stringTagMap       Map(String, String),
    numberTagMap       Map(String, Float64),
    boolTagMap         Map(String, Bool),
    resourceTagsMap    Map(String, String),
    isRemote           LowCardinality(String),
    statusMessage      String,
    statusCodeString   String,
    spanKind           String,
    component          LowCardinality(String)
)
    engine = Distributed('{cluster}', 'cube_trace', 'trace_index', cityHash64(traceID));

-- distributed_trace_error_index
DROP TABLE IF EXISTS cube_trace.distributed_trace_error_index;
create table cube_trace.distributed_trace_error_index ON CLUSTER '{cluster}'
(
    time                DateTime,
    timestamp           DateTime64(9),
    errorID             String,
    groupID             String,
    traceID             String,
    spanID              String,
    serviceName         LowCardinality(String),
    exceptionType       LowCardinality(String),
    exceptionMessage    String,
    exceptionStacktrace String,
    exceptionEscaped    Bool,
    resourceTagsMap     Map(String, String),
    component           LowCardinality(String)
)
    engine = Distributed('{cluster}', 'cube_trace', 'trace_error_index', cityHash64(groupID));


-- distributed_trace_spans
DROP TABLE IF EXISTS cube_trace.distributed_trace_spans;
create table cube_trace.distributed_trace_spans ON CLUSTER '{cluster}'
(
    time      DateTime,
    timestamp DateTime64(9),
    traceID   String,
    model     String
)
    engine = Distributed('{cluster}', 'cube_trace', 'trace_spans', cityHash64(traceID));

-- distributed_span_attributes
DROP TABLE IF EXISTS cube_trace.distributed_span_attributes;
create table cube_trace.distributed_span_attributes ON CLUSTER '{cluster}'
(
    time            DateTime,
    timestamp       DateTime,
    tagKey          LowCardinality(String),
    tagType         Enum8('tag' = 1, 'resource' = 2),
    dataType        Enum8('string' = 1, 'bool' = 2, 'float64' = 3),
    stringTagValue  String,
    float64TagValue Nullable(Float64),
    isColumn        Bool
)
    engine = Distributed('{cluster}', 'cube_trace', 'span_attributes', cityHash64(rand()));

-- distributed_span_attributes_keys
DROP TABLE IF EXISTS cube_trace.distributed_span_attributes_keys;
create table cube_trace.distributed_span_attributes_keys ON CLUSTER '{cluster}'
(
    time     DateTime,
    tagKey   LowCardinality(String),
    tagType  Enum8('tag' = 1, 'resource' = 2),
    dataType Enum8('string' = 1, 'bool' = 2, 'float64' = 3),
    isColumn Bool
)
    engine = Distributed('{cluster}', 'cube_trace', 'span_attributes_keys', cityHash64(rand()));

-- distributed_top_level_operations
DROP TABLE IF EXISTS cube_trace.distributed_top_level_operations;
create table cube_trace.distributed_top_level_operations ON CLUSTER '{cluster}'
(
    name        LowCardinality(String),
    serviceName LowCardinality(String),
    time        DateTime default now()
)
    engine = Distributed('{cluster}', 'cube_trace', 'top_level_operations', cityHash64(rand()));

-- distributed_usage_explorer
DROP TABLE IF EXISTS cube_trace.distributed_usage_explorer;
create table cube_trace.distributed_usage_explorer ON CLUSTER '{cluster}'
(
    timestamp    DateTime64(9),
    service_name LowCardinality(String),
    count        UInt64
)
    engine = Distributed('{cluster}', 'cube_trace', 'usage_explorer', cityHash64(rand()));

-- distributed_dependency_graph_minutes
DROP TABLE IF EXISTS cube_trace.distributed_dependency_graph_minutes;
create table cube_trace.distributed_dependency_graph_minutes ON CLUSTER '{cluster}'
(
    src                      LowCardinality(String),
    dest                     LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              SimpleAggregateFunction(sum, UInt64),
    total_count              SimpleAggregateFunction(sum, UInt64),
    timestamp                DateTime,
    deployment_environment   LowCardinality(String),
    k8s_cluster_name         LowCardinality(String),
    k8s_namespace_name       LowCardinality(String)
)
    engine = Distributed('{cluster}', 'cube_trace', 'dependency_graph_minutes', cityHash64(rand()));

-- distributed_trace_index_cold
DROP TABLE IF EXISTS cube_trace.distributed_trace_index_cold;
create table cube_trace.distributed_trace_index_cold ON CLUSTER '{cluster}'
(
    time               DateTime,
    timestamp          DateTime64(9),
    traceID            String,
    spanID             String,
    parentSpanID       String,
    serviceName        LowCardinality(String),
    name               LowCardinality(String),
    kind               Int8,
    durationNano       UInt64,
    statusCode         Int16,
    externalHttpMethod LowCardinality(String),
    externalHttpUrl    LowCardinality(String),
    dbSystem           LowCardinality(String),
    dbName             LowCardinality(String),
    dbOperation        LowCardinality(String),
    peerService        LowCardinality(String),
    events             Array(String),
    httpMethod         LowCardinality(String),
    httpUrl            LowCardinality(String),
    httpRoute          LowCardinality(String),
    httpHost           LowCardinality(String),
    msgSystem          LowCardinality(String),
    msgOperation       LowCardinality(String),
    hasError           Bool,
    rpcSystem          LowCardinality(String),
    rpcService         LowCardinality(String),
    rpcMethod          LowCardinality(String),
    responseStatusCode LowCardinality(String),
    stringTagMap       Map(String, String),
    numberTagMap       Map(String, Float64),
    boolTagMap         Map(String, Bool),
    resourceTagsMap    Map(String, String),
    isRemote           LowCardinality(String),
    statusMessage      String,
    statusCodeString   String,
    spanKind           String,
    component          LowCardinality(String)
)
    engine = Distributed('{cluster}', 'cube_trace', 'trace_index_cold', cityHash64(traceID));

-- distributed_trace_spans_cold
DROP TABLE IF EXISTS cube_trace.distributed_trace_spans_cold;
create table cube_trace.distributed_trace_spans_cold ON CLUSTER '{cluster}'
(
    time      DateTime,
    timestamp DateTime64(9),
    traceID   String,
    model     String
)
    engine = Distributed('{cluster}', 'cube_trace', 'trace_spans_cold', cityHash64(traceID));

-- distributed_trace_error_index_cold
DROP TABLE IF EXISTS cube_trace.distributed_trace_error_index_cold;
create table cube_trace.distributed_trace_error_index_cold ON CLUSTER '{cluster}'
(
    time                DateTime,
    timestamp           DateTime64(9),
    errorID             String,
    groupID             String,
    traceID             String,
    spanID              String,
    serviceName         LowCardinality(String),
    exceptionType       LowCardinality(String),
    exceptionMessage    String,
    exceptionStacktrace String,
    exceptionEscaped    Bool,
    resourceTagsMap     Map(String, String),
    component           LowCardinality(String)
)
    engine = Distributed('{cluster}', 'cube_trace', 'trace_error_index_cold', cityHash64(groupID));

-- dependency_graph_minutes_db_calls_mv
DROP VIEW IF EXISTS cube_trace.dependency_graph_minutes_db_calls_mv;
CREATE MATERIALIZED VIEW cube_trace.dependency_graph_minutes_db_calls_mv
            ON CLUSTER '{cluster}'
            TO cube_trace.dependency_graph_minutes
            (
             `src` LowCardinality(String),
             `dest` LowCardinality(String),
             `duration_quantiles_state` AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
             `error_count` UInt64,
             `total_count` UInt64,
             `timestamp` DateTime,
             `deployment_environment` String,
             `k8s_cluster_name` String,
             `k8s_namespace_name` String
                )
AS
SELECT serviceName                                                         AS src,
       dbSystem                                                            AS dest,
       quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano)) AS duration_quantiles_state,
       countIf(statusCode = 2)                                             AS error_count,
       count(*)                                                            AS total_count,
       toStartOfMinute(timestamp)                                          AS timestamp,
       resourceTagsMap['deployment.environment']                           AS deployment_environment,
       resourceTagsMap['k8s.cluster.name']                                 AS k8s_cluster_name,
       resourceTagsMap['k8s.namespace.name']                               AS k8s_namespace_name
FROM cube_trace.trace_index
WHERE (dest != '')
  AND (kind != 2)
GROUP BY timestamp,
         src,
         dest,
         deployment_environment,
         k8s_cluster_name,
         k8s_namespace_name;

-- dependency_graph_minutes_messaging_calls_mv
DROP VIEW IF EXISTS cube_trace.dependency_graph_minutes_messaging_calls_mv;
CREATE MATERIALIZED VIEW cube_trace.dependency_graph_minutes_messaging_calls_mv
            ON CLUSTER '{cluster}'
            TO cube_trace.dependency_graph_minutes
            (
             `src` LowCardinality(String),
             `dest` LowCardinality(String),
             `duration_quantiles_state` AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
             `error_count` UInt64,
             `total_count` UInt64,
             `timestamp` DateTime,
             `deployment_environment` String,
             `k8s_cluster_name` String,
             `k8s_namespace_name` String
                )
AS
SELECT serviceName                                                         AS src,
       msgSystem                                                           AS dest,
       quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano)) AS duration_quantiles_state,
       countIf(statusCode = 2)                                             AS error_count,
       count(*)                                                            AS total_count,
       toStartOfMinute(timestamp)                                          AS timestamp,
       resourceTagsMap['deployment.environment']                           AS deployment_environment,
       resourceTagsMap['k8s.cluster.name']                                 AS k8s_cluster_name,
       resourceTagsMap['k8s.namespace.name']                               AS k8s_namespace_name
FROM cube_trace.trace_index
WHERE (dest != '')
  AND (kind != 2)
GROUP BY timestamp,
         src,
         dest,
         deployment_environment,
         k8s_cluster_name,
         k8s_namespace_name;

-- dependency_graph_minutes_service_calls_mv
DROP VIEW IF EXISTS cube_trace.dependency_graph_minutes_service_calls_mv;
CREATE MATERIALIZED VIEW cube_trace.dependency_graph_minutes_service_calls_mv
            ON CLUSTER '{cluster}'
            TO cube_trace.dependency_graph_minutes
            (
             `src` LowCardinality(String),
             `dest` LowCardinality(String),
             `duration_quantiles_state` AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
             `error_count` UInt64,
             `total_count` UInt64,
             `timestamp` DateTime,
             `deployment_environment` String,
             `k8s_cluster_name` String,
             `k8s_namespace_name` String
                )
AS
SELECT A.serviceName                                                         AS src,
       B.serviceName                                                         AS dest,
       quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(B.durationNano)) AS duration_quantiles_state,
       countIf(B.statusCode = 2)                                             AS error_count,
       count(*)                                                              AS total_count,
       toStartOfMinute(B.timestamp)                                          AS timestamp,
       B.resourceTagsMap['deployment.environment']                           AS deployment_environment,
       B.resourceTagsMap['k8s.cluster.name']                                 AS k8s_cluster_name,
       B.resourceTagsMap['k8s.namespace.name']                               AS k8s_namespace_name
FROM cube_trace.trace_index AS A,
     cube_trace.trace_index AS B
WHERE (A.serviceName != B.serviceName)
  AND (A.spanID = B.parentSpanID)
GROUP BY timestamp,
         src,
         dest,
         deployment_environment,
         k8s_cluster_name,
         k8s_namespace_name;

-- root_operations
DROP VIEW IF EXISTS cube_trace.root_operations;
CREATE MATERIALIZED VIEW cube_trace.root_operations
            ON CLUSTER '{cluster}'
            TO cube_trace.top_level_operations
            (
             `name` LowCardinality(String),
             `serviceName` LowCardinality(String)
                )
AS
SELECT DISTINCT name,
                serviceName
FROM cube_trace.trace_index
WHERE parentSpanID = '';

-- sub_root_operations
DROP VIEW IF EXISTS cube_trace.sub_root_operations;
CREATE MATERIALIZED VIEW cube_trace.sub_root_operations
            ON CLUSTER '{cluster}'
            TO cube_trace.top_level_operations
            (
             `name` LowCardinality(String),
             `serviceName` LowCardinality(String)
                )
AS
SELECT DISTINCT name,
                serviceName
FROM cube_trace.trace_index AS A,
     cube_trace.trace_index AS B
WHERE (A.serviceName != B.serviceName)
  AND (A.parentSpanID = B.spanID);

-- usage_explorer_mv
DROP VIEW IF EXISTS cube_trace.usage_explorer_mv;
CREATE MATERIALIZED VIEW cube_trace.usage_explorer_mv
            ON CLUSTER '{cluster}'
            TO cube_trace.usage_explorer
            (
             `timestamp` DateTime,
             `service_name` LowCardinality(String),
             `count` UInt64
                )
AS
SELECT toStartOfHour(timestamp) AS timestamp,
       serviceName              AS service_name,
       count()                  AS count
FROM cube_trace.trace_index
GROUP BY timestamp,
         serviceName;