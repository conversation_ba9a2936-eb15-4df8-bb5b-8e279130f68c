CREATE DATABASE IF NOT EXISTS cube_trace ON CLUSTER '{cluster}';

CREATE TABLE IF NOT EXISTS cube_trace.server_minutes_apm_metric ON CLUSTER '{cluster}'
(
    time                     DateTime,
    service                  LowCardinality(String),
    cluster                  LowCardinality(String),
    region                   LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              AggregateFunction(count, UInt64),
    total_count              AggregateFunction(count, UInt64),
    avg_cost                 AggregateFunction(avg, UInt64),
    http_route               LowCardinality(String),
    http_method              LowCardinality(String),
    response_code       	 LowCardinality(String)
)
ENGINE = ReplicatedAggregatingMergeTree('/clickhouse/tables/{shard}/cube_trace/server_minutes_apm_metric.ecf8723135a241cd9562dc066bba9f62','{replica}')
PARTITION BY toDate(time)
ORDER BY (time, service, cluster, region, http_route, http_method, response_code)
TTL time + INTERVAL 30 DAY DELETE
SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;



CREATE TABLE IF NOT EXISTS cube_trace.distributed_server_minutes_apm_metric ON CLUSTER '{cluster}'
(
    time                     DateTime,
    service                  LowCardinality(String),
    cluster                  LowCardinality(String),
    region                   LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              AggregateFunction(count, UInt64),
    total_count              AggregateFunction(count, UInt64),
    avg_cost                 AggregateFunction(avg, UInt64),
    http_route               LowCardinality(String),
    http_method              LowCardinality(String),
    response_code       	 LowCardinality(String)
)
ENGINE = Distributed('{cluster}', 'cube_trace', 'server_minutes_apm_metric', cityHash64(rand()));



CREATE MATERIALIZED VIEW IF NOT EXISTS cube_trace.mv_server_minutes_apm_metric
ON CLUSTER '{cluster}'
TO cube_trace.server_minutes_apm_metric
AS
SELECT
    serviceName                                                          AS service,
    quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano))  AS duration_quantiles_state,
    countStateIf(statusCode = 2)                                         AS error_count,
    countState(*)                                                        AS total_count,
    avgState(durationNano)                 								 AS avg_cost,
    toStartOfMinute(timestamp)                                           AS time,
    resourceTagsMap['appcluster']                                        AS cluster,
    resourceTagsMap['regionId']                                          AS region,
    httpMethod                                                           AS http_method,
    httpRoute 															 AS http_route,
    responseStatusCode                                                   AS response_code
FROM cube_trace.trace_index
WHERE kind = 2
GROUP BY time,
         service,
         cluster,
         region,
         http_route,
         http_method,
         response_code
SETTINGS async_insert = 1;


CREATE TABLE IF NOT EXISTS cube_trace.db_minutes_apm_metric ON CLUSTER '{cluster}'
(
    time                     DateTime,
    service                  LowCardinality(String),
    cluster                  LowCardinality(String),
    region                   LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              AggregateFunction(count, UInt64),
    total_count              AggregateFunction(count, UInt64),
    avg_cost                 AggregateFunction(avg, UInt64),
    db_system                LowCardinality(String),
    db_table                 LowCardinality(String),
    db_operation		     LowCardinality(String),
    db_statement             String,
    host           			 LowCardinality(String)
)
ENGINE = ReplicatedAggregatingMergeTree('/clickhouse/tables/{shard}/cube_trace/db_minutes_apm_metric.ecf8723135a241cd9562dc066bba9f62','{replica}')
PARTITION BY toDate(time)
ORDER BY (time, service, cluster, region, db_system, db_table, db_operation, db_statement, host)
TTL time + INTERVAL 30 DAY DELETE
SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;



CREATE TABLE IF NOT EXISTS cube_trace.distributed_db_minutes_apm_metric ON CLUSTER '{cluster}'
(
    time                     DateTime,
    service                  LowCardinality(String),
    cluster                  LowCardinality(String),
    region                   LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              AggregateFunction(count, UInt64),
    total_count              AggregateFunction(count, UInt64),
    avg_cost                 AggregateFunction(avg, UInt64),
    db_system                LowCardinality(String),
    db_table                 LowCardinality(String),
    db_operation		     LowCardinality(String),
    db_statement             String,
    host           			 LowCardinality(String)
)
ENGINE = Distributed('{cluster}', 'cube_trace', 'db_minutes_apm_metric', cityHash64(rand()));



CREATE MATERIALIZED VIEW IF NOT EXISTS cube_trace.mv_db_minutes_apm_metric
ON CLUSTER '{cluster}'
TO cube_trace.db_minutes_apm_metric
AS
SELECT
    serviceName                                                          AS service,
    quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano))  AS duration_quantiles_state,
    countStateIf(statusCode = 2)                                         AS error_count,
    countState(*)                                                        AS total_count,
    avgState(durationNano)                 								 AS avg_cost,
    toStartOfMinute(timestamp)                                           AS time,
    resourceTagsMap['appcluster']                                        AS cluster,
    resourceTagsMap['regionId']                                          AS region,
    stringTagMap['db.sql.table']                                         AS db_table,
    CASE
        WHEN dbOperation != '' THEN dbOperation
        ELSE name
    END 															     AS db_operation,
    CASE
        WHEN dbSystem = 'mysql' THEN
            trim(
                replaceRegexpOne(stringTagMap['db.statement'], '/\\*.*?\\*/', '')
            )
        ELSE ''
    END 																 AS db_statement,
    dbSystem                                                             AS db_system,
    CASE
        WHEN httpHost != '' THEN httpHost
        ELSE stringTagMap['network.peer.address']
    END 														         AS host
FROM cube_trace.trace_index
WHERE kind = 3 AND db_system != ''
GROUP BY time,
         service,
         cluster,
         region,
         db_table,
         db_operation,
         db_statement,
         db_system,
         host
SETTINGS async_insert = 1;



CREATE TABLE IF NOT EXISTS cube_trace.client_minutes_apm_metric ON CLUSTER '{cluster}'
(
    time                     DateTime,
    service                  LowCardinality(String),
    cluster                  LowCardinality(String),
    region                   LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              AggregateFunction(count, UInt64),
    total_count              AggregateFunction(count, UInt64),
    avg_cost                 AggregateFunction(avg, UInt64),
    host                     LowCardinality(String),
    http_method              LowCardinality(String),
    response_code       	 LowCardinality(String)
)
ENGINE = ReplicatedAggregatingMergeTree('/clickhouse/tables/{shard}/cube_trace/client_minutes_apm_metric.ecf8723135a241cd9562dc066bba9f62','{replica}')
PARTITION BY toDate(time)
ORDER BY (time, service, cluster, region, host, http_method, response_code)
TTL time + INTERVAL 30 DAY DELETE
SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;



CREATE TABLE IF NOT EXISTS cube_trace.distributed_client_minutes_apm_metric ON CLUSTER '{cluster}'
(
    time                     DateTime,
    service                  LowCardinality(String),
    cluster                  LowCardinality(String),
    region                   LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              AggregateFunction(count, UInt64),
    total_count              AggregateFunction(count, UInt64),
    avg_cost                 AggregateFunction(avg, UInt64),
    host               		 LowCardinality(String),
    http_method              LowCardinality(String),
    response_code       	 LowCardinality(String)
    )
ENGINE = Distributed('{cluster}', 'cube_trace', 'client_minutes_apm_metric', cityHash64(rand()));



CREATE MATERIALIZED VIEW IF NOT EXISTS cube_trace.mv_client_minutes_apm_metric
ON CLUSTER '{cluster}'
TO cube_trace.client_minutes_apm_metric
AS
SELECT
    serviceName                                                          AS service,
    quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano))  AS duration_quantiles_state,
    countStateIf(statusCode = 2)                                         AS error_count,
    countState(*)                                                        AS total_count,
    avgState(durationNano)                 								 AS avg_cost,
    toStartOfMinute(timestamp)                                           AS time,
    resourceTagsMap['appcluster']                                        AS cluster,
    resourceTagsMap['regionId']                                          AS region,
    httpMethod                                                           AS http_method,
    httpHost 															 AS host,
    responseStatusCode                                                   AS response_code
FROM cube_trace.trace_index
WHERE kind = 3 AND http_method != '' AND dbSystem = ''
GROUP BY time,
         service,
         cluster,
         region,
         host,
         http_method,
         response_code
SETTINGS async_insert = 1;


CREATE TABLE IF NOT EXISTS cube_trace.message_queue_minutes_apm_metric ON CLUSTER '{cluster}'
(
    time                     DateTime,
    service                  LowCardinality(String),
    cluster                  LowCardinality(String),
    region                   LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              AggregateFunction(count, UInt64),
    total_count              AggregateFunction(count, UInt64),
    avg_cost                 AggregateFunction(avg, UInt64),
    topic_name               LowCardinality(String),
    operation                LowCardinality(String)
)
ENGINE = ReplicatedAggregatingMergeTree('/clickhouse/tables/{shard}/cube_trace/message_queue_minutes_apm_metric.ecf8723135a241cd9562dc066bba9f62','{replica}')
PARTITION BY toDate(time)
ORDER BY (time, cluster, region, topic_name, operation)
TTL time + INTERVAL 30 DAY DELETE
SETTINGS index_granularity = 8192, ttl_only_drop_parts = 1;



CREATE TABLE IF NOT EXISTS cube_trace.distributed_message_queue_minutes_apm_metric ON CLUSTER '{cluster}'
(
    time                     DateTime,
    cluster                  LowCardinality(String),
    region                   LowCardinality(String),
    duration_quantiles_state AggregateFunction(quantiles(0.5, 0.75, 0.9, 0.95, 0.99), Float64),
    error_count              AggregateFunction(count, UInt64),
    total_count              AggregateFunction(count, UInt64),
    avg_cost                 AggregateFunction(avg, UInt64),
    topic_name               LowCardinality(String),
    operation             	 LowCardinality(String)
)
ENGINE = Distributed('{cluster}', 'cube_trace', 'message_queue_minutes_apm_metric', cityHash64(rand()));



CREATE MATERIALIZED VIEW IF NOT EXISTS cube_trace.mv_message_queue_minutes_apm_metric
ON CLUSTER '{cluster}'
TO cube_trace.message_queue_minutes_apm_metric
AS
SELECT
    serviceName                                                          AS service,
    quantilesState(0.5, 0.75, 0.9, 0.95, 0.99)(toFloat64(durationNano))  AS duration_quantiles_state,
    countStateIf(statusCode = 2)                                         AS error_count,
    countState(*)                                                        AS total_count,
    avgState(durationNano)                 								 AS avg_cost,
    toStartOfMinute(timestamp)                                           AS time,
    resourceTagsMap['appcluster']                                        AS cluster,
    resourceTagsMap['regionId']                                          AS region,
    stringTagMap['messaging.destination.name']                           AS topic_name,
    msgOperation 														 AS operation
FROM cube_trace.trace_index
WHERE kind IN (4,5)
GROUP BY time,
         service,
         cluster,
         region,
         topic_name,
         operation
SETTINGS async_insert = 1;