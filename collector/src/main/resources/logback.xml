<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property resource="application.properties"/>
    <property name="PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS}[%X{x-zm-trackingid}][%t][%p][%c{0}] - %m%n"/>
    <springProperty scope="context" name="LOG_PATH" source="log.filePath"/>
    <springProperty scope="context" name="APM_LOG_HOME" source="apm.log.home"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                %boldGreen(%date{ISO8601}) %highlight([%-5level]) %boldCyan([%file]) %boldCyan([%line]) %yellow([%thread]) %boldMagenta(%logger{10}) %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/cube-trace.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/cube-trace.%d{yyyy-MM-dd_HH}.log.gz</fileNamePattern>
            <!-- keep 30 days'(720 hour) worth of history capped at 5GB total size -->
            <maxHistory>720</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-5GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="ERROR_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/cube-trace-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/cube-trace-error.%d{yyyy-MM-dd_HH}.log.gz
            </fileNamePattern>
            <!-- keep 30 days'(720 hour) worth of history capped at 5GB total size -->
            <maxHistory>720</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-5GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="MONITOR_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/cube-trace-monitor.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/cube-trace-monitor.%d{yyyy-MM-dd_HH}.log.gz
            </fileNamePattern>
            <!-- keep 30 days'(720 hour) worth of history capped at 10GB total size -->
            <maxHistory>720</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-10GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>

    <appender name="DefaultREDMonitor" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/biz-RED-monitor.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/biz-RED-monitor.%d{yyyy-MM-dd_HH}.log.gz
            </fileNamePattern>
            <!-- keep 30 days'(720 hour) worth of history capped at 10GB total size -->
            <maxHistory>1</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-10GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ASYNC_MQ_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/cube-trace-async.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/cube-trace-async.%d{yyyy-MM-dd_HH}.log.gz
            </fileNamePattern>
            <!-- keep 30 days'(720 hour) worth of history capped at 5GB total size -->
            <maxHistory>720</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-5GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="ApmMonitorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APM_LOG_HOME:-logs}/metrics.log</file>
        <append>true</append>
        <immediateFlush>true</immediateFlush>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/apm-metrics.%d{yyyy-MM-dd_HH}.log.gz</fileNamePattern>
            <!-- keep 30 days'(720 hour) worth of history capped at 50GB total size -->
            <maxHistory>720</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-5GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <Pattern>%msg%n</Pattern>
        </encoder>
    </appender>

    <logger name="us.zoom.trace" level="info">
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_LOG"/>
    </logger>

    <logger name="us.zoom.infra" level="info">
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_LOG"/>
    </logger>

    <logger name="us.zoom.mq" level="WARN" additivity="false">
        <appender-ref ref="ASYNC_MQ_LOG"/>
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="us.zoom.monitor" level="TRACE" additivity="false">
        <appender-ref ref="ApmMonitorLog"/>
    </logger>

    <logger name="Monitor" level="TRACE" additivity="false">
        <appender-ref ref="MONITOR_LOG"/>
    </logger>

    <logger name="REDMonitor" level="TRACE" additivity="false">
        <appender-ref ref="DefaultREDMonitor"/>
    </logger>


    <logger name="us.zoom.cloud.secrets" level="TRACE" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="us.zoom.jwt" level="TRACE" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>

    <root level="error">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ERROR_LOG"/>
    </root>
</configuration>