package us.zoom.trace.common.model.opentelemetry;

import com.google.common.hash.BloomFilter;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultScriptExecutor;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import us.zoom.trace.biz.longtimestorage.LongTimeStorageTraceIdCache;
import us.zoom.trace.util.RedisService;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */

public class RedisTemplateTest {

    public static void main(String[] args) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        redisTemplate.afterPropertiesSet();
        redisTemplate.setScriptExecutor(new DefaultScriptExecutor<>(redisTemplate));
        LongTimeStorageTraceIdCache longTimeStorageTraceIdCache = new LongTimeStorageTraceIdCache(new RedisService(redisTemplate));
        BloomFilter<byte[]> bloomFilter = longTimeStorageTraceIdCache.getBloomFilter(TimeUnit.MILLISECONDS.toNanos(1741795200000L));
        System.out.println(bloomFilter);
    }
}
