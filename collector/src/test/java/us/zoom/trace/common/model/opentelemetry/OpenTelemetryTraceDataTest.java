package us.zoom.trace.common.model.opentelemetry;

/**
 * @author: eason.jia
 * @date: 2024/8/6
 */
public class OpenTelemetryTraceDataTest {

    private static String traceData = "{\n" +
            "\t\"resource\": {\n" +
            "\t\t\"attributes\": [{\n" +
            "\t\t\t\"key\": \"container.id\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"44d9ce7a1b645a25c9990df0c37935facc135da551467ecf467dbaa2c622ff6c\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"host.arch\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"amd64\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"host.name\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"cube-config-dev-main-va1-infra-config-internal-api-578f8cbtmg47\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"os.description\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"Linux 5.10.218-208.862.amzn2.x86_64\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"os.type\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"linux\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"process.runtime.description\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"Oracle Corporation Java HotSpot(TM) 64-Bit Server VM 17.0.10+11-LTS-240\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"process.runtime.name\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"Java(TM) SE Runtime Environment\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"process.runtime.version\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"17.0.10+11-LTS-240\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"service.instance.id\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"a11e664f-513e-414c-94bf-e8c4e4b22ab1\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"service.name\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"cube-config-api\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"telemetry.distro.name\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"opentelemetry-java-instrumentation\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"telemetry.distro.version\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"2.6.0.zoom-SNAPSHOT\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"telemetry.sdk.language\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"java\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"telemetry.sdk.name\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"opentelemetry\"\n" +
            "\t\t\t}\n" +
            "\t\t}, {\n" +
            "\t\t\t\"key\": \"telemetry.sdk.version\",\n" +
            "\t\t\t\"value\": {\n" +
            "\t\t\t\t\"stringValue\": \"1.40.0\"\n" +
            "\t\t\t}\n" +
            "\t\t}]\n" +
            "\t},\n" +
            "\t\"scopeSpans\": [{\n" +
            "\t\t\"scope\": {\n" +
            "\t\t\t\"name\": \"io.opentelemetry.undertow-1.4\",\n" +
            "\t\t\t\"version\": \"2.6.0.zoom-SNAPSHOT\",\n" +
            "\t\t\t\"attributes\": []\n" +
            "\t\t},\n" +
            "\t\t\"spans\": [{\n" +
            "\t\t\t\"traceId\": \"dbffc53caad4801613062a39869477c4\",\n" +
            "\t\t\t\"spanId\": \"d144da5f8e92b30c\",\n" +
            "\t\t\t\"name\": \"GET /inner/config/spcConfigs\",\n" +
            "\t\t\t\"kind\": 2,\n" +
            "\t\t\t\"startTimeUnixNano\": \"1722956455180150161\",\n" +
            "\t\t\t\"endTimeUnixNano\": \"1722956455183595799\",\n" +
            "\t\t\t\"attributes\": [{\n" +
            "\t\t\t\t\"key\": \"http.request.method\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"GET\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"http.route\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"/inner/config/spcConfigs\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"server.address\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"cubeconfig-perf-new.zoomdev.us\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"client.address\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"************\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"network.peer.address\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"*************\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"url.path\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"/inner/config/spcConfigs\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"thread.id\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"intValue\": \"73\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"http.response.status_code\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"intValue\": \"304\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"network.protocol.version\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"1.1\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"user_agent.original\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"Java/17.0.10\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"url.scheme\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"https\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"thread.name\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"XNIO-1 I/O-1\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"network.peer.port\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"intValue\": \"34244\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}],\n" +
            "\t\t\t\"events\": [],\n" +
            "\t\t\t\"links\": [],\n" +
            "\t\t\t\"status\": {},\n" +
            "\t\t\t\"flags\": 257\n" +
            "\t\t}, {\n" +
            "\t\t\t\"traceId\": \"bef81e4610d53045eef2835b9896db7e\",\n" +
            "\t\t\t\"spanId\": \"ec9477641e6c1c02\",\n" +
            "\t\t\t\"name\": \"GET /inner/config/spcCoverConfigs\",\n" +
            "\t\t\t\"kind\": 2,\n" +
            "\t\t\t\"startTimeUnixNano\": \"1722956455227244409\",\n" +
            "\t\t\t\"endTimeUnixNano\": \"1722956455230897522\",\n" +
            "\t\t\t\"attributes\": [{\n" +
            "\t\t\t\t\"key\": \"http.request.method\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"GET\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"http.route\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"/inner/config/spcCoverConfigs\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"server.address\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"cubeconfig-perf-new.zoomdev.us\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"client.address\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"************\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"network.peer.address\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"*************\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"url.path\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"/inner/config/spcCoverConfigs\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"thread.id\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"intValue\": \"73\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"http.response.status_code\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"intValue\": \"304\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"network.protocol.version\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"1.1\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"user_agent.original\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"Java/17.0.10\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"url.scheme\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"https\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"thread.name\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"XNIO-1 I/O-1\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"network.peer.port\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"intValue\": \"34244\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}],\n" +
            "\t\t\t\"events\": [],\n" +
            "\t\t\t\"links\": [],\n" +
            "\t\t\t\"status\": {},\n" +
            "\t\t\t\"flags\": 257\n" +
            "\t\t}, {\n" +
            "\t\t\t\"traceId\": \"e53c41944b8dbfa9421aa0a5a96356dc\",\n" +
            "\t\t\t\"spanId\": \"ec7b2b420a2bfe3a\",\n" +
            "\t\t\t\"name\": \"GET /inner/config/unitTag\",\n" +
            "\t\t\t\"kind\": 2,\n" +
            "\t\t\t\"startTimeUnixNano\": \"1722956455245704923\",\n" +
            "\t\t\t\"endTimeUnixNano\": \"1722956455249029907\",\n" +
            "\t\t\t\"attributes\": [{\n" +
            "\t\t\t\t\"key\": \"http.request.method\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"GET\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"http.route\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"/inner/config/unitTag\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"server.address\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"cubeconfig-perf-new.zoomdev.us\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"client.address\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"************\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"network.peer.address\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"*************\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"url.query\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"name=infra_clickhouse\\u0026taskType=sink\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"url.path\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"/inner/config/unitTag\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"thread.id\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"intValue\": \"73\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"http.response.status_code\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"intValue\": \"304\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"network.protocol.version\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"1.1\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"user_agent.original\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"Java/17.0.10\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"url.scheme\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"https\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"thread.name\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"stringValue\": \"XNIO-1 I/O-1\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}, {\n" +
            "\t\t\t\t\"key\": \"network.peer.port\",\n" +
            "\t\t\t\t\"value\": {\n" +
            "\t\t\t\t\t\"intValue\": \"34244\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t}],\n" +
            "\t\t\t\"events\": [],\n" +
            "\t\t\t\"links\": [],\n" +
            "\t\t\t\"status\": {},\n" +
            "\t\t\t\"flags\": 257\n" +
            "\t\t}]\n" +
            "\t}],\n" +
            "\t\"schemaUrl\": \"https://opentelemetry.io/schemas/1.24.0\"\n" +
            "}";

//    public static void main(String[] args) throws JsonProcessingException {
//        OpenTelemetryTraceData otelTraceData = JsonUtils.parse(OpenTelemetryTraceDataTest.traceData, OpenTelemetryTraceData.class);
//        System.out.println(otelTraceData);
//        OtelResource resource = otelTraceData.getResource();
//        List<OtelScopeSpan> scopeSpans = otelTraceData.getScopeSpans();
//        for (OtelScopeSpan scopeSpan : scopeSpans) {
//            OtelScope scope = scopeSpan.getScope();
//            List<OtelSpan> spans = scopeSpan.getSpans();
//            for (OtelSpan otelSpan : spans) {
//                SpanIndex spanDO = SpanBuilder.buildSpan(otelSpan, scope, resource);
//                Map<String, Object> traceIndexRow = ClickHouseModelConvertUtils.toTraceIndexRow(spanDO);
//                System.out.println(traceIndexRow);
//
//                Map<String, Object> traceIndexErrorRow = ClickHouseModelConvertUtils.toTraceIndexErrorRow(spanDO);
//                System.out.println(traceIndexErrorRow);
//
//                Map<String, Object> spanRow = ClickHouseModelConvertUtils.toSpanRow(spanDO);
//                System.out.println(spanRow);
//
//                List<Map<String, Map<String, Object>>> spanAttributeKeyAndValueRow = ClickHouseModelConvertUtils.toSpanAttributeKeyAndValueRow(spanDO);
//                System.out.println(spanAttributeKeyAndValueRow);
//            }
//        }
//    }
}
