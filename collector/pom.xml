<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>us.zoom.trace</groupId>
    <artifactId>collector</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <compiler.plugin.version>3.10.1</compiler.plugin.version>
        <!-- dependency version -->
        <spring-boot.version>3.4.3</spring-boot.version>
        <nws-platform.version>3.3.250206</nws-platform.version>
        <opentelemetry.version>1.40.0</opentelemetry.version>
        <async.mq.version>2.7.9</async.mq.version>
        <globaltracing.core.version>2.1.0-RELEASE</globaltracing.core.version>
        <zoom-threadpool.version>1.0.20230103</zoom-threadpool.version>
        <kafka.version>3.4.0</kafka.version>
        <opentelemetry.version>1.40.0</opentelemetry.version>
        <jackson.version>2.16.1</jackson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- zoom -->
            <dependency>
                <groupId>us.zoom.commons</groupId>
                <artifactId>nws-platform-dependencies</artifactId>
                <version>${nws-platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- zoom -->
        <dependency>
            <groupId>us.zoom.commons</groupId>
            <artifactId>zoom-web-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>us.zoom.mq</groupId>
            <artifactId>asyncmq-client-ha</artifactId>
            <version>${async.mq.version}</version>
        </dependency>
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>cube-config-client</artifactId>
            <version>2.0.17-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>us.zoom.commons</groupId>
            <artifactId>zoom-commons-apm-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>us.zoom.commons</groupId>
            <artifactId>zoom-commons-apm</artifactId>
        </dependency>
        <dependency>
            <groupId>us.zoom.globaltracing.server</groupId>
            <artifactId>websrv-globaltracing-server-core</artifactId>
            <version>${globaltracing.core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.okta.jwt</groupId>
                    <artifactId>okta-jwt-verifier</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.okta.jwt</groupId>
                    <artifactId>okta-jwt-verifier-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>log4j-over-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jcl-over-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis.spring.boot</groupId>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>tk.mybatis</groupId>
                    <artifactId>mapper-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.zaxxer</groupId>
                    <artifactId>HikariCP</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.minidev</groupId>
                    <artifactId>json-smart</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>tk.mybatis</groupId>
                    <artifactId>mapper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>us.zoom.async</groupId>
                    <artifactId>async-dashboard-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>us.zoom.async</groupId>
                    <artifactId>async-dashboard-datasource-elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.amazonaws</groupId>
                    <artifactId>aws-java-sdk-elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>us.zoom.async</groupId>
                    <artifactId>async-dashboard-datasource-cube</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- spring -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>

        <!-- csms && jwt-->
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>csms-sdk-bridge-common</artifactId>
            <version>0.4.10</version>
        </dependency>
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>cloud-secrets-management-service-sdk</artifactId>
            <version>0.6.16</version>
        </dependency>
        <dependency>
            <groupId>us.zoom</groupId>
            <artifactId>csms-sdk-api</artifactId>
            <version>0.4.10</version>
        </dependency>
        <dependency>
            <groupId>us.zoom.commons</groupId>
            <artifactId>zoom-commons-jwt</artifactId>
            <version>2.0.15</version>
        </dependency>

        <!-- adapter for asyncmq-client start-->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>${kafka.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>snappy-java</artifactId>
                    <groupId>org.xerial.snappy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>us.zoom.commons</groupId>
            <artifactId>zoom-threadpool</artifactId>
            <version>${zoom-threadpool.version}</version>
        </dependency>

        <!-- log -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <!-- utils -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.1.3-jre</version>
        </dependency>

        <!-- jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry.proto</groupId>
            <artifactId>opentelemetry-proto</artifactId>
            <version>1.4.0-alpha</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>4.28.3</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>4.28.3</version>
        </dependency>

        <!-- security issues-->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-common</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-buffer</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-resolver</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport-native-unix-common</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport-classes-epoll</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport-classes-kqueue</artifactId>
            <version>4.1.118.Final</version>
        </dependency>

        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>2.5.2</version>
        </dependency>
        <dependency>
            <groupId>com.tdunning</groupId>
            <artifactId>t-digest</artifactId>
            <version>3.3</version>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
            <version>1.14.5</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
            <version>6.3.8</version>
        </dependency>
        <dependency>
            <groupId>com.hubspot.jackson</groupId>
            <artifactId>jackson-datatype-protobuf</artifactId>
            <version>0.9.18</version>
        </dependency>
        <dependency>
            <groupId>org.lz4</groupId>
            <artifactId>lz4-java</artifactId>
            <version>1.8.0</version> <!-- 最新版可在 Maven Central 查 -->
        </dependency>
    </dependencies>

    <build>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.7.1</version>
            </extension>
        </extensions>
        <finalName>cube-trace-collector</finalName>
        <plugins>
            <plugin>
                <groupId>org.xolstice.maven.plugins</groupId>
                <artifactId>protobuf-maven-plugin</artifactId>
                <version>0.6.1</version>
                <configuration>
                    <protoSourceRoot>${project.basedir}/src/main/resources/proto</protoSourceRoot>
                    <protocArtifact>com.google.protobuf:protoc:4.28.3:exe:${os.detected.classifier}</protocArtifact>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${compiler.plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <executable>true</executable>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>